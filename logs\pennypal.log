2025-08-03T09:26:45.985+05:30  INFO 15664 --- [pennypal-api] [main] c.p.fintech.api.PennyPalApplication      : Starting PennyPalApplication using Java 17.0.14 with PID 15664 (C:\Users\<USER>\PP_Aug02\pennypal-backend-monorepo\pennypal-api\target\classes started by Ashwin Work2 in C:\Users\<USER>\PP_Aug02)
2025-08-03T09:26:45.998+05:30 DEBUG 15664 --- [pennypal-api] [main] c.p.fintech.api.PennyPalApplication      : Running with Spring Boot v3.2.2, Spring v6.1.3
2025-08-03T09:26:46.001+05:30  INFO 15664 --- [pennypal-api] [main] c.p.fintech.api.PennyPalApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-03T09:26:47.730+05:30  INFO 15664 --- [pennypal-api] [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-03T09:26:47.733+05:30  INFO 15664 --- [pennypal-api] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03T09:26:48.119+05:30  INFO 15664 --- [pennypal-api] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 366 ms. Found 50 JPA repository interfaces.
