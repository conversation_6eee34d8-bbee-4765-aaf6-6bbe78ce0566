package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.service.SseService;
import com.pennypal.fintech.service.JWTService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Map;

@RestController
@RequestMapping("/api/v1/sse")
@Tag(name = "Server-Sent Events", description = "Real-time communication endpoints")
@Slf4j
public class SseController {

    @Autowired
    private SseService sseService;

    @Autowired
    private JWTService jwtService;

    @GetMapping(value = "/connect", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "Establish SSE connection", description = "Creates a Server-Sent Events connection for real-time updates")
    public SseEmitter connect(
            @Parameter(description = "JWT token for authentication", required = true)
            @RequestHeader("Authorization") String authHeader) {

        log.info("SSE connection request received");
        try {
            // Extract token from Authorization header
            String token = authHeader.replace("Bearer ", "");
            log.info("token: {}", token);
            
            // Get user ID from token
            Integer userId = jwtService.extractUserId(token);
            log.info("userId: {}", userId);
            
            if (userId == null) {
                log.error("Invalid token provided for SSE connection");
                throw new RuntimeException("Invalid authentication token");
            }
            
            log.info("Creating SSE connection for user: {}", userId);
            return sseService.createConnection(userId);
            
        } catch (Exception e) {
            log.error("Failed to create SSE connection: {}", e.getMessage());
            throw new RuntimeException("Failed to establish SSE connection: " + e.getMessage());
        }
    }

    @GetMapping("/status")
    @Operation(summary = "Get SSE connection status", description = "Returns information about active SSE connections")
    public ResponseEntity<Map<String, Object>> getConnectionStatus(
            @Parameter(description = "JWT token for authentication", required = true)
            @RequestHeader("Authorization") String authHeader) {
        
        try {
            // Extract token from Authorization header
            String token = authHeader.replace("Bearer ", "");
            
            // Get user ID from token
            Integer userId = jwtService.extractUserId(token);
            
            if (userId == null) {
                return ResponseEntity.badRequest().body(Map.of("error", "Invalid authentication token"));
            }
            
            int userConnections = sseService.getConnectionCount(userId);
            int totalConnections = sseService.getTotalConnectionCount();
            boolean hasActiveConnection = sseService.hasActiveConnections(userId);
            
            Map<String, Object> status = Map.of(
                "userId", userId,
                "hasActiveConnection", hasActiveConnection,
                "userConnectionCount", userConnections,
                "totalConnectionCount", totalConnections
            );
            
            return ResponseEntity.ok(status);
            
        } catch (Exception e) {
            log.error("Failed to get SSE connection status: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("error", "Failed to get connection status"));
        }
    }

    @PostMapping("/test-event")
    @Operation(summary = "Send test SSE event", description = "Sends a test event to verify SSE connection (for development/testing)")
    public ResponseEntity<Map<String, String>> sendTestEvent(
            @Parameter(description = "JWT token for authentication", required = true)
            @RequestHeader("Authorization") String authHeader,
            @Parameter(description = "Test message to send")
            @RequestBody(required = false) Map<String, String> payload) {

        try {
            // Extract token from Authorization header
            String token = authHeader.replace("Bearer ", "");

            // Get user ID from token
            Integer userId = jwtService.extractUserId(token);

            if (userId == null) {
                return ResponseEntity.badRequest().body(Map.of("error", "Invalid authentication token"));
            }

            String message = payload != null && payload.containsKey("message")
                ? payload.get("message")
                : "Test event from PennyPal SSE";

            sseService.sendEventToUser(userId, "test", Map.of(
                "message", message,
                "timestamp", System.currentTimeMillis(),
                "userId", userId
            ));

            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Test event sent to user " + userId
            ));

        } catch (Exception e) {
            log.error("Failed to send test SSE event: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("error", "Failed to send test event"));
        }
    }

    @PostMapping("/test-cache-invalidation")
    @Operation(summary = "Trigger cache invalidation test", description = "Manually triggers recurring transaction cache invalidation for testing")
    public ResponseEntity<Map<String, String>> testCacheInvalidation(
            @Parameter(description = "JWT token for authentication", required = true)
            @RequestHeader("Authorization") String authHeader) {

        try {
            // Extract token from Authorization header
            String token = authHeader.replace("Bearer ", "");

            // Get user ID from token
            Integer userId = jwtService.extractUserId(token);

            if (userId == null) {
                return ResponseEntity.badRequest().body(Map.of("error", "Invalid authentication token"));
            }

            // Send cache invalidation event
            sseService.sendRecurringTransactionCacheInvalidation(userId);

            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Cache invalidation event sent to user " + userId
            ));

        } catch (Exception e) {
            log.error("Failed to send cache invalidation test: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("error", "Failed to send cache invalidation test"));
        }
    }

    @PostMapping("/test-account-sync-invalidation")
    @Operation(summary = "Trigger account sync cache invalidation test", description = "Manually triggers account sync cache invalidation for testing")
    public ResponseEntity<Map<String, String>> testAccountSyncCacheInvalidation(
            @Parameter(description = "JWT token for authentication", required = true)
            @RequestHeader("Authorization") String authHeader) {

        try {
            // Extract token from Authorization header
            String token = authHeader.replace("Bearer ", "");

            // Get user ID from token
            Integer userId = jwtService.extractUserId(token);

            if (userId == null) {
                return ResponseEntity.badRequest().body(Map.of("error", "Invalid authentication token"));
            }

            // Send account sync cache invalidation event
            sseService.sendAccountSyncCacheInvalidation(userId);

            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Account sync cache invalidation event sent to user " + userId
            ));

        } catch (Exception e) {
            log.error("Failed to send account sync cache invalidation test: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("error", "Failed to send account sync cache invalidation test"));
        }
    }

    @PostMapping("/test-recurring-rules-notification-invalidation")
    @Operation(summary = "Trigger recurring transaction rules notification cache invalidation test", description = "Manually triggers recurring transaction rules notification cache invalidation for testing")
    public ResponseEntity<Map<String, String>> testRecurringRulesNotificationInvalidation(
            @Parameter(description = "JWT token for authentication", required = true)
            @RequestHeader("Authorization") String authHeader) {

        try {
            // Extract token from Authorization header
            String token = authHeader.replace("Bearer ", "");

            // Get user ID from token
            Integer userId = jwtService.extractUserId(token);

            if (userId == null) {
                return ResponseEntity.badRequest().body(Map.of("error", "Invalid authentication token"));
            }

            // Send recurring transaction rules notification cache invalidation event
            sseService.sendRecurringTransactionRulesNotificationInvalidation(userId);

            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Recurring transaction rules notification cache invalidation event sent to user " + userId
            ));

        } catch (Exception e) {
            log.error("Failed to send recurring transaction rules notification cache invalidation test: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("error", "Failed to send recurring transaction rules notification cache invalidation test"));
        }
    }

    @PostMapping("/test-budget-rules-notification-invalidation")
    @Operation(summary = "Trigger budget rules notification cache invalidation test", description = "Manually triggers budget rules notification cache invalidation for testing")
    public ResponseEntity<Map<String, String>> testBudgetRulesNotificationInvalidation(
            @Parameter(description = "JWT token for authentication", required = true)
            @RequestHeader("Authorization") String authHeader) {

        try {
            // Extract token from Authorization header
            String token = authHeader.replace("Bearer ", "");

            // Get user ID from token
            Integer userId = jwtService.extractUserId(token);

            if (userId == null) {
                return ResponseEntity.badRequest().body(Map.of("error", "Invalid authentication token"));
            }

            // Send budget rules notification cache invalidation event
            sseService.sendBudgetRulesNotificationInvalidation(userId);

            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Budget rules notification cache invalidation event sent to user " + userId
            ));

        } catch (Exception e) {
            log.error("Failed to send budget rules notification cache invalidation test: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("error", "Failed to send budget rules notification cache invalidation test"));
        }
    }
}