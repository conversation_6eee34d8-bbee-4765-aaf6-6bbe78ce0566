package com.pennypal.fintech.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.pennypal.fintech.dto.UserNotificationRulesDto;
import com.pennypal.fintech.service.UserNotificationRulesService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

@RestController
@Slf4j
@RequestMapping("/api/v1/user-notification-rules")
@Tag(name = "User Notification Rules", description = "APIs for managing user-specific notification rules and preferences")
public class UserNotificationRulesController {

    @Autowired
    private final UserNotificationRulesService userNotificationRulesService;

    public UserNotificationRulesController(UserNotificationRulesService userNotificationRulesService) {
        this.userNotificationRulesService = userNotificationRulesService;
    }

    // To get all notification rules for a user
    @Operation(summary = "Get all notification rules for a user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved notification rules"),
        @ApiResponse(responseCode = "400", description = "Invalid user ID"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/user/{userId}")
    public ResponseEntity<List<UserNotificationRulesDto>> getUserRules(
        @Parameter(description = "ID of the user to get rules for", required = true)
        @PathVariable Integer userId) {
        log.info("Inside getUserRules method of UserNotificationRulesController");
        log.info("User ID: " + userId);
        List<UserNotificationRulesDto> rules = userNotificationRulesService.getNotificationRulesByUser(userId);
        return ResponseEntity.ok(rules);
    }

    // To edit a user notification rule
    @Operation(summary = "Edit a user notification rule")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully edited notification rule"),
        @ApiResponse(responseCode = "400", description = "Invalid rule ID"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PutMapping("/edit")
    public ResponseEntity<UserNotificationRulesDto> editUserRule(
        @Parameter(description = "User notification rule to be updated", required = true)
        @RequestBody UserNotificationRulesDto ruleDto) {
        log.info("Inside editUserRule method of UserNotificationRulesController");
        log.info("Rule: " + ruleDto);
        try {
            userNotificationRulesService.editRule(ruleDto);
            return ResponseEntity.ok(ruleDto);
        } catch (Exception e) {
            log.error("Error editing rule: {}" + e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }
}