package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.RecurringContributionDto;
import com.pennypal.fintech.exception.InsufficientFundsException;
import com.pennypal.fintech.exception.InvalidRequestException;
import com.pennypal.fintech.exception.ResourceNotFoundException;
import com.pennypal.fintech.service.RecurringContributionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

@RestController
@RequestMapping("/api/recurring-contributions")
@Tag(name = "Recurring Contributions", description = "APIs for managing recurring contributions to financial goals")
public class RecurringContributionController {
    private static final Logger logger = LoggerFactory.getLogger(RecurringContributionController.class);

    private final RecurringContributionService recurringContributionService;

    @Autowired
    public RecurringContributionController(RecurringContributionService recurringContributionService) {
        this.recurringContributionService = recurringContributionService;
    }

    @PostMapping
    @Operation(summary = "Create recurring contribution",
               description = "Sets up a new recurring contribution to a financial goal")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "201",
            description = "Recurring contribution created successfully",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request data",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User or goal not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<RecurringContributionDto.RecurringContributionResponse> createRecurringContribution(
            @Parameter(description = "ID of the user", required = true)
            @RequestHeader("User-Id") Integer userId,
            @Parameter(description = "Recurring contribution creation request", required = true)
            @RequestBody RecurringContributionDto.CreateRecurringContributionRequest request) {
        try {
            RecurringContributionDto.RecurringContributionResponse response = 
                    recurringContributionService.setupRecurringContribution(userId, request);
            return new ResponseEntity<>(response, HttpStatus.CREATED);
        } catch (ResourceNotFoundException | InvalidRequestException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error creating recurring contribution: " + e.getMessage(), e);
            throw new RuntimeException("Error creating recurring contribution. Please try again later.", e);
        }
    }

    @Operation(summary = "Get recurring contributions for a goal",
               description = "Retrieves all recurring contributions for a specific financial goal")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved recurring contributions",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User or goal not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/goal/{goalId}")
    public ResponseEntity<List<RecurringContributionDto.RecurringContributionResponse>> getRecurringContributionsForGoal(
            @Parameter(description = "ID of the user", required = true)
            @RequestHeader("User-Id") Integer userId,
            @Parameter(description = "ID of the goal to retrieve recurring contributions for", required = true)
            @PathVariable Integer goalId) {
        try {
            List<RecurringContributionDto.RecurringContributionResponse> responses = 
                    recurringContributionService.getRecurringContributionsForGoal(userId, goalId);
            return ResponseEntity.ok(responses);
        } catch (ResourceNotFoundException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error retrieving recurring contributions: " + e.getMessage(), e);
            throw new RuntimeException("Error retrieving recurring contributions. Please try again later.", e);
        }
    }

    @Operation(summary = "Get all recurring contributions for a user",
               description = "Retrieves all recurring contributions for a specific user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved all recurring contributions",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping
    public ResponseEntity<List<RecurringContributionDto.RecurringContributionResponse>> getAllRecurringContributions(
            @Parameter(description = "ID of the user", required = true)
            @RequestHeader("User-Id") Integer userId) {
        try {
            List<RecurringContributionDto.RecurringContributionResponse> responses = 
                    recurringContributionService.getAllRecurringContributions(userId);
            return ResponseEntity.ok(responses);
        } catch (ResourceNotFoundException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error retrieving all recurring contributions: " + e.getMessage(), e);
            throw new RuntimeException("Error retrieving all recurring contributions. Please try again later.", e);
        }
    }

    @Operation(summary = "Update recurring contribution",
               description = "Updates an existing recurring contribution")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Recurring contribution updated successfully",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request data",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User or contribution not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PutMapping("/{contributionId}")
    public ResponseEntity<RecurringContributionDto.RecurringContributionResponse> updateRecurringContribution(
            @Parameter(description = "ID of the user", required = true)
            @RequestHeader("User-Id") Integer userId,
            @Parameter(description = "ID of the recurring contribution to update", required = true)
            @PathVariable Integer contributionId,
            @Parameter(description = "Recurring contribution update request", required = true)
            @RequestBody RecurringContributionDto.UpdateRecurringContributionRequest request) {
        try {
            RecurringContributionDto.RecurringContributionResponse response = 
                    recurringContributionService.updateRecurringContribution(userId, contributionId, request);
            return ResponseEntity.ok(response);
        } catch (ResourceNotFoundException | InvalidRequestException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error updating recurring contribution: " + e.getMessage(), e);
            throw new RuntimeException("Error updating recurring contribution. Please try again later.", e);
        }
    }

    @Operation(summary = "Delete recurring contribution",
               description = "Deletes an existing recurring contribution")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "204",
            description = "Recurring contribution deleted successfully",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User or contribution not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @DeleteMapping("/{contributionId}")
    public ResponseEntity<Void> deleteRecurringContribution(
            @Parameter(description = "ID of the user", required = true)
            @RequestHeader("User-Id") Integer userId,
            @Parameter(description = "ID of the recurring contribution to delete", required = true)
            @PathVariable Integer contributionId) {
        try {
            recurringContributionService.deleteRecurringContribution(userId, contributionId);
            return ResponseEntity.noContent().build();
        } catch (ResourceNotFoundException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error deleting recurring contribution: " + e.getMessage(), e);
            throw new RuntimeException("Error deleting recurring contribution. Please try again later.", e);
        }
    }

    @Operation(summary = "Manually trigger a recurring contribution",
               description = "Manually triggers a recurring contribution to be processed immediately")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Contribution triggered successfully",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User or contribution not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PostMapping("/{contributionId}/trigger")
    public ResponseEntity<RecurringContributionDto.RecurringContributionResponse> triggerContribution(
            @Parameter(description = "ID of the user", required = true)
            @RequestHeader("User-Id") Integer userId,
            @Parameter(description = "ID of the recurring contribution to trigger", required = true)
            @PathVariable Integer contributionId) {
        try {
            RecurringContributionDto.RecurringContributionResponse response = 
                    recurringContributionService.manuallyTriggerContribution(userId, contributionId);
            return ResponseEntity.ok(response);
        } catch (ResourceNotFoundException e) {
            throw e;
        } catch (InsufficientFundsException e) {
            return ResponseEntity.status(HttpStatus.PAYMENT_REQUIRED)
                    .body(null); // Consider returning more details about insufficient funds
        } catch (Exception e) {
            logger.error("Error triggering contribution: " + e.getMessage(), e);
            throw new RuntimeException("Error triggering contribution. Please try again later.", e);
        }
    }
}