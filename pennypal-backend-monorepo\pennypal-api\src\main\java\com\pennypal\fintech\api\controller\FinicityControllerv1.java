package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.FinicityConnectRequestDto;
import com.pennypal.fintech.dto.FinicityCustomerRequestDto;
import com.pennypal.fintech.service.FinicityServicev1;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/api/v1/finicity")
@Tag(name = "Finicity Integration v1", description = "Version 1 APIs for Finicity financial data aggregation service")
public class FinicityControllerv1 {

    @Autowired
    private FinicityServicev1 finicityServicev1;

    @PostMapping("/create-customer")
    @Operation(summary = "Create Finicity customer",
               description = "Creates a new customer in the Finicity platform")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully created customer",
            content = @Content(
                mediaType = "text/plain",
                schema = @Schema(description = "Customer ID")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request data",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<String> createCustomer(
            @Parameter(description = "Customer creation request data", required = true)
            @RequestBody FinicityCustomerRequestDto request) {
        System.out.println("Creating customer: " + request);
        String customerId = finicityServicev1.createCustomer(request);
        System.out.println("Customer ID: " + customerId);
        return ResponseEntity.ok(customerId);
    }

    @PostMapping("/connect-link")
    @Operation(summary = "Generate Finicity connect link",
               description = "Generates a connect link for users to link their financial accounts through Finicity")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully generated connect link",
            content = @Content(
                mediaType = "text/plain",
                schema = @Schema(description = "Connect link URL")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request data",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<String> getConnectLink(
            @Parameter(description = "Connect request data containing user ID", required = true)
            @RequestBody FinicityConnectRequestDto request) {
        System.out.println("Generating connect link for customer: " + request.getUserId());
        String link = finicityServicev1.generateConnectLink(request.getUserId());
        System.out.println("Connect link: " + link);
        return ResponseEntity.ok(link);
    }

    @Operation(summary = "Get customer accounts from Finicity",
               description = "Retrieves all financial accounts for a specific customer from Finicity")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved customer accounts",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Customer not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PostMapping("/accounts")
    public ResponseEntity<?> getAccounts(
        @Parameter(description = "Request data containing user ID", required = true)
        @RequestBody FinicityConnectRequestDto request) {
        System.out.println("Getting accounts for customer: " + request.getUserId());
        return ResponseEntity.ok(finicityServicev1.getCustomerAccounts(request.getUserId()));
    }

    @Operation(summary = "Get customer transactions from Finicity",
               description = "Retrieves financial transactions for a specific customer account from Finicity within a date range")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved customer transactions",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Customer or account not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid date range",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PostMapping("/transactions/{userId}")
    public ResponseEntity<?> getTransactions(
        @Parameter(description = "ID of the user to get transactions for", required = true)
        @PathVariable Integer userId) {
        System.out.println("Getting transactions for userId: " + userId);
        return ResponseEntity.ok(finicityServicev1.getTransactions(userId));
    }

    @Operation(summary = "Sync customer transactions from Finicity",
               description = "Synchronizes financial transactions for a specific customer account from Finicity within a date range")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully synced customer transactions",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Customer or account not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid date range",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PostMapping("/sync-transactions/{userId}")
    public ResponseEntity<?> syncTransactions(
        @Parameter(description = "ID of the user to sync transactions for", required = true)
        @PathVariable Integer userId) {
        System.out.println("Syncing transactions for userId: " + userId);
        return ResponseEntity.ok(finicityServicev1.syncTransactions(userId));
    }
}