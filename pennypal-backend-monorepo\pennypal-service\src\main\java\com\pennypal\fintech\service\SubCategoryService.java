package com.pennypal.fintech.service;

import com.pennypal.fintech.dto.SubCategoryDto;
import com.pennypal.fintech.entity.SubCategory;
import com.pennypal.fintech.repository.CategoryRepository;
import com.pennypal.fintech.repository.SubCategoryRepository;
import com.pennypal.fintech.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class SubCategoryService {

    
    @Autowired
    private SubCategoryRepository subCategoryRepository;

    @Autowired
    private CategoryRepository categoryRepository;

    @Autowired
    private UserRepository userRepository;

    public SubCategoryService(SubCategoryRepository subCategoryRepository,
                              CategoryRepository categoryRepository,
                              UserRepository userRepository) {
        this.subCategoryRepository = subCategoryRepository;
        this.categoryRepository = categoryRepository;
        this.userRepository = userRepository;
    }

    // Save or update a SubCategory
    @CacheEvict(value = "subCategoryCache",
                allEntries = true)
    @Transactional
    public SubCategory saveSubCategory(SubCategoryDto subCategoryDto) {
        SubCategory subCategory = new SubCategory();
        subCategory.setIconKey(subCategoryDto.getIconKey()); // Set the iconKey from the DTO
        // Optionally, handle category setting if necessary (e.g., through category repository)
        return subCategoryRepository.save(subCategory);
    }

    // Get all SubCategories
    @Cacheable(value = "subCategoryCache",
               key = "#root.methodName")
    public List<SubCategory> getAllSubCategories() {
        return subCategoryRepository.findAll();
    }

    // Get SubCategory by ID
    @Cacheable(value = "subCategoryCache",
               key = "#root.methodName + '_' + #id")
    public Optional<SubCategory> getSubCategoryById(int id) {
        return subCategoryRepository.findById(id);
    }

    // Delete SubCategory by ID
    @CacheEvict(value = "subCategoryCache",
                allEntries = true)
    public void deleteSubCategory(int id) {
        subCategoryRepository.deleteById(id);
    }

    // Get SubCategories by categoryId
    @Cacheable(value = "subCategoryCache",
               key = "#root.methodName + '_' + #categoryId")
    public List<SubCategory> getSubCategoriesByCategoryId(int categoryId) {
        return subCategoryRepository.findByCategoryId(categoryId);
    }
	
	@Cacheable(value = "subCategoryCache",
               key = "#root.methodName + '_' + #userId")
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getDistinctSubCategoriesByUserId(Integer userId) {
        log.info("Inside getDistinctSubCategoriesByUserId in SubCategoryService with userId: {}", userId);

        if (userId == null) {
            log.error("User ID cannot be null");
            throw new IllegalArgumentException("User ID cannot be null");
        }
        
        if (!userRepository.existsById(userId)) {
            log.error("User not found with ID: {}", userId);
            throw new RuntimeException("User not found");
        }

        List<Object[]> results = subCategoryRepository.findDistinctSubCategories(userId);
        log.info("Raw data size: {}", results.size());
        
        return results.stream()
            .map(result -> {
                Map<String, Object> map = new HashMap<>();
                map.put("subCategoryId", result[0]);
                map.put("subCategoryName", result[3] + " - " + result[1]);
                map.put("categoryId", result[2]);
                map.put("type", result[4]);
                return map;
            })
            .collect(Collectors.toList());
    }

    @Cacheable(value = "subCategoryCache",
               key = "#root.methodName")
    public List<SubCategoryDto> getAllSubCategoriesDto() {
        return subCategoryRepository.findAll().stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    private SubCategoryDto convertToDto(SubCategory entity) {
        SubCategoryDto dto = new SubCategoryDto();
        dto.setId(entity.getId());
        dto.setSubCategory(entity.getSubCategory());
        dto.setIconKey(entity.getIconKey() != null ? entity.getIconKey() : "FaMiscellaneous");
        
        if (entity.getCategory() != null) {
            dto.setCategoryId(entity.getCategory().getId());
        }
        
        return dto;
    }

    @CacheEvict(value = "subCategoryCache",
                allEntries = true)
    public void saveSubCategoryIconBlob(int subCategoryId, MultipartFile file) throws Exception {
        Optional<SubCategory> optional = subCategoryRepository.findById(subCategoryId);
        if (optional.isPresent()) {
            SubCategory subCategory = optional.get();
            subCategory.setIconBlob(file.getBytes());  // Store as BLOB
            subCategory.setUpdateDatetime(new Date()); // Optional: update timestamp
            subCategoryRepository.save(subCategory);
        } else {
            throw new RuntimeException("SubCategory not found with ID: " + subCategoryId);
        }
    }

    @Cacheable(value = "subCategoryCache",
                key = "#root.methodName")
    public List<SubCategory> findAllWithIcons() {
        return subCategoryRepository.findAllWithIcons();
    }
}