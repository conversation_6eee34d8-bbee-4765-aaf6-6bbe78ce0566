package com.pennypal.fintech.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pennypal.fintech.entity.AiQueryLogs;
import com.pennypal.fintech.repository.AiQueryLogsRepository;

import java.util.ArrayList;
import java.util.Deque;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Service
public class ChatbotService {

    @Autowired
    private AiQueryLogsRepository aiQueryLogsRepository;

    private final Map<Integer, Deque<String>> userQueryHistory = new HashMap<>();
    private static final int MAX_HISTORY_SIZE = 5;

    public void addQuery(int userId, String query) {
        userQueryHistory.putIfAbsent(userId, new LinkedList<>());
        Deque<String> deque = userQueryHistory.get(userId);

        if (deque.size() >= MAX_HISTORY_SIZE) {
            deque.removeFirst(); // Remove oldest query
        }

        deque.addLast(query); // Add newest query to the end
        System.out.println("Deque: " + deque + " for user: " + userId);
    }

    public List<String> getPreviousQueries(int userId) {
        return new ArrayList<>(userQueryHistory.getOrDefault(userId, new LinkedList<>()));
    }

    @CacheEvict(value = "chatbotCache",
                key = "'getHistory' + '_' + #request.get('user_id')")
    public Map<String, Object> addHistory(Map<String, Object> request, ResponseEntity<String> response) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> responseBody = mapper.readValue(
                response.getBody(), new TypeReference<Map<String, Object>>() {}
            );

            AiQueryLogs log = new AiQueryLogs();
            log.setUserId(Integer.parseInt((String) request.get("user_id")));
            log.setUserQuery((String) request.get("user_query"));
            log.setSqlQuery((String) responseBody.get("sql_query"));
            log.setResult((String) responseBody.get("result").toString());
            log.setResponse((String) responseBody.get("response"));
            log.setResponseCode(response.getStatusCode().value());

            AiQueryLogs savedLog = aiQueryLogsRepository.save(log);
            Integer chatId = savedLog.getId();
            Boolean isSaved = savedLog.getIsSaved();
            // Add to response
            responseBody.put("chat_id", chatId);
            responseBody.put("is_saved", isSaved);
            return responseBody;
            
        } catch (Exception e) {
            e.printStackTrace();
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to process response: " + e.getMessage());
            return errorResponse;
        }
    }

    @Cacheable(value = "chatbotCache",
               key = "#root.methodName + '_' + #userId",
               unless = "#result == null")
    public List<AiQueryLogs> getHistory(Integer userId) {
        System.out.println("Inside getHistory in ChatbotService");
        System.out.println("User ID: " + userId);
        List<AiQueryLogs> history = aiQueryLogsRepository.findByUserId(userId);
        System.out.println("History size: " + history.size());
        System.out.println("History: " + history);
        return history;
    }
}