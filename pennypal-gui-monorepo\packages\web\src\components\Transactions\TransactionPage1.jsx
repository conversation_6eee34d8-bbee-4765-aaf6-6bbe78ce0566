import React, { useEffect, useRef,useState ,useCallback} from 'react';
import { useSelector, useDispatch ,} from 'react-redux';
import Calendar from 'react-calendar';
import ErrorBoundary from './ErrorBoundary';
import CalendarView from './CalendarView';
import './TransactionPage.css';
import PaymentLoader from '../load/PaymentLoader';
import { axiosInstance } from '../../../../logic/api/axiosConfig';
import CloseIcon from '@mui/icons-material/Close';
import {
  AddCircleOutline,
  VisibilityOff,
  FilterList,
  Upload,
  Receipt,
  Download,
  Palette,
  TextFormat,
  AccountBalance
} from '@mui/icons-material';
import TransactionAIChat from '../AI/TransactionAIChat';
import ReceiptModal from './ReceiptModal';
import SubCategoryIcon from './SubCategoryIcon';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import  PictureAsPdf from '@mui/icons-material/PictureAsPdf';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import NotesIcon from '@mui/icons-material/Notes';
import Visibility from '@mui/icons-material/Visibility';
import SaveIcon from '@mui/icons-material/Save';
import  AttachMoneyIcon  from '@mui/icons-material/AttachMoney';
import  AccountBalanceIcon  from '@mui/icons-material/AccountBalance';
import  CategoryIcon  from '@mui/icons-material/Category';
import  VisibilityOffIcon  from '@mui/icons-material/VisibilityOff';
import SplitTransactionPopup from './SplitTransactionPopup';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import ReceiptIcon from '@mui/icons-material/Receipt';
import ReceiptLongIcon from '@mui/icons-material/ReceiptLong';
import DeleteIcon from '@mui/icons-material/Delete';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
 import ReceiptHandler from './ReceiptHandler';
 import Reconcile from '../Reconcile/Reconciliation';
import RefreshIcon from '@mui/icons-material/Refresh';
import CategoryChartModal from './CategoryChartModal';
import {
  Dialog, DialogActions, DialogContent, DialogTitle, Button, IconButton, InputAdornment,FormControl,InputLabel,
  LinearProgress, CircularProgress, TextField, Table, TableHead,Tooltip,  Select,Grid,Paper,ListSubheader ,
  TableBody, TableRow, TableCell, Tabs, Tab, MenuItem, Radio,Box,Typography,FormControlLabel,Checkbox
} from '@mui/material';
import { 
  FaMoneyBillWave, 
  FaArrowDown, 
  FaArrowUp, 
  FaDollarSign, 
  FaTrophy, 
  FaExclamationTriangle 
} from 'react-icons/fa';
import receipt from '../../assets/receipt.png'
import split from '../../assets/split 1.png'
import BankIcon from '../Accounts/BankIcon';
import { logEvent } from '../../utils/EventLogger';
import { getCurrentUserId } from '../../utils/AuthUtil';
// Theme configurations
const THEMES = {
  default: {
    name: 'Lime Fresh',
    primary: '#8bc34a',
    primaryHover: '#7cb342',
    secondary: '#81c784',
    accent1: 'from-lime-400 to-green-400',
    accent2: 'from-blue-400 to-indigo-400',
    accent3: 'from-purple-400 to-pink-400',
    accent4: 'from-yellow-400 to-orange-400',
    bg: 'bg-gradient-to-br from-lime-50 via-white to-lime-100',
    cardBg: 'bg-white',
    cardHover: 'hover:bg-lime-50',
    headerBg: 'bg-gradient-to-r from-lime-100 to-green-100',
  },
  ocean: {
    name: 'Ocean Breeze',
    primary: '#2196f3',
    primaryHover: '#1976d2',
    secondary: '#64b5f6',
    accent1: 'from-blue-400 to-cyan-400',
    accent2: 'from-teal-400 to-blue-400',
    accent3: 'from-indigo-400 to-blue-400',
    accent4: 'from-cyan-400 to-teal-400',
    bg: 'bg-gradient-to-br from-blue-50 via-white to-cyan-100',
    cardBg: 'bg-white',
    cardHover: 'hover:bg-blue-50',
    headerBg: 'bg-gradient-to-r from-blue-100 to-cyan-100',
  },
  sunset: {
    name: 'Sunset Glow',
    primary: '#ff6b35',
    primaryHover: '#e55100',
    secondary: '#ffab40',
    accent1: 'from-orange-400 to-red-400',
    accent2: 'from-pink-400 to-orange-400',
    accent3: 'from-yellow-400 to-orange-400',
    accent4: 'from-red-400 to-pink-400',
    bg: 'bg-gradient-to-br from-orange-50 via-white to-red-100',
    cardBg: 'bg-white',
    cardHover: 'hover:bg-orange-50',
    headerBg: 'bg-gradient-to-r from-orange-100 to-red-100',
  },
  purple: {
    name: 'Purple Dream',
    primary: '#9c27b0',
    primaryHover: '#7b1fa2',
    secondary: '#ba68c8',
    accent1: 'from-purple-400 to-pink-400',
    accent2: 'from-indigo-400 to-purple-400',
    accent3: 'from-pink-400 to-purple-400',
    accent4: 'from-violet-400 to-purple-400',
    bg: 'bg-gradient-to-br from-purple-50 via-white to-pink-100',
    cardBg: 'bg-white',
    cardHover: 'hover:bg-purple-50',
    headerBg: 'bg-gradient-to-r from-purple-100 to-pink-100',
  },
  emerald: {
    name: 'Emerald Forest',
    primary: '#009688',
    primaryHover: '#00695c',
    secondary: '#4db6ac',
    accent1: 'from-emerald-400 to-teal-400',
    accent2: 'from-green-400 to-emerald-400',
    accent3: 'from-teal-400 to-cyan-400',
    accent4: 'from-lime-400 to-green-400',
    bg: 'bg-gradient-to-br from-emerald-50 via-white to-teal-100',
    cardBg: 'bg-white',
    cardHover: 'hover:bg-emerald-50',
    headerBg: 'bg-gradient-to-r from-emerald-100 to-teal-100',
  }
};
const DARK_THEMES = {
  default: {
    name: 'Dark Lime',
    primary: '#8bc34a',
    primaryHover: '#7cb342',
    secondary: '#81c784',
    accent1: 'from-lime-500 to-green-500',
    accent2: 'from-blue-500 to-indigo-500',
    accent3: 'from-purple-500 to-pink-500',
    accent4: 'from-yellow-500 to-orange-500',
    bg: 'bg-gray-900',
    cardBg: 'bg-gray-800',
    cardHover: 'hover:bg-lime-900/5',
    headerBg: 'bg-gray-700',
  },
  ocean: {
    name: 'Dark Ocean',
    primary: '#2196f3',
    primaryHover: '#1976d2',
    secondary: '#64b5f6',
    accent1: 'from-blue-500 to-cyan-500',
    accent2: 'from-teal-500 to-blue-500',
    accent3: 'from-indigo-500 to-blue-500',
    accent4: 'from-cyan-500 to-teal-500',
    bg: 'bg-gray-900',
    cardBg: 'bg-gray-800',
    cardHover: 'hover:bg-blue-900/10',
    headerBg: 'bg-gray-700',
  },
  sunset: {
    name: 'Dark Sunset',
    primary: '#ff6b35',
    primaryHover: '#e55100',
    secondary: '#ffab40',
    accent1: 'from-orange-500 to-red-500',
    accent2: 'from-pink-500 to-orange-500',
    accent3: 'from-yellow-500 to-orange-500',
    accent4: 'from-red-500 to-pink-500',
    bg: 'bg-gray-900',
    cardBg: 'bg-gray-800',
    cardHover: 'hover:bg-orange-900/10',
    headerBg: 'bg-gray-700',
  },
  purple: {
    name: 'Dark Purple',
    primary: '#9c27b0',
    primaryHover: '#7b1fa2',
    secondary: '#ba68c8',
    accent1: 'from-purple-500 to-pink-500',
    accent2: 'from-indigo-500 to-purple-500',
    accent3: 'from-pink-500 to-purple-500',
    accent4: 'from-violet-500 to-purple-500',
    bg: 'bg-gray-900',
    cardBg: 'bg-gray-800',
    cardHover: 'hover:bg-purple-900/10',
    headerBg: 'bg-gray-700',
  },
  emerald: {
    name: 'Dark Emerald',
    primary: '#009688',
    primaryHover: '#00695c',
    secondary: '#4db6ac',
    accent1: 'from-emerald-500 to-teal-500',
    accent2: 'from-green-500 to-emerald-500',
    accent3: 'from-teal-500 to-cyan-500',
    accent4: 'from-lime-500 to-green-500',
    bg: 'bg-gray-900',
    cardBg: 'bg-gray-800',
    cardHover: 'hover:bg-emerald-900/10',
    headerBg: 'bg-gray-700',
  }
};
const FONTS = {
  roboto: { name: 'Roboto', class: 'font-roboto' },
  inter: { name: 'Inter', class: 'font-inter' },
  poppins: { name: 'Poppins', class: 'font-poppins' },
  opensans: { name: 'Open Sans', class: 'font-opensans' },
  lato: { name: 'Lato', class: 'font-lato' },
  nunito: { name: 'Nunito', class: 'font-nunito' },
};
// Add custom CSS for fonts
const FontStyles = () => (
  <style jsx global>{`
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Open+Sans:wght@300;400;500;600;700&family=Lato:wght@300;400;700&family=Nunito:wght@300;400;500;600;700&display=swap');
    
    .font-roboto { font-family: 'Roboto', system-ui, -apple-system, sans-serif; }
    .font-inter { font-family: 'Inter', system-ui, -apple-system, sans-serif; }
    .font-poppins { font-family: 'Poppins', system-ui, -apple-system, sans-serif; }
    .font-opensans { font-family: 'Open Sans', system-ui, -apple-system, sans-serif; }
    .font-lato { font-family: 'Lato', system-ui, -apple-system, sans-serif; }
    .font-nunito { font-family: 'Nunito', system-ui, -apple-system, sans-serif; }
  `}</style>
);
// Import actions from slices
import {
  fetchTransactionsStart,
  setSelectedTransaction,
  setOpenModal,
  toggleCardVisibility,
  setSearchName,
  setStartDate,
  setEndDate,
  setSearchDate,
  setSelectedDateRange,
  toggleDateFilter,
  setSortOrder,
  toggleSelectTransaction,
  toggleSelectAll,
  toggleAddTransactionModal,
  updateNewTransaction,
  resetNewTransaction,
  applyFilters,
  addTransactionRequest,
  fetchTransactionSummaryStart ,
  setCustomSearchText,
  selectAllTransactions,
  deselectAllTransactions,
  setSelectedCategory,
   setPage,
     fetchCategoryMonthlyExpensesStart, 
  setOpenCategoryModal ,
  setSelectedSubCategoryId,
  fetchAllIconsStart,
  setSelectedAccount,
  clearSubCategoryIcon,
  
  // setSelectedCategoryId
} from '../../../../logic/redux/transactionSlice';
import { fetchAccountsRequest } from '../../../../logic/redux/transactionSlice';
import {
  setCurrentTab,
  setReceiptUploadModal,
  // setSelectedFile,
  setErrorMessage,
  setShowError,
  // setFileMetadata,
  setBlinkError,
  setIsReceiptModalOpen,
  setSelectedReceipt,
  // setSelectedTransaction,
  setEditingField,
  setEditedValue,
  setEditedItemIndex,
  setSelectedDate,
  setUploadProgress,
  setIsUploading,
  setIsProcessing,
  // setIsPopupVisible,
  // setReceiptNewTransaction,
  uploadReceiptRequest,
  saveReceiptRequest,
  addNewTransaction,
  fetchReceiptTransactionIdsRequest,
  fetchReceiptDetailsRequest,
  // addTransactionRequest,
  updateReceiptField,
  setJsonResponse, 
  setIsMatchingTransactionAdd,
  
} from '../../../../logic/redux/receiptSlice';
export const TransactionNameCell = ({ name, icon }) => {
  // Use the transaction name as fallback when icon prop is null
  const iconName = icon || name;
  return (
    <div className="flex items-center gap-2 w-full min-w-0">
      <BankIcon
        institutionName={iconName}
        // To fix icon issue
        accountType="merchant"
        size={32}
        sizeClass="md"
        className="transaction-icon flex-shrink-0"
      />
      <span className="text-base font-medium truncate flex-1 min-w-0">
        {name}
      </span>
    </div>
  );
};
// Component for account cell with bank icon
export const AccountCell = ({ bankName }) => {
  return (
    <div className="flex items-center gap-2 w-full min-w-0">
      <BankIcon
        institutionName={bankName}
        // To fix icon issue
        accountType="bank"
        size={28}
        sizeClass="md"
        className="account-icon flex-shrink-0"
      />
      <span className="text-base font-medium truncate flex-1 min-w-0">
        {bankName}
      </span>
    </div>
  );
};
// Theme Selector Component
const ThemeSelector = ({ currentTheme, currentFont, darkMode, onThemeChange, onFontChange, colors }) => {
  const [isThemeOpen, setIsThemeOpen] = useState(false);
  const [isFontOpen, setIsFontOpen] = useState(false);
  const themeRef = useRef(null);
  const fontRef = useRef(null);
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (themeRef.current && !themeRef.current.contains(event.target)) {
        setIsThemeOpen(false);
      }
      if (fontRef.current && !fontRef.current.contains(event.target)) {
        setIsFontOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);
  const themes = darkMode ? DARK_THEMES : THEMES;
  return (
<div className="flex items-center gap-3 relative ">      {/* Theme Selector */}
      <div className="relative" ref={themeRef}>
        <Tooltip title="Change Theme" placement="top">
          <button
            className={`
              bg-gradient-to-r ${colors.accent1} text-white p-3 rounded-xl flex items-center
              transition-all duration-200 hover:scale-105 ${colors.shadowHover}
              border-2 border-transparent hover:border-white/30 mb-1
            `}
            onClick={() => setIsThemeOpen(!isThemeOpen)}
          >
            <Palette fontSize="small" />
          </button>
        </Tooltip>
        {isThemeOpen && (
       <div className={`
          absolute top-full right-0 mt-2 z-[1000] ${colors.cardBg} ${colors.shadow}
            border ${colors.border} rounded-2xl max-h-96 min-w-56 max-w-[90vw]
            overflow-auto animate-in slide-in-from-top-2 duration-200
          `}>
            <div className="py-2">
              <div className={`px-4 py-3 text-xs font-semibold ${colors.textSecondary} uppercase tracking-wider border-b ${colors.borderLight}`}>
                Color Themes
              </div>
              
              {Object.entries(themes).map(([key, theme]) => (
                <button 
                  key={key}
                  onClick={() => {
                    onThemeChange(key);
                    setIsThemeOpen(false);
                  }} 
                  className={`
                    w-full px-4 py-3 text-sm ${colors.text} ${colors.cardHover}
                    text-left transition-all duration-150 flex items-center gap-3 group
                    ${currentTheme === key ? 'bg-gradient-to-r ' + theme.accent1 + ' text-white' : ''}
                  `}
                >
                  <div 
                    className={`w-6 h-6 rounded-full bg-gradient-to-r ${theme.accent1} flex-shrink-0 shadow-lg`}
                  ></div>
                  <span className="font-medium">{theme.name}</span>
                  {currentTheme === key && (
                    <div className="ml-auto w-2 h-2 bg-white rounded-full"></div>
                  )}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
      {/* Font Selector */}
      <div className="relative" ref={fontRef}>
        <Tooltip title="Change Font" placement="top">
          <button
            className={`
             bg-gradient-to-r ${colors.accent2} text-white p-3 rounded-xl flex items-center
              transition-all duration-200 hover:scale-105 ${colors.shadowHover}
              border-2 border-transparent hover:border-white/30 mb-1
            `}
            onClick={() => setIsFontOpen(!isFontOpen)}
          >
            <TextFormat fontSize="small" />
          </button>
        </Tooltip>
        {isFontOpen && (
         <div className={`
      absolute top-full right-0 mt-2 z-[1000] ${colors.cardBg} ${colors.shadow}
            border ${colors.border} rounded-2xl max-h-96 min-w-56 max-w-[90vw]
            overflow-auto animate-in slide-in-from-top-2 duration-200
          `}>
            <div className="py-2">
              <div className={`px-4 py-3 text-xs font-semibold ${colors.textSecondary} uppercase tracking-wider border-b ${colors.borderLight}`}>
                Font Families
              </div>
              
              {Object.entries(FONTS).map(([key, font]) => (
                <button 
                  key={key}
                  onClick={() => {
                    onFontChange(key);
                    setIsFontOpen(false);
                  }} 
                  className={`
                    w-full px-4 py-3 text-sm ${colors.text} ${colors.cardHover}
                    text-left transition-all duration-150 flex items-center gap-3 group
                    ${currentFont === key ? 'bg-gradient-to-r ' + colors.accent2 + ' text-white' : ''}
                    ${font.class}
                  `}
                >
                  <span className="font-medium">{font.name}</span>
                  {currentFont === key && (
                    <div className="ml-auto w-2 h-2 bg-white rounded-full"></div>
                  )}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
// Custom Table Component
const CustomTransactionTable = ({ 
  rows, 
  selectedTransactions, 
  expandedRows, 
  reconciledTransactionsById,
  receiptTransactionIds,
  colors,
  darkMode,
  fontClass,
  dispatch,
  handleRowClick,
  handleReceiptClick,
  handleSplitIconClick,
  setSelectedCategoryId,
  toggleSelectTransaction,
  selectAllTransactions,
  deselectAllTransactions,
  setSelectedTransaction,
  setOpenModal,
  fetchCategoryMonthlyExpensesStart,
  setSelectedCategory,
  setSelectedSubCategoryId,
  setOpenCategoryModal
}) => {
  const [sortField, setSortField] = useState('date');
  const [sortDirection, setSortDirection] = useState('desc');
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  // Calculate select all state
  const selectableRows = rows.filter(row =>
    !row.isGroupHeader &&
    !row.isReconciledDetail &&
    row.transaction_id
  );
  const allSelected = selectableRows.length > 0 &&
    selectableRows.every(row => selectedTransactions.includes(row.transaction_id));
  const someSelected = selectableRows.some(row =>
    selectedTransactions.includes(row.transaction_id)
  );
  return (
    <div className={`${colors.cardBg} rounded-2xl border ${colors.border} ${colors.shadow} overflow-hidden ${fontClass}`}>
      
      {/* Table Body - No Headers */}
      <div className="max-h-[2000px] overflow-y-auto">
        {rows.length === 0 ? (
          <div className="p-8 text-center">
            <p className={`${colors.textSecondary} text-lg`}>
              No transactions match the current filters.
            </p>
          </div>
        ) : (
          rows.map((row, index) => {
          if (row.isGroupHeader) {
            return (
              <div
                key={row.id}
                className={`${darkMode ? 'bg-lime-800/10' : 'bg-lime-100'} border-b ${colors.borderLight} sticky top-0 z-[5] ${fontClass}`}
              >
                <div className="grid gap-4 p-4" style={{ gridTemplateColumns: '27% 27% 27% 19%' }}>
                  <div className="flex items-center">
                    <span className={`font-bold text-base ${darkMode ? 'text-lime-400' : 'text-lime-700'}`}>
                      {row.date}
                    </span>
                  </div>
                  <div></div>
                  <div></div>
                  <div className="flex justify-center">
                    <span className={`font-bold text-base ${darkMode ? 'text-lime-400' : 'text-lime-700'}`}>
                      ${row.amount}
                    </span>
                  </div>
                </div>
              </div>
            );
          }
          const isSelected = selectedTransactions.includes(row.transaction_id);
          const hasReconcile = row.reconcileId;
          const isExpanded = expandedRows[row.transaction_id];
          const isLoading = hasReconcile && isExpanded && !reconciledTransactionsById[row.reconcileId];
          const hasReceipt = receiptTransactionIds.includes(row.transaction_id);
          return (
            <div key={row.id}>
              {/* Main Row */}
              <div
                className={`
                  grid gap-4 p-4 border-b ${colors.borderLight} 
                  ${colors.cardHover}
                  transition-all duration-200 group cursor-pointer
                  ${row.isReconciledDetail ? 'bg-gray-50/50 pl-8' : ''}
                  ${isSelected ? 'bg-lime-100/30' : ''}
                `}
                style={{ gridTemplateColumns: '27% 27% 27% 19%' }}
                onClick={() => {
                  if (!row.isGroupHeader && !row.isReconciledDetail) {
                    handleRowClick(row.transaction_id);
                  }
                }}
              >
                {/* Description with integrated checkbox */}
                <div className={`flex items-center justify-start ${fontClass}`}>
                  {row.isReconciledDetail ? (
                    <div className={`pl-8 ${colors.textSecondary} italic w-full`}>
                      <TransactionNameCell name={row.name} icon={row.icon} />
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2 w-full">
                      {/* Checkbox integrated with description */}
                      {!row.isGroupHeader && !row.isReconciledDetail && row.transaction_id && (
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => dispatch(toggleSelectTransaction(row.transaction_id))}
                          className={`h-4 w-4 text-lime-500 border-gray-300 rounded focus:ring-lime-500 flex-shrink-0 ${
                            isSelected ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
                          } transition-opacity`}
                          onClick={(e) => e.stopPropagation()}
                        />
                      )}
                      
                      {hasReconcile ? (
                        <div 
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRowClick(row.transaction_id);
                          }}
                          className={`cursor-pointer ${darkMode ? 'text-lime-400' : 'text-lime-600'} hover:scale-110 transition-transform flex-shrink-0`}
                        >
                          {isLoading ? (
                            <CircularProgress size={16} className="text-lime-500" />
                          ) : (
                            <span className="text-base font-bold">{isExpanded ? '▼' : '▶'}</span>
                          )}
                        </div>
                      ) : (
                        <div className="w-4 flex-shrink-0" />
                      )}
                      <div 
                        onClick={(e) => {
                          e.stopPropagation();
                          dispatch(setSelectedTransaction(row));
                          dispatch(setOpenModal(true));
                        }}
                        className="cursor-pointer hover:scale-105 transition-transform flex-1 min-w-0"
                      >
                        <TransactionNameCell name={row.name} icon={row.icon} />
                      </div>
                    </div>
                  )}
                </div>
                {/* Category */}
                <div className={`flex items-center justify-start ${fontClass}`}>
                  {!row.isGroupHeader && (
                    <div
                      className={`flex items-center gap-2 cursor-pointer transition-all duration-200 hover:scale-105 w-full ${
                        darkMode ? 'hover:text-lime-400' : 'hover:text-lime-600'
                      } ${
                        row.isReconciledDetail ? `${colors.textSecondary} pl-4` : colors.text
                      }`}
                      onClick={(e) => {
                        e.stopPropagation();
                         const userId = getCurrentUserId();
                        setSelectedCategoryId(row.categoryId);
                        dispatch(fetchCategoryMonthlyExpensesStart({
                          categoryId: row.categoryId,
                          subCategoryId: row.subCategoryId,
                          userId,
                          months: 3
                        }));
                        dispatch(setSelectedCategory(row.category));
                        dispatch(setSelectedSubCategoryId(row.subCategoryId));
                        dispatch(setOpenCategoryModal(true));
                      }}
                    >
                      <SubCategoryIcon 
                        subCategoryId={row.subCategoryId} 
                        size={18}
                        className="flex-shrink-0"
                      />
                      <span className="text-base font-medium truncate flex-1">{row.category}</span>
                    </div>
                  )}
                </div>
                {/* Account */}
                <div className={`flex items-center justify-start ${fontClass}`}>
                  {!row.isGroupHeader && (
                    <div className={`w-full ${row.isReconciledDetail ? `${colors.textSecondary} pl-4` : ''}`}>
                      <AccountCell bankName={row.bank} />
                    </div>
                  )}
                </div>
                {/* Amount */}
                <div className={`flex items-center justify-center ${fontClass}`}>
                  <span className={`font-semibold text-base ${row.isReconciledDetail ? colors.textSecondary : colors.text}`}>
                    ${row.amount}
                  </span>
                </div>
              </div>
              {/* Reconciled Details */}
              {isExpanded && hasReconcile && reconciledTransactionsById[row.reconcileId] && (
                <div className={`${darkMode ? 'bg-gray-800/30' : 'bg-gray-50'} ${fontClass}`}>
                  {reconciledTransactionsById[row.reconcileId]
                    .filter(match => match.id !== row.transaction_id && (row.amount * match.amount < 0))
                    .map((match, matchIndex) => (
                      <div
                        key={`reconcile-${row.transaction_id}-${matchIndex}`}
                        className={`grid gap-4 p-4 pl-12 border-b ${colors.borderLight} ${colors.textSecondary} italic`}
                        style={{ gridTemplateColumns: '27% 27% 27% 19%' }}
                      >
                        <div className="flex items-center justify-start">
                          <TransactionNameCell name={match.description} icon={match.icon} />
                        </div>
                        <div className="flex items-center justify-start">
                          <span className="text-base font-medium">{match.category}</span>
                        </div>
                        <div className="flex items-center justify-start">
                          <AccountCell bankName={match.accountName} />
                        </div>
                        <div className="flex items-center justify-center">
                          <span className="font-semibold text-base">
                            ${match.amount?.toFixed(2) || 'N/A'}
                          </span>
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </div>
          );
        }))}
      </div>
    </div>
  );
};
// Hidden Transactions Table
const HiddenTransactionsTable = ({ hiddenTransactions, colors, darkMode, fontClass, dispatch, setSelectedTransaction, setOpenModal }) => {
  return (
    <div className={`${colors.cardBg} rounded-2xl border ${colors.border} ${colors.shadow} overflow-hidden ${fontClass}`}>
      {/* Body - No Headers */}
      <div className="max-h-[1000px] overflow-y-auto">
        {hiddenTransactions.map((transaction) => (
          <div
            key={transaction.id}
            className={`grid gap-4 p-4 border-b ${colors.borderLight} ${colors.cardHover} transition-all duration-200 cursor-pointer ${fontClass}`}
            style={{ gridTemplateColumns: '27% 27% 27% 19%' }}
            onClick={() => {
              dispatch(setSelectedTransaction(transaction));
              dispatch(setOpenModal(true));
            }}
          >
            <div className="flex items-center justify-start">
              <TransactionNameCell name={transaction.description} icon={transaction.icon} />
            </div>
            <div className="flex items-center justify-start">
              <span className="text-base font-medium text-gray-700">{transaction.category}</span>
            </div>
            <div className="flex items-center justify-start">
              <AccountCell bankName={transaction.accountName} />
            </div>
            <div className="flex items-center justify-center">
              <span className="text-base font-semibold text-gray-700">{transaction.transactionAmount}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
const TransactionPage1 = ({ darkMode, onAIChatOpen, onAIChatClose }) => {

  // Theme and font state
  const [activeTab, setActiveTab] = useState('transactions');
const [dialogTab, setDialogTab] = useState(0);
  const [currentTheme, setCurrentTheme] = useState(() => {
    return localStorage.getItem('transactionTheme') || 'default';
  });
  
  const [currentFont, setCurrentFont] = useState(() => {
    return localStorage.getItem('transactionFont') || 'roboto';
  });

  // AI Chat state
  const [showAIChat, setShowAIChat] = useState(false);

  // Handle AI chat toggle with sidebar coordination
  const handleAIChatToggle = () => {
    const newShowAIChat = !showAIChat;
    setShowAIChat(newShowAIChat);

    // Call sidebar handlers if provided
    if (newShowAIChat && onAIChatOpen) {
      onAIChatOpen();
    } else if (!newShowAIChat && onAIChatClose) {
      onAIChatClose();
    }
  };

  // Handle AI chat close (for the close button in the chat)
  const handleAIChatClose = () => {
    setShowAIChat(false);
    if (onAIChatClose) {
      onAIChatClose();
    }
  };

    const dispatch = useDispatch();
 const subCategoryIcons = useSelector(state => state.transactions.subCategoryIcons || {});
    const [splitModalOpen, setSplitModalOpen] = useState(false);
      const [selectedSplitTransaction, setSelectedSplitTransaction] = useState(null);
  const transactions = useSelector(state => state.transactions.transactions);
const page = useSelector(state => state.transactions.page);
const pageSize = useSelector(state => state.transactions.pageSize);
const totalElements = useSelector(state => state.transactions.totalElements);
const totalPages = useSelector(state => state.transactions.totalPages);
 const loading = useSelector(state => state.transactions.loading);
  const [selectedCategoryId, setSelectedCategoryId] = useState(null);
const [rows, setRows] = useState([]);
const [showHidden, setShowHidden] = useState(false);
const summary = useSelector(state => state.transactions.summary);
const accounts = useSelector((state) => state.transactions.accounts); // same slice
const loadingAccounts = useSelector((state) => state.transactions.loadingAccounts);
const categoryId = useSelector((state) => state.transactions.selectedCategoryId);
const hiddenTransactions = useSelector(state => state.transactions.hiddenTransactions);
  const selectedTransaction = useSelector(state => state.transactions.selectedTransaction);
  const openModal = useSelector(state => state.transactions.openModal);
  const isCardVisible = useSelector(state => state.transactions.isCardVisible);
  const searchName = useSelector(state => state.transactions.searchName);
  const searchDate = useSelector(state => state.transactions.searchDate);
  const sortOrder = useSelector(state => state.transactions.sortOrder);
  const dateFilterOpen = useSelector(state => state.transactions.dateFilterOpen);
  const selectedDateRange = useSelector(state => state.transactions.selectedDateRange);
  const selectedTransactions = useSelector(state => state.transactions.selectedTransactions);
  const allChecked = useSelector(state => state.transactions.allChecked);
  const isAddTransactionOpen = useSelector(state => state.transactions.isAddTransactionOpen);
   const newTransaction = useSelector(state => state.transactions.newTransaction);
   const hideFromBudgetSuccess = useSelector(state => state.transactions.hideFromBudgetSuccess);
  const hideFromBudgetError = useSelector(state => state.transactions.hideFromBudgetError);
 const [expandedRows, setExpandedRows] = useState({});
 const selectedAccount = useSelector((state) => state.transactions.selectedAccount);
  const reconciledTransactionsById = useSelector(state => state.transactions.reconciledTransactionsById);
  const [expandedTransactionIds, setExpandedTransactionIds] = useState([]);
  const [customDateRange, setCustomDateRange] = useState({ start: '', end: '' });
const [customFilterOpen, setCustomFilterOpen] = useState(false);
const customText = useSelector((state) => state.transactions.customSearchText);
const [initialLoading, setInitialLoading] = useState(true);
const { iconMap, loaded } = useSelector(state => state.transactions);
 const [showEmbeddedReceiptUpload, setShowEmbeddedReceiptUpload] = useState(false);
const error = useSelector((state) => state.transactions.error);
const [showCalendarView, setShowCalendarView] = useState(false);
const [isTransactionProcessing, setIsTransactionProcessing] = useState(false);
const [formData, setFormData] = useState({});

  // Receipts state
   const {
    errorMessage,
    jsonResponse,
    currentTab,
    receiptUploadModal,
    showError,
    blinkError,
    isReceiptModalOpen,
    selectedReceipt,
    // selectedFile,
    fileMetadata,
    // selectedTransaction,
    uploadPopupWidth,
    editingField,
    editedValue,
    editedItemIndex,
    selectedDate,
    uploadProgress,
    isUploading,
    isProcessing,
    isPopupVisible,
    
    //  receiptNewTransaction,
    receiptTransactionIds,
  } = useSelector((state) => state.receipts);
  
  const receiptNewTransaction = useSelector((state) => state.receipts.receiptNewTransaction);
const { addTransactionSuccess, addTransactionError } = useSelector((state) => state.receipts);
const categories = useSelector(state => state.budget.categories);
const subcategories = useSelector(state => state.budget.subcategories);
  const isMatchingTransactionAdd = useSelector(state => state.receipts.isMatchingTransactionAdd);
const { saveSuccess, saveError } = useSelector((state) => state.receipts);
  // Refs
  const dateFilterRef = useRef(null);
 const containerRef = useRef(null);
 const [exportDropdownOpen, setExportDropdownOpen] = useState(false);
const exportDropdownRef = useRef(null);
const handleThemeChange = (theme) => {
    setCurrentTheme(theme);
    localStorage.setItem('transactionTheme', theme);
    logEvent('TransactionPage', 'ThemeChange', { theme });
  };
  const handleFontChange = (font) => {
    setCurrentFont(font);
    localStorage.setItem('transactionFont', font);
    logEvent('TransactionPage', 'FontChange', { font });
  };
    const handleSplitIconClick = useCallback((transaction) => {
    setSelectedSplitTransaction(transaction);
    setSplitModalOpen(true);
    // Close any other open modals if needed
    dispatch(setOpenModal(false));
  }, [dispatch]);
  const handleCloseSplitModal = useCallback(() => {
    setSplitModalOpen(false);
    setSelectedSplitTransaction(null);
  }, []);
  const handleSubmitSplit = (splitData) => {
    console.log("Split request submitted:", splitData);
    // The actual submission is now handled by the epic
  };
  useEffect(() => {
    const userId = getCurrentUserId();
    if (userId) {
      dispatch(fetchAccountsRequest({ userId }));
    } else {
      console.warn('User ID not available, skipping accounts fetch');
      dispatch(fetchAccountsFailure('User not authenticated'));
    }
  }, [dispatch]);
  
  useEffect(() => {
     dispatch(fetchReceiptTransactionIdsRequest());
   }, [dispatch]);
   useEffect(() => {
  const handleClickOutside = (event) => {
    if (exportDropdownRef.current && !exportDropdownRef.current.contains(event.target)) {
      setExportDropdownOpen(false);
    }
  };
  document.addEventListener('mousedown', handleClickOutside);
  return () => {
    document.removeEventListener('mousedown', handleClickOutside);
  };
}, []);
 
useEffect(() => {
  const handleClickOutside = (event) => {
    if (
      dateFilterRef.current &&
      !dateFilterRef.current.contains(event.target)
    ) {
      dispatch(toggleDateFilter(false)); // Explicitly close it
    }
  };

  document.addEventListener('mousedown', handleClickOutside);
  return () => {
    document.removeEventListener('mousedown', handleClickOutside);
  };
}, []);
useEffect(() => {
    let timeout;
    if (initialLoading) {
      timeout = setTimeout(() => {
        setInitialLoading(false);
      }, 10000); // 10 seconds
    }
    return () => clearTimeout(timeout);
  }, [initialLoading]);
useEffect(() => {
  if (addTransactionSuccess) {
    alert('Transaction added successfully!');
    dispatch({
      type: 'receipts/resetAddTransactionStatus', // Add a new action to reset status
      payload: { addTransactionSuccess: false, addTransactionError: null },
    });
  }
}, [addTransactionSuccess, dispatch]);
useEffect(() => {
  if (addTransactionError) {
    alert(`Error adding transaction: ${addTransactionError}`);
    dispatch({
      type: 'receipts/resetAddTransactionStatus',
      payload: { addTransactionSuccess: false, addTransactionError: null },
    });
  }
}, [addTransactionError, dispatch]);
   const handleCloseReceiptModal = () => {
    dispatch(setIsReceiptModalOpen(false));
  };
 useEffect(() => {
  console.log('Current iconMap:', iconMap); // Add this line
  if (!loaded && !loading) {
    dispatch(fetchAllIconsStart());
  }
}, [dispatch, loaded, loading, iconMap]);

useEffect(() => {
  if (!selectedTransaction || accounts.length === 0 || subcategories.length === 0) return;

  const matchedAccount = accounts.find((a) =>
    `${a.accountName} ****${a.accountMask}` === selectedTransaction.bank
  );

  const matchedSub = subcategories.find(
    (s) =>
      s.subCategory?.trim().toLowerCase() ===
      selectedTransaction.category?.trim().toLowerCase()
  );

  // Format the date properly when initializing the form
  const formattedDate = selectedTransaction.transactionDate 
    ? formatDateForInput(selectedTransaction.transactionDate)
    : new Date().toISOString().split('T')[0];

  setFormData({
    ...selectedTransaction,
    date: formattedDate, // Make sure this is in YYYY-MM-DD format
    bank: matchedAccount ? matchedAccount.accountId.toString() : '',
    subcategory: matchedSub ? matchedSub.subCategory.trim() : '',
  });
}, [selectedTransaction, accounts, subcategories]);


useEffect(() => {
  if (!selectedTransaction || accounts.length === 0 || subcategories.length === 0) return;

  // Find the account that matches selectedTransaction.bank (by name string)
  const matchedAccount = accounts.find((a) =>
    `${a.accountName} ****${a.accountMask}` === selectedTransaction.bank
  );

  // Find subcategory that matches selectedTransaction.category
  const matchedSub = subcategories.find(
    (s) =>
      s.subCategory?.trim().toLowerCase() ===
      selectedTransaction.category?.trim().toLowerCase()
  );

  console.log('✅ matchedAccount:', matchedAccount);
  console.log('✅ matchedSub:', matchedSub);

  setFormData({
    ...selectedTransaction,
    bank: matchedAccount ? matchedAccount.accountId.toString() : '',
    subcategory: matchedSub ? matchedSub.subCategory.trim() : '',
  });
}, [selectedTransaction, accounts, subcategories]);


  useEffect(() => {
    dispatch({ type: 'transactions/setAppendMode', payload: false }); // Initial load
    dispatch({ type: 'transactions/fetchTransactionsStart' });
  }, [dispatch]);
   useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current || loading) return;
      const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
      const isBottom = scrollTop + clientHeight >= scrollHeight - 200; // 200px from bottom
      if (isBottom && page < totalPages - 1) {
        dispatch({ type: 'transactions/setAppendMode', payload: true });
        dispatch({ type: 'transactions/setPage', payload: page + 1 });
        dispatch({ type: 'transactions/fetchTransactionsStart' });
      }
    };
    const container = containerRef.current;
    if (container) container.addEventListener('scroll', handleScroll);
    return () => {
      if (container) container.removeEventListener('scroll', handleScroll);
    };
  }, [dispatch, page, totalPages, loading]);
  
  // Fetch transactions on component mount
// Initial load only (first 250 items)
 useEffect(() => {
    dispatch(fetchTransactionsStart());
  }, [dispatch,page, pageSize]);
    useEffect(() => {
        console.log("Dispatching fetchTransactionSummaryStart");
    dispatch(fetchTransactionSummaryStart()); 
  }, [dispatch]);
   useEffect(() => {
  dispatch({ type: 'transactions/fetchHiddenTransactions' });
}, [dispatch]);
  
// Fetch categories/subcategories on mount
useEffect(() => {
  dispatch({ type: 'budget/fetchCategories' });
  dispatch({ type: 'budget/fetchSubcategories' });
}, [dispatch]);
useEffect(() => {
  if (isPopupVisible) {
    dispatch({ type: 'budget/fetchCategories' });
    dispatch({ type: 'budget/fetchSubcategories' });
  }
}, [dispatch, isPopupVisible]);
  useEffect(() => {
    if (error) {
      console.error('Transaction fetch error:', error);
      // Optionally, show a toast or alert
      // toast.error(`Failed to load transactions: ${error}`);
    }
  }, [error]);
  // Get current theme configuration
  const themeConfig = darkMode ? DARK_THEMES[currentTheme] : THEMES[currentTheme];
  const fontClass = FONTS[currentFont].class;
  // Redux state selectors
  // Define color themes with selected theme
  const colors = {
    primary: themeConfig.primary,
    primaryHover: themeConfig.primaryHover,
    secondary: themeConfig.secondary,
    positive: darkMode ? 'text-green-400' : 'text-green-600',
    negative: darkMode ? 'text-red-400' : 'text-red-600',
    neutral: darkMode ? 'text-gray-400' : 'text-gray-600',
    bg: themeConfig.bg,
    cardBg: themeConfig.cardBg,
    tableBg: themeConfig.cardBg,
    headerBg: themeConfig.headerBg,
    text: darkMode ? 'text-white' : 'text-gray-800',
    textSecondary: darkMode ? 'text-gray-300' : 'text-gray-600',
    border: darkMode ? 'border-gray-600' : 'border-gray-200',
    borderLight: darkMode ? 'border-gray-700' : 'border-gray-100',
    shadow: darkMode ? 'shadow-gray-900/20' : 'shadow-lg',
    shadowHover: darkMode ? 'hover:shadow-gray-900/30' : 'hover:shadow-xl',
    buttonPrimary: `bg-[${themeConfig.primary}] hover:bg-[${themeConfig.primaryHover}]`,
    buttonSecondary: darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-100 hover:bg-gray-200',
    accent1: themeConfig.accent1,
    accent2: themeConfig.accent2,
    accent3: themeConfig.accent3,
    accent4: themeConfig.accent4,
    cardHover: themeConfig.cardHover,
  };
  // Transactions state
const handleHideTransaction = (transactionId) => {
  if (!transactionId) return;
  dispatch({ type: 'transactions/hideTransactions', payload: [transactionId] });
  closeTransactionModal();
};
// const user = useSelector((state) => state.auth.user); 
   // Handle receipt click
   const handleReceiptClick = (transactionId) => {
     dispatch(fetchReceiptDetailsRequest(transactionId));
   };
 
   const handleFieldDoubleClick = (field) => {
     dispatch(setEditingField(field));
     dispatch(setEditedItemIndex(null));
     dispatch(setEditedValue(jsonResponse?.[field] || ''));
   };
 
   // Handle date change
   const handleDateChange = (date) => {
     dispatch(setSelectedDate(date));
     dispatch(setEditedValue(date.toISOString().split('T')[0]));
   };
//dropdown for export data
 
  const handleSelectAll = () => {
    dispatch(toggleSelectAll());
  };
  const handleCheckboxChange = (transactionId) => {
    dispatch(toggleSelectTransaction(transactionId));
  };
  const toggleTransactionModal = (transaction) => {
    dispatch(setSelectedTransaction(transaction));
    dispatch(setOpenModal(true));
  };
   const toggleReceiptModal = () => {
      dispatch(setReceiptUploadModal(true));
      dispatch(setJsonResponse(null));
      dispatch(setErrorMessage('')); // Clear error message
    dispatch(setShowError(false));
    };
  const closeTransactionModal = () => {
    dispatch(setOpenModal(false));
    dispatch(setSelectedTransaction(null));
  };
//transaction detail model
const handleFieldChange = (field, value) => {
  setFormData(prev => ({
    ...prev,
    [field]: value,
  }));
};
// In your TransactionPage1 component
const handleSaveChanges = () => {
  console.log('formData for validation:', formData);

  if (
    !formData.date ||
    !formData.name ||
    !formData.subcategory ||  // Use subcategory (not category)
    !formData.bank ||
    !formData.amount
  ) {
    alert('Please fill in all required fields: Date, Description, Category, Account, Amount.');
    return;
  }

  const matchedSub = subcategories.find(
    (sub) => sub.subCategory.trim().toLowerCase() === formData.subcategory.trim().toLowerCase()
  );
  const categoryId = matchedSub?.categoryId || null;
  const accountId = Number(formData.bank) || null;

  const formattedTransaction = {
    transactionId: formData.transaction_id || `temp-${Date.now()}`,
transactionDate: new Date(formData.date).toISOString(),
      description: formData.name,
    category: formData.subcategory,
    categoryId,
    account: formData.bank,
    accountId,
    transactionAmount: parseFloat(formData.amount) || 0,
    tax: parseFloat(formData.tax) || 0,
    notes: formData.notes || '',
    tag: formData.tag || '',
    hideFromBudget: formData.hideFromBudget || false,
    hidden: formData.hidden || false,
    userId: getCurrentUserId(),
  };
  console.log('Sending to server:', formattedTransaction);
  if (isMatchingTransactionAdd) {
    dispatch(addNewTransaction(formattedTransaction));
  } else if (formData.transaction_id) {
    dispatch({
      type: 'transactions/updateTransaction',
      payload: {
        id: formData.transaction_id,
        data: formattedTransaction,
      },
    });
  } else {
    dispatch(addTransactionRequest({ transactionData: formattedTransaction }));
  }

  dispatch(setOpenModal(false));
  dispatch(setSelectedTransaction(null));
  dispatch(setIsMatchingTransactionAdd(false));
};
 const handleOpenAddTransaction = () => {
  dispatch(setSelectedTransaction({
    date: new Date().toISOString().split('T')[0],
    name: '',
    subcategory: '',
    bank: '',
    amount: '',
    tax: '',
    notes: '',
    tag: '',
    hideFromBudget: false,
    hidden: false,
  }));
  dispatch(setIsMatchingTransactionAdd(false));
  dispatch(setOpenModal(true));
};
const handletranssave = () => {
  let formattedDate = null;
  if (newTransaction.date) {
    const fullDate = new Date(newTransaction.date);
    if (!isNaN(fullDate)) {
      formattedDate = fullDate.toISOString();
    } else {
      console.warn('Invalid date format:', newTransaction.date);
    }
  }
  const transactionData = {
    transactionId: `temp-${Date.now()}`,
    transactionDate: formattedDate,
    description: newTransaction.description,
    category: newTransaction.category,
    account: newTransaction.account,
    transactionAmount: newTransaction.amount ? parseFloat(newTransaction.amount) : 0,
      userId: getCurrentUserId(),
  };
  dispatch(addTransactionRequest({ transactionData }));
};
//for trans detail popup
const formatDateForInput = (dateString) => {
  if (!dateString) return '';
  
  // If it's already in YYYY-MM-DD format, return as-is
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
    return dateString;
  }
  
  // Otherwise, parse it
  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    console.warn('Invalid date format:', dateString);
    return '';
  }
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};
    // Replace with actual data source
 const selectDateRange = (range) => {
  const today = new Date();
  let start = null;
  let end = null;

  // Handle "custom" first to prevent dropdown toggle
  if (range === 'custom') {
    setCustomFilterOpen(true);
    dispatch(setSelectedDateRange('custom'));

    // If dropdown is closed, open it
    if (!dateFilterOpen) {
      dispatch(toggleDateFilter());
    }

    return;
  }

  // Close custom input if not using custom
  setCustomFilterOpen(false);

  dispatch(setSelectedDateRange(range));
  logEvent('DateFilter', 'SelectRange', { range });

  switch (range) {
    case 'last7days':
      start = new Date(today);
      start.setDate(today.getDate() - 6); // Include today
      end = new Date(today);
      break;

    case 'currentMonth':
      start = new Date(today.getFullYear(), today.getMonth(), 1);
      end = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      break;

    case 'previousMonth':
      start = new Date(today.getFullYear(), today.getMonth() - 1, 1);
      end = new Date(today.getFullYear(), today.getMonth(), 0);
      break;

    case 'last6months':
      start = new Date(today);
      start.setMonth(today.getMonth() - 6);
      start.setDate(1);
      end = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      break;

    case 'all':
      dispatch(setSearchDate({ start: '', end: '' }));
      dispatch(applyFilters());
      dispatch(toggleDateFilter());
      return;
  }

  // Format dates and apply filters
  if (start && end) {
    dispatch(setSearchDate({
      start: start.toISOString().split('T')[0],
      end: end.toISOString().split('T')[0]
    }));
    dispatch(applyFilters());
  }

  dispatch(toggleDateFilter());
};

  const applyFiltersHandler = () => {
    dispatch(applyFilters());
  };
  
 const exportToCSV = async () => {
  const userId = getCurrentUserId();
  console.log("Exporting for user:", userId);
  if (!userId) {
    alert("No user ID found. Cannot export.");
    return;
  }
  const pageSize = 250;
  let currentPage = 0;
  let allTransactions = [];
  let totalPages = 1;
  try {
    while (currentPage < totalPages) {
      const response = await axiosInstance.get(
`/pennypal/api/v1/transaction/transactions/user/${userId}?page=${currentPage}&pageSize=${pageSize}&removed=false&is_remove=false` ,
     { timeout: 80000 } 
);
      const data = response.data;
      const transactions = data.transactions || data.content || [];
      totalPages = data.totalPages || Math.ceil((data.totalElements || 0) / pageSize);
      // ❗️Filter out unwanted transactions (removed or is_remove)
      const filteredTransactions = transactions.filter(tx => !tx.removed && !tx.is_remove);
      console.log(`Fetched page ${currentPage + 1} of ${totalPages}`);
      console.log(`Transactions fetched: ${transactions.length }, after filtering: ${filteredTransactions.length}`);
      allTransactions = [...allTransactions, ...filteredTransactions];
      currentPage += 1;
    }
    console.log("Exporting", allTransactions.length, "transactions");
    if (allTransactions.length === 0) {
      alert("No transactions to export.");
      return;
    }
    // Escape values to be CSV-safe
    const escapeCSV = value => `"${String(value).replace(/"/g, '""')}"`;
    const headers = ['Transaction Date', 'Description', 'Amount', 'Category', 'Account Name'];
    const rows = allTransactions.map(tx => [
             escapeCSV(tx.id || ''),
      escapeCSV(tx.transactionDate || ''),
      escapeCSV(tx.description || ''),
      escapeCSV(tx.transactionAmount || ''),
      escapeCSV(tx.category || ''),
      escapeCSV(tx.accountName || '')
    ]);
    const csvData = [headers, ...rows].map(e => e.join(",")).join("\n");
    const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "transactions.csv";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (err) {
    console.error("Failed to export transactions:", err);
    const errorMsg = err instanceof Error
      ? err.message
      : typeof err === 'string'
        ? err
        : "Unknown error";
    alert(`Export failed: ${errorMsg}`);
  }
};
const exportToPDF = () => {
  const doc = new jsPDF({ orientation: 'landscape' });
  if (!transactions.length) return;
  const headers = [['Transaction Date', 'Description', 'Amount', 'Category', 'Account Name']];
  const data = transactions.map(tx => [
    tx.transactionDate || '',
    tx.description || '',
    tx.transactionAmount || '',
    tx.category || '',
    tx.accountName || ''
  ]);
  autoTable(doc, {
    head: headers,
    body: data,
    styles: {
      fontSize: 7,
      cellPadding: 2,
      overflow: 'linebreak',
      cellWidth: 'wrap',
    },
    headStyles: {
      fillColor: [139, 195, 74],
      fontSize: 7,
    },
    startY: 10,
    margin: { left: 5, right: 5 },
    theme: 'striped',
  });
  doc.save('transactions.pdf');
};
  // Helper functions
  
  // Group transactions by date
 const handleRowClick = (transactionId) => {
    console.log('1. Row clicked - transactionId:', transactionId);
    
    setExpandedRows(prev => {
      const newState = {...prev, [transactionId]: !prev[transactionId]};
      console.log('2. Expanded rows state:', newState);
      return newState;
    });
  
    const transaction = rows.find(row => row.transaction_id === transactionId);
    console.log('3. Found transaction:', transaction);
    
    if (!reconciledTransactionsById[transaction.reconcileId]) {
      console.log('Dispatching to fetch reconciled transactions');
      dispatch({ 
        type: 'transactions/getReconciledTransactions', 
        payload: transaction.reconcileId 
      });
    } else {
      console.log('4. No reconcileId found for transaction');
    }
  };
  
  useEffect(() => {
      if (!Array.isArray(transactions)) return;
    const buildRows = () => {
      console.log('Building rows with state:', {
        transactions,
        reconciledTransactionsById,
        expandedRows
      });
      
      console.log('Transactions data:', transactions.map(t => ({
        id: t.id,
        description: t.description,
        reconcileId: t.reconcileId,
      })));
      // Generate mock data if we have fewer than 250 transactions
      let allTransactions = [...transactions];
      
      // if (allTransactions.length < 250) {
      //   const mockCategories = ['Food & Dining', 'Gas & Transportation', 'Shopping', 'Entertainment', 'Bills & Utilities', 'Healthcare', 'Travel', 'Investment'];
      //   const mockDescriptions = ['Starbucks', 'Shell Gas Station', 'Amazon', 'Netflix', 'Electric Bill', 'CVS Pharmacy', 'Uber', 'TD Ameritrade'];
      //   const mockAccounts = ['Chase Checking', 'Wells Fargo Credit', 'Bank of America Savings', 'Capital One Credit'];
        
      //   for (let i = allTransactions.length; i < 250; i++) {
      //     const mockDate = new Date();
      //     mockDate.setDate(mockDate.getDate() - Math.floor(Math.random() * 365));
          
      //     allTransactions.push({
      //       id: `mock-${i}`,
      //       transactionDate: mockDate.toISOString(),
      //       description: mockDescriptions[Math.floor(Math.random() * mockDescriptions.length)] + ` #${i}`,
      //       category: mockCategories[Math.floor(Math.random() * mockCategories.length)],
      //       transactionAmount: Math.random() * 500 + 10,
      //       accountName: mockAccounts[Math.floor(Math.random() * mockAccounts.length)],
      //       categoryId: Math.floor(Math.random() * 10) + 1,
      //       subCategoryId: Math.floor(Math.random() * 20) + 1,
      //       reconcileId: Math.random() > 0.8 ? `reconcile-${i}` : null
      //     });
      //   }
      // }
      
      const groupedTransactions = allTransactions.reduce((acc, transaction) => {
        const transactionDate = new Date(transaction.transactionDate).toLocaleDateString();
        if (!acc[transactionDate]) {
          acc[transactionDate] = { totalAmount: 0, transactions: [] };
        }
        acc[transactionDate].transactions.push(transaction);
        acc[transactionDate].totalAmount += transaction.transactionAmount || 0;
        return acc;
      }, {});
  
      let rowsArray = [];
      let idCounter = 1;
  
      Object.keys(groupedTransactions)
        .sort((a, b) => new Date(b) - new Date(a))
        .forEach((date) => {
          rowsArray.push({
            id: `header-${date}`,
            date,
            isGroupHeader: true,
            amount: groupedTransactions[date].totalAmount?.toFixed(2) || '0.00',
            name: '', // Ensure name is set for group header
            category: '',
            bank: '',
          });
  
          groupedTransactions[date].transactions.forEach((transaction) => {
          const account = accounts.find(acc => acc.accountId === transaction.accountId);
  
  // Create new values, do NOT mutate transaction directly
  const accountName = account?.accountName || 'Unknown Bank';
  const accountMask = account?.accountMask;
            const isExpanded = expandedRows[transaction.id];
            rowsArray.push({
              id: idCounter++,
              transaction_id: transaction.id,
              date,
              name: transaction.description,
              category: transaction.category,
              amount: transaction.transactionAmount?.toFixed(2) || 'N/A',
     bank: accountMask ? `${accountName} ****${accountMask}` : accountName,
                  receipts: transaction.receiptUrl,
              reconcileId: transaction.reconcileId,
              categoryId: transaction.categoryId,
              subCategoryId: transaction.subCategoryId,   
              isExpanded,
            });
          });
        });
  
      console.log('Final rows built:', rowsArray.length, 'rows');
      return rowsArray;
    };
  
    setRows(buildRows());
  }, [transactions, reconciledTransactionsById, expandedRows]);
  
// Add this utility function somewhere in your component file
const formatCurrency = (value) => {
  if (value === undefined || value === null) return '$0';
  const roundedValue = Math.ceil(value); // Round UP to nearest whole number
  return `$${new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(roundedValue)}`;
};
if (initialLoading) {
    return <PaymentLoader />;
  }


  // Check if any filters are applied
  const hasActiveFilters = searchName ||
                          (searchDate && (searchDate.start || searchDate.end)) ||
                          selectedAccount ||
                          customText;

  // Only show "No transactions found, Retry" if there are truly no transactions AND no filters are applied
  if (!loading && !loadingAccounts && transactions.length === 0 && !hasActiveFilters) {

    return (
      <div
        className={`p-8 min-h-screen ${fontClass} ${colors.bg} transition-all duration-300`}
        style={{
          marginRight: showAIChat ? '384px' : '0',
          width: showAIChat ? 'calc(100% - 384px)' : '100%'
        }}
      >
        <FontStyles />
        <div className="relative z-20 mb-8 flex items-center justify-between">
          <h1 className={`text-3xl font-bold tracking-tight ${colors.text}`}>
            Transactions
          </h1>
        </div>
        <div className={`${colors.cardBg} rounded-2xl border ${colors.border} p-6 text-center`}>
          <p className={`${colors.textSecondary} text-lg`}>
            {error ? `Error: ${error}` : 'No transactions found.'}
          </p>
          <Button
            variant="contained"
            onClick={() => dispatch(fetchTransactionsStart())}
            sx={{ mt: 2, backgroundColor: colors.primary }}
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }
  // Additional data for the cards
const additionalData1 = [
  { 
    label: 'Total Transaction', 
    value: summary?.totalTransaction || '0',
    icon: FaMoneyBillWave,
    color: colors.accent1,
    bgColor: darkMode ? 'bg-lime-900/20' : 'bg-lime-50',
  },
  { 
    label: 'Total Debit', 
    value: formatCurrency(summary?.totalDebit),
    icon: FaArrowDown,
    color: darkMode ? 'from-red-500 to-red-600' : 'from-red-400 to-red-500',
    bgColor: darkMode ? 'bg-red-900/20' : 'bg-red-50',
  },
  { 
    label: 'Total Credit', 
    value: formatCurrency(summary?.totalCredit),
    icon: FaArrowUp,
    color: darkMode ? 'from-green-500 to-green-600' : 'from-green-400 to-green-500',
    bgColor: darkMode ? 'bg-green-900/20' : 'bg-green-50',
  },
];
const additionalData2 = [
  { 
    label: 'Total Amount', 
    value: formatCurrency(summary?.totalAmount),
    icon: FaDollarSign,
    color: colors.accent1,
    bgColor: darkMode ? 'bg-lime-900/20' : 'bg-lime-50',
  },
  { 
    label: 'Largest Transaction', 
    value: formatCurrency(summary?.largestTransaction),
    icon: FaTrophy,
    color: darkMode ? 'from-yellow-500 to-yellow-600' : 'from-yellow-400 to-yellow-500',
    bgColor: darkMode ? 'bg-yellow-900/20' : 'bg-yellow-50',
  },
  { 
    label: 'Dispute Transaction', 
    value: summary?.disputeTransaction || '0',
    icon: FaExclamationTriangle,
    color: darkMode ? 'from-orange-500 to-orange-600' : 'from-orange-400 to-orange-500',
    bgColor: darkMode ? 'bg-orange-900/20' : 'bg-orange-50',
  },
];
  const headerBaseClass = 'font-[Architects Daughter] text-lg';
  const headerBgClass = darkMode ? 'bg-gray-800 text-white' : 'bg-white text-black';
  // Right before your return statement add:
console.log('Selected Transaction:', selectedTransaction);
console.log('Available Accounts:', accounts);
console.log('Available Subcategories:', subcategories);
 return (
  <div
    className={`p-8 min-h-screen ${fontClass} relative overflow-hidden ${colors.bg} transition-all duration-300`}
    style={{
      marginRight: showAIChat ? '384px' : '0',
      width: showAIChat ? 'calc(100% - 384px)' : '100%'
    }}
  >
    <FontStyles />
    {/* Fixed Background Elements */}
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <div className={`absolute -top-40 -right-40 w-80 h-80 rounded-full bg-gradient-to-r ${colors.accent1} opacity-10`}></div>
      <div className={`absolute -bottom-40 -left-40 w-96 h-96 rounded-full bg-gradient-to-r ${colors.accent2} opacity-5`}></div>
    </div>
         
    {/* Header with Icon and Theme Selector */}
    <div className="relative z-20 mb-8 flex items-center justify-between">
      <div className="flex items-center gap-4">
        {/* Transaction Icon */}
        <div className={`
          p-4 rounded-2xl bg-gradient-to-r ${colors.accent1} 
          shadow-lg transform hover:scale-105 transition-all duration-300
          border-2 border-white/20
        `}>
          <AccountBalance className="text-white text-2xl" />
        </div>
        
        <h1 className={`
          flex items-center text-3xl font-bold tracking-tight
          ${colors.text}
        `}>
          Transactions
        </h1>
      </div>
      
      {/* Theme Selector and AI Chat Icon */}
      <div className="relative z-30 flex items-center gap-3">
        {/* AI Chat Icon */}
        <Tooltip title="Open AI Assistant" placement="top">
          <button
            className={`p-3 rounded-xl transition-all duration-200 hover:scale-110 ${
              darkMode
                ? "bg-gray-800/50 border-gray-700/50 hover:bg-gray-800/70"
                : "bg-white/70 border-white/50 hover:bg-white/90"
            } backdrop-blur-lg border shadow-lg`}
            onClick={handleAIChatToggle}
          >
            <svg
              className="w-6 h-6 text-purple-500"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M12 2L13.09 6.26L17.64 7.35L13.09 8.44L12 12.7L10.91 8.44L6.36 7.35L10.91 6.26L12 2Z" />
              <path d="M19.5 8.5L20.5 11L23 12L20.5 13L19.5 15.5L18.5 13L16 12L18.5 11L19.5 8.5Z" />
              <path d="M4.5 16.5L5.5 19L8 20L5.5 21L4.5 23.5L3.5 21L1 20L3.5 19L4.5 16.5Z" />
            </svg>
          </button>
        </Tooltip>
        
        <ThemeSelector
          currentTheme={currentTheme}
          currentFont={currentFont}
          darkMode={darkMode}
          onThemeChange={handleThemeChange}
          onFontChange={handleFontChange}
          colors={colors}
        />
      </div>
    </div>
 
    {/* Cards Container - Always Visible */}
    <div className="relative z-10 w-full mb-8">
      {/* Single row for all cards in 6 columns */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {additionalData1.map((item, index) => {
          const IconComponent = item.icon;
          return (
            <div
              key={`card-${index}`}
              className={`
                group relative overflow-hidden rounded-2xl ${colors.shadow} ${colors.shadowHover}
                transform hover:-translate-y-2 transition-all duration-300 ease-out cursor-pointer
                border ${colors.border} hover:border-lime-300 w-full
                ${item.bgColor} hover:bg-opacity-80 backdrop-blur-lg
              `}
            >
              {/* Top Decorative Bar */}
              <div className={`
                h-1 w-full bg-gradient-to-r ${item.color}
                transform scale-x-0 group-hover:scale-x-100 
                transition-transform duration-300 origin-left
              `}></div>
                         
              <div className="p-4 relative z-10">
                {/* Header with Icon and Label */}
                <div className="flex flex-col items-center mb-3">
                  <div className={`
                    p-2 rounded-lg bg-gradient-to-br ${item.color}
                    shadow-md transform group-hover:rotate-6 group-hover:scale-105
                    transition-all duration-300 mb-2
                  `}>
                    <IconComponent className="text-white text-lg" />
                  </div>
                  
                  {/* Label */}
                  <label className={`
                    text-xs font-bold uppercase tracking-wide text-center
                    ${colors.textSecondary}
                    group-hover:text-lime-600 transition-colors duration-300
                  `}>
                    {item.label}
                  </label>
                </div>
                             
                {/* Value - Centered */}
                <div className="text-center">
                  <p className={`
                    text-xl font-bold tracking-tight
                    ${colors.text}
                    group-hover:text-lime-700 transition-colors duration-300
                  `}>
                    {item.value}
                  </p>
                </div>
              </div>
            </div>
          );
        })}
        
        {additionalData2.map((item, index) => {
          const IconComponent = item.icon;
          return (
            <div
              key={`card-${index + 3}`}
              className={`
                group relative overflow-hidden rounded-2xl ${colors.shadow} ${colors.shadowHover}
                transform hover:-translate-y-2 transition-all duration-300 ease-out cursor-pointer
                border ${colors.border} hover:border-lime-300 w-full
                ${item.bgColor} hover:bg-opacity-80 backdrop-blur-lg
              `}
            >
              {/* Top Decorative Bar */}
              <div className={`
                h-1 w-full bg-gradient-to-r ${item.color}
                transform scale-x-0 group-hover:scale-x-100 
                transition-transform duration-300 origin-left
              `}></div>
                         
              <div className="p-4 relative z-10">
                {/* Header with Icon and Label */}
                <div className="flex flex-col items-center mb-3">
                  <div className={`
                    p-2 rounded-lg bg-gradient-to-br ${item.color}
                    shadow-md transform group-hover:rotate-6 group-hover:scale-105
                    transition-all duration-300 mb-2
                  `}>
                    <IconComponent className="text-white text-lg" />
                  </div>
                  
                  {/* Label */}
                  <label className={`
                    text-xs font-bold uppercase tracking-wide text-center
                    ${colors.textSecondary}
                    group-hover:text-lime-600 transition-colors duration-300
                  `}>
                    {item.label}
                  </label>
                </div>
                             
                {/* Value - Centered */}
                <div className="text-center">
                  <p className={`
                    text-xl font-bold tracking-tight
                    ${colors.text}
                    group-hover:text-lime-700 transition-colors duration-300
                  `}>
                    {item.value}
                  </p>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
<div className="relative z-10 w-full">
  {/* Tab Navigation */}
  <div className={`mb-6 ${colors.cardBg} rounded-xl border ${colors.border} ${colors.shadow}`}>
     <Tabs
        value={activeTab}
        onChange={(e, newValue) => setActiveTab(newValue)}
        aria-label="Transaction tabs"
        variant="fullWidth"
        sx={{
          '& .MuiTabs-indicator': {
            backgroundColor: colors.primary,
            height: 3
          },
          '& .MuiTab-root': {
            textTransform: 'none',
            fontWeight: 600,
            fontSize: '0.875rem',
            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
            '&.Mui-selected': {
              color: colors.primary,
            },
          }
        }}
      >
        <Tab
          label="Transactions"
          value="transactions"
          icon={<AccountBalanceIcon fontSize="small" />}
          iconPosition="start"
        />
        <Tab
          label="Reconcile"
          value="reconcile"
          icon={<RefreshIcon fontSize="small" />}
          iconPosition="start"
        />
      </Tabs>
  </div>
  {isReceiptModalOpen && (
        <ReceiptModal 
          receiptData={selectedReceipt} 
          onClose={handleCloseReceiptModal} 
          handleFieldDoubleClick={handleFieldDoubleClick}
        />
      )}
      <SplitTransactionPopup
        open={splitModalOpen}
        onClose={handleCloseSplitModal}
        transaction={selectedSplitTransaction}
        onSubmitSplit={handleSubmitSplit}
      />
       <CategoryChartModal categoryId={selectedCategoryId} />
       <ReceiptHandler/>
      {/* Filters */}
       {activeTab === 'transactions' ? (
    <>
<div className="flex-1 p-4 relative" style={{ overflow: 'visible', zIndex: 20 }}>
  {/* Search and Button Container - Fixed positioning */}
<div className="flex items-center justify-between gap-4 mb-6 w-full">    
  {/* Left side - Search and Accounts Dropdown */}
    <div className="flex items-center gap-4 flex-shrink-0 z-50">
      {/* Accounts Dropdown */}
      <div className="relative w-48">
        <select
          className={`
            ${colors.cardBg} ${colors.border} rounded-xl px-4 py-3 text-sm
            focus:outline-none focus:ring-2 focus:ring-lime-500 focus:border-transparent
            w-full transition-all duration-200 ${colors.text}
            shadow-sm hover:shadow-md appearance-none pr-8
          `}
           value={selectedAccount || ""}
          onChange={(e) => {
                  const selectedValue = e.target.value;
                  console.log('Selected account ID:', selectedValue);
      dispatch(setSelectedAccount(selectedValue || null));
      dispatch(setPage(0)); // Reset page on filter change
      dispatch(applyFilters());
     console.log("Selected account:", e.target.value);
          }}
          disabled={loadingAccounts}
        >
          <option value="">All Accounts</option>
          {accounts.map((account) => (
            <option key={account.accountId} value={account.accountId}>
              {`${account.accountName} (${account.accountMask})`}
            </option>
          ))}
        </select>
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <AccountBalanceWalletIcon 
            fontSize="small" 
            className={`${colors.textSecondary} ${loadingAccounts ? 'animate-pulse' : ''}`} 
          />
        </div>
        {loadingAccounts && (
          <div className="absolute inset-0 flex items-center justify-end pr-10">
            <CircularProgress size={16} />
          </div>
        )}
      </div>
  {/* Left side - Search */}
  <div className="flex-shrink-0 w-80">
    <div className="relative">
      <input
        type="text"
        placeholder="Search by description (e.g., KFC, Spotify)"
        className={`
          ${colors.cardBg} ${colors.border} rounded-xl px-4 py-3 text-sm
          focus:outline-none focus:ring-2 focus:ring-lime-500 focus:border-transparent
          w-full transition-all duration-200 ${colors.text}
          shadow-sm hover:shadow-md
        `}
        value={customText}
        onChange={(e) => {
          dispatch(setCustomSearchText(e.target.value));
          dispatch(setSelectedDateRange('custom'));
          dispatch(applyFilters());
        }}
      />
      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
        <svg className={`w-5 h-5 ${colors.textSecondary}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>
    </div>
  </div>
  </div>
  {/* Right side - Action buttons */}
<div className="flex flex-wrap items-center gap-3 flex-shrink-0 relative z-10">    {/* Add Transaction Button */}
   <Tooltip title="Add New Transaction" placement="top">
  <button
    style={{
      backgroundColor: themeConfig.primary,
      borderColor: themeConfig.primaryHover
    }}
    className={`
      text-white p-3 rounded-xl flex items-center
      transition-all duration-200 hover:scale-105 ${colors.shadowHover}
      border-2 hover:border-white/30
    `}
    onClick={handleOpenAddTransaction}
  >
    <AddCircleOutline fontSize="small" />
  </button>
</Tooltip>
<Tooltip title="Show Calendar View" placement="top">
  <button
    style={{
      backgroundColor: themeConfig.primary,
      borderColor: themeConfig.primaryHover,
    }}
    className={`
      text-white p-3 rounded-xl flex items-center
      transition-all duration-200 hover:scale-105 ${colors.shadowHover}
      border-2 hover:border-white/30
    `}
    onClick={() => setShowCalendarView(!showCalendarView)}
  >
    <CalendarTodayIcon fontSize="small" />
  </button>
</Tooltip>
{/* <div className="flex justify-start"> */}
  <Tooltip title={showHidden ? "Hide hidden transactions" : "Show hidden transactions"}>
    <button
      onClick={() => setShowHidden(prev => !prev)}
      className={`
        ${colors.buttonSecondary} ${colors.text} px-3 py-3 rounded-xl flex items-center 
        transition-all duration-200 ${colors.shadow} hover:${colors.shadowHover}
        border ${colors.borderLight} hover:scale-105
      `}
    >
      {showHidden ? <VisibilityOff fontSize="small" /> : <Visibility fontSize="small" />}
    </button>
  </Tooltip>
    {/* Hide Selected Button */}
     <Tooltip title="Hide Selected Transactions" placement="top">
  <button
    style={{
      backgroundColor: selectedTransactions.length === 0 || isTransactionProcessing ? 'gray' : themeConfig.primary,
      borderColor: selectedTransactions.length === 0 || isTransactionProcessing ? 'transparent' : themeConfig.primaryHover,
      cursor: selectedTransactions.length === 0 || isTransactionProcessing ? 'not-allowed' : 'pointer',
    }}
    className={`
      text-white p-3 rounded-xl flex items-center
      disabled:opacity-50 disabled:cursor-not-allowed
      transition-all duration-200 hover:scale-105 ${colors.shadowHover}
      border-2
    `}
    onClick={async () => {
      if (selectedTransactions.length === 0) return;

      if (window.confirm('Are you sure you want to hide the selected transactions?')) {
        try {
          setIsTransactionProcessing(true);
          dispatch({
            type: 'transactions/hideTransactions',
            payload: selectedTransactions,
          });
        } finally {
          setIsTransactionProcessing(false);
        }
      }
    }}
    disabled={selectedTransactions.length === 0 || isTransactionProcessing}
  >
    {isTransactionProcessing ? (
      <CircularProgress size={20} className="mr-2" />
    ) : (
      <VisibilityOff fontSize="small" />
    )}
  </button>
</Tooltip>

    {/* Hide from Budget Button */}
    <Tooltip title="Hide Selected Transactions from Budget" placement="top">
  <button
    style={{
      backgroundColor: selectedTransactions.length === 0 || isTransactionProcessing ? 'gray' : themeConfig.primary,
      borderColor: selectedTransactions.length === 0 || isTransactionProcessing ? 'transparent' : themeConfig.primaryHover,
      cursor: selectedTransactions.length === 0 || isTransactionProcessing ? 'not-allowed' : 'pointer',
    }}
    className={`
      text-white p-3 rounded-xl flex items-center
      disabled:opacity-50 disabled:cursor-not-allowed
      transition-all duration-200 hover:scale-105 ${colors.shadowHover}
      border-2
    `}
    onClick={async () => {
      if (selectedTransactions.length === 0) return;

      if (window.confirm('Are you sure you want to hide the selected transactions from budget?')) {
        try {
          setIsTransactionProcessing(true);
          dispatch({
            type: 'transactions/hideFromBudget',
            payload: selectedTransactions,
          });
        } finally {
          setIsTransactionProcessing(false);
        }
      }
    }}
    disabled={selectedTransactions.length === 0 || isTransactionProcessing}
  >
    {isTransactionProcessing ? (
      <CircularProgress size={20} className="mr-2" />
    ) : (
      <AccountBalanceWalletIcon fontSize="small" />
    )}
  </button>
</Tooltip>
    {/* Filter By Button */}
<div className="relative">
      <Tooltip title="Filter Transactions" placement="top">
        <div
          style={{
      backgroundColor: themeConfig.primary,
      borderColor: themeConfig.primaryHover
    }}
    className={`
      text-white p-3 rounded-xl flex items-center
      transition-all duration-200 hover:scale-105 ${colors.shadowHover}
      border-2 hover:border-white/30
    `}
          onClick={() => dispatch(toggleDateFilter())}
        >
          <FilterList fontSize="small" />
          
          {/* Date Filter Dropdown */}
          {dateFilterOpen && (
            <div
              ref={dateFilterRef}
              className={`
               absolute top-[calc(100%+8px)] right-0 ${colors.cardBg} ${colors.shadow} border ${colors.border}
          rounded-2xl overflow-hidden z-[1000] min-w-56 animate-in slide-in-from-top-2 duration-200
        `}
      >
              <div className="py-2">
                <div className={`px-4 py-3 text-xs font-semibold ${colors.textSecondary} uppercase tracking-wider border-b ${colors.borderLight}`}>
                  Date Range
                </div>
                
                 {[
  { value: 'all', label: 'All', color: 'blue' },
  { value: 'last7days', label: 'Last 7 Days', color: 'green' },
  { value: 'currentMonth', label: 'Current Month', color: 'purple' },
  { value: 'previousMonth', label:'Previous', color: 'orange' },
  { value: 'last6months', label: 'Last 6 Months', color: 'teal' },
  { value: 'custom', label: 'Custom Range', color: 'pink' },
].map((option) => {
  const isSelected = selectedDateRange === option.value; // <- Add this

  return (
    <button 
      key={option.value}
onClick={(e) => {
  e.stopPropagation(); // Prevent accidental dropdown toggle
  selectDateRange(option.value);
}}
      className={`
        w-full px-4 py-3 text-sm ${colors.text}
        ${isSelected ? `bg-${option.color}-100 text-${option.color}-700 font-semibold` : ''}
        hover:bg-${option.color}-50 hover:text-${option.color}-700 
        text-left transition-all duration-150 flex items-center gap-3 group
        ${darkMode && !isSelected ? `hover:bg-${option.color}-900/20 hover:text-${option.color}-400` : ''}
        ${darkMode && isSelected ? `bg-${option.color}-900/20 text-${option.color}-400` : ''}
      `}
    >
      <div className={`w-2 h-2 rounded-full ${isSelected ? `bg-${option.color}-600` : 'bg-gray-300'} group-hover:bg-${option.color}-500 transition-colors duration-150`}></div>
      <span className="font-medium">{option.label}</span>
    </button>
  );
})}

      {customFilterOpen && (
              <div className="px-4 py-3 border-t border-gray-200">
                <div className="flex flex-col gap-2">
                  <TextField
                    label="Start Date"
                    type="date"
                    size="small"
                    value={customDateRange.start || ''}
                    onChange={(e) => setCustomDateRange({ ...customDateRange, start: e.target.value })}
                    InputLabelProps={{ shrink: true }}
                    sx={{ '& .MuiInputBase-input': { fontSize: '14px' } }}
                    onClick={(e) => e.stopPropagation()} // Prevent dropdown toggle
                  />
                  <TextField
                    label="End Date"
                    type="date"
                    size="small"
                    value={customDateRange.end || ''}
                    onChange={(e) => setCustomDateRange({ ...customDateRange, end: e.target.value })}
                    InputLabelProps={{ shrink: true }}
                    sx={{ '& .MuiInputBase-input': { fontSize: '14px' } }}
                    onClick={(e) => e.stopPropagation()} // Prevent dropdown toggle
                  />
                  <div className="flex justify-end gap-2 mt-2">
                    <Button
                      size="small"
                      onClick={() => {
                        setCustomFilterOpen(false);
                        setCustomDateRange({ start: '', end: '' });
                        dispatch(setSelectedDateRange('all'));
                        dispatch(setSearchDate({ start: '', end: '' }));
                        dispatch(applyFilters());
                        dispatch(toggleDateFilter());
                      }}
                      sx={{ textTransform: 'none' }}
                    >
                      Cancel
                    </Button>
                    <Button
                      size="small"
                      variant="contained"
                      onClick={() => {
                        if (customDateRange.start && customDateRange.end) {
                          if (new Date(customDateRange.end) < new Date(customDateRange.start)) {
                            alert('End date cannot be before start date.');
                            return;
                          }
                          dispatch(setSearchDate({
                            start: customDateRange.start,
                            end: customDateRange.end
                          }));
                          dispatch(applyFilters());
                          setCustomFilterOpen(false);
                          dispatch(toggleDateFilter());
                        }
                      }}
                      disabled={!customDateRange.start || !customDateRange.end}
                      sx={{ textTransform: 'none', backgroundColor: '#8bc34a' }}
                    >
                      Apply
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  </Tooltip>
</div>
    {/* Upload Receipt Button */}
    <Tooltip title="Upload Receipt" placement="top">
      <button
       style={{
      backgroundColor: themeConfig.primary,
      borderColor: themeConfig.primaryHover
    }}
    className={`
      text-white p-3 rounded-xl flex items-center
      transition-all duration-200 hover:scale-105 ${colors.shadowHover}
      border-2 hover:border-white/30
    `}
        onClick={toggleReceiptModal}
      >
        <Receipt fontSize="small" />
      </button>
    </Tooltip>
    {/* Export Data Button with Dropdown */}
    <div className="relative">
      <Tooltip title="Export Transactions" placement="top">
        <button
          style={{
      backgroundColor: themeConfig.primary,
      borderColor: themeConfig.primaryHover
    }}
    className={`
      text-white p-3 rounded-xl flex items-center
      transition-all duration-200 hover:scale-105 ${colors.shadowHover}
      border-2 hover:border-white/30
    `}
          onClick={() => setExportDropdownOpen(!exportDropdownOpen)}
        >
          <Download fontSize="small" />
        </button>
      </Tooltip>
      {/* Export Options Dropdown */}
      {exportDropdownOpen && (
        <div
          ref={exportDropdownRef}
         className={`
             absolute top-[calc(100%+8px)] right-0 ${colors.cardBg} ${colors.shadow} border ${colors.border}
          rounded-2xl overflow-hidden z-[1000] min-w-48 animate-in slide-in-from-top-2 duration-200
        `}
      >
          <div className="py-2">
            <div className={`px-4 py-3 text-xs font-semibold ${colors.textSecondary} uppercase tracking-wider border-b ${colors.borderLight}`}>
              Export Format
            </div>
            
            {/* CSV Export Option */}
            <button 
             onClick={async () => {
  await exportToCSV();
  setExportDropdownOpen(false);
}}
              className={`
                w-full px-4 py-3 text-sm ${colors.text} hover:bg-green-50 hover:text-green-700 
                text-left transition-all duration-150 flex items-center gap-3 group
                ${darkMode ? 'hover:bg-green-900/20 hover:text-green-400' : ''}
              `}
            >
              <div className={`w-10 h-10 ${darkMode ? 'bg-green-900/30' : 'bg-green-100'} rounded-xl flex items-center justify-center group-hover:bg-green-200 transition-colors duration-150`}>
                <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                </svg>
              </div>
              <div>
                <div className="font-semibold">Export as CSV</div>
                <div className={`text-xs ${colors.textSecondary}`}>Spreadsheet format</div>
              </div>
            </button>
            
            {/* PDF Export Option */}
            <button 
              onClick={() => {
                exportToPDF();
                setExportDropdownOpen(false);
              }} 
              className={`
                w-full px-4 py-3 text-sm ${colors.text} hover:bg-red-50 hover:text-red-700 
                text-left transition-all duration-150 flex items-center gap-3 group
                ${darkMode ? 'hover:bg-red-900/20 hover:text-red-400' : ''}
              `}
            >
              <div className={`w-10 h-10 ${darkMode ? 'bg-red-900/30' : 'bg-red-100'} rounded-xl flex items-center justify-center group-hover:bg-red-200 transition-colors duration-150`}>
                <PictureAsPdf className="text-red-600" fontSize="small" />
              </div>
              <div>
                <div className="font-semibold">Export as PDF</div>
                <div className={`text-xs ${colors.textSecondary}`}>Document format</div>
              </div>
            </button>
          </div>
        </div>
      )}
    </div>
  </div>
</div>
</div>
   {/* Custom Transaction Table */}
  
    <div
      ref={containerRef}
      style={{ maxHeight: '80vh', overflowY: 'auto' }}
      // className="transaction-list-container border rounded"
    >
       {showCalendarView ? (
    <ErrorBoundary>
      <CalendarView
        transactions={transactions}
        colors={colors}
        darkMode={darkMode}
        fontClass={fontClass}
      />
    </ErrorBoundary>
  ) : (
<div className="w-full mt-1">
  <CustomTransactionTable
    rows={rows}
    selectedTransactions={selectedTransactions}
    expandedRows={expandedRows}
    reconciledTransactionsById={reconciledTransactionsById}
    receiptTransactionIds={receiptTransactionIds}
    colors={colors}
    darkMode={darkMode}
    fontClass={fontClass}
    dispatch={dispatch}
    handleRowClick={handleRowClick}
    handleReceiptClick={handleReceiptClick}
    handleSplitIconClick={handleSplitIconClick}
    setSelectedCategoryId={setSelectedCategoryId}
    toggleSelectTransaction={toggleSelectTransaction}
    selectAllTransactions={selectAllTransactions}
    deselectAllTransactions={deselectAllTransactions}
    setSelectedTransaction={setSelectedTransaction}
    setOpenModal={setOpenModal}
    fetchCategoryMonthlyExpensesStart={fetchCategoryMonthlyExpensesStart}
    setSelectedCategory={setSelectedCategory}
    setSelectedSubCategoryId={setSelectedSubCategoryId}
    setOpenCategoryModal={setOpenCategoryModal}
  />
</div>
)}
</div>
      
     
    {!showCalendarView && showHidden && hiddenTransactions.length > 0 && (
    <div className="mt-8 w-full">
    <div className="flex items-center mb-4">
      <h3 className={`text-xl font-semibold ${colors.text}`}>Hidden Transactions</h3>
      <span className={`ml-3 text-sm ${colors.textSecondary} ${colors.cardBg} px-3 py-1 rounded-full border ${colors.borderLight}`}>
        {hiddenTransactions.length}
      </span>
    </div>
    
    <HiddenTransactionsTable
      hiddenTransactions={hiddenTransactions}
      colors={colors}
      darkMode={darkMode}
      fontClass={fontClass}
      dispatch={dispatch}
      setSelectedTransaction={setSelectedTransaction}
      setOpenModal={setOpenModal}
    />
  </div>
)}
 {/* Pagination and Show Hidden button container */}
    {/* <div className="flex justify-between items-center mt-6">
      <div className="flex justify-start">
        <Tooltip title={showHidden ? "Hide hidden transactions" : "Show hidden transactions"}>
          <button
            onClick={() => setShowHidden(prev => !prev)}
            className={`
              ${colors.buttonSecondary} ${colors.text} px-6 py-3 rounded-xl flex items-center 
              transition-all duration-200 ${colors.shadow} hover:${colors.shadowHover}
              border ${colors.borderLight} hover:scale-105
            `}
          >
            {showHidden ? <VisibilityOff fontSize="small" /> : <Visibility fontSize="small" />}
            <span className="ml-2 text-sm font-semibold">
              {showHidden ? 'Hide' : 'Show'} Hidden ({hiddenTransactions?.length || 0})
            </span>
          </button>
        </Tooltip>
      </div>
</div> */}
    </>
  ) : (
    <Reconcile darkMode={darkMode} />
  )}
      {/* Pagination on the right */}
      {/* <div className="flex justify-end">
        <CustomPagination />
      </div>  */}
    </div>
      {/* Transaction Details Modal */}
{selectedTransaction && (
  <Dialog
    open={openModal}
    onClose={closeTransactionModal}
    fullWidth={true}
    maxWidth="sm"
    PaperProps={{
      style: {
        borderRadius: '24px',
        overflow: 'hidden',
        boxShadow: darkMode ? '0 25px 50px -12px rgba(0, 0, 0, 0.5)' : '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        border: 'none',
        backgroundColor: darkMode ? '#1f2937' : '#ffffff'
      }
    }}
  >
<DialogTitle
  sx={{
    background: `linear-gradient(135deg, ${themeConfig.primary} 0%, ${themeConfig.primaryHover} 100%)`,
    color: 'white',
    padding: '24px',
    fontWeight: '700',
    fontSize: '22px',
    position: 'relative',
    boxShadow: `0 8px 16px -4px ${themeConfig.primary}40`
  }}
>
  {/* Flex container for date & amount */}
  <div
    style={{
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingLeft: '48px',   
      paddingRight: '48px', 
    }}
  >
    {/* Left: Date */}
    <span style={{ fontSize: '14px', fontWeight: 500, opacity: 0.9 }}>
      {selectedTransaction?.date
        ? new Date(selectedTransaction.date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          })
        : ''}
    </span>
    {/* Right: Amount */}
    <span style={{ fontSize: '20px', fontWeight: 'bold' }}>
      {selectedTransaction?.amount !== undefined
        ? `$${Number(selectedTransaction.amount).toFixed(2)}`
        : 'Transaction Detail'}
    </span>
  </div>
  {/* Trash Icon - Top Left */}
  <IconButton
    aria-label="delete"
    onClick={() => {
      if (window.confirm('Are you sure you want to delete this transaction?')) {
        dispatch({
          type: 'transactions/deleteTransaction',
          payload: selectedTransaction.transaction_id
        });
        closeTransactionModal();
      }
    }}
    sx={{
      position: 'absolute',
      left: 16,
      top: 16,
      color: 'white',
      backgroundColor: 'rgba(255, 255, 255, 0.15)',
      borderRadius: '50%',
      padding: '8px',
      zIndex: 1,
      '&:hover': {
        backgroundColor: 'rgba(255, 255, 255, 0.25)',
        transform: 'scale(1.1)'
      },
      transition: 'all 0.3s ease-in-out'
    }}
  >
    <DeleteIcon fontSize="small" />
  </IconButton>
  {/* Close Icon - Top Right */}
  <IconButton
    aria-label="close"
    onClick={closeTransactionModal}
    sx={{
      position: 'absolute',
      right: 16,
      top: 16,
      color: 'white',
      backgroundColor: 'rgba(255, 255, 255, 0.15)',
      borderRadius: '50%',
      padding: '8px',
      zIndex: 1,
      '&:hover': {
        backgroundColor: 'rgba(255, 255, 255, 0.25)',
        transform: 'scale(1.1) rotate(90deg)'
      },
      transition: 'all 0.3s ease-in-out'
    }}
  >
    <CloseIcon fontSize="small" />
  </IconButton>
</DialogTitle>
    <DialogContent sx={{ 
      padding: '32px 24px', 
      backgroundColor: '#f8fafc'
    }}>
 <Tabs
            value={dialogTab}
            onChange={(e, newValue) => {
              if (newValue === 1) {
                if (!receiptTransactionIds.includes(selectedTransaction.transaction_id)) {
                  setShowEmbeddedReceiptUpload(true);
                  setDialogTab(newValue);
                } else {
                  setShowEmbeddedReceiptUpload(false);
                  setDialogTab(newValue);
                }
              } else {
                setShowEmbeddedReceiptUpload(false);
                setDialogTab(newValue);
              }
            }}
            indicatorColor="primary"
            textColor="primary"
            variant="fullWidth"
            sx={{ borderBottom: 1, borderColor: 'divider', backgroundColor: '#e6f4ea' }}
          >
    <Tab label="Details" />
 <Tab 
          label="Receipt" 
          // disabled={!receiptTransactionIds.includes(selectedTransaction.transaction_id) && !showEmbeddedReceiptUpload}
        />
            <Tab label="Split" />
  </Tabs>
   {dialogTab === 0 && (
     <div className="w-full space-y-4">
        {/* Date Field */}
       <div className="grid grid-cols-5 gap-4 items-center">
          <div className="flex items-center col-span-2">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <CalendarTodayIcon sx={{ fontSize: 18, color: '#1976d2' }} />
            </div>
            <span className="font-semibold text-gray-700 text-sm">Date</span>
          </div>
          <div className="col-span-3">
            <TextField
              type="date"
  value={formatDateForInput(formData.date)}
              onChange={(e) => handleFieldChange('date', e.target.value)}
              variant="outlined"
              size="small"
              fullWidth
              InputLabelProps={{
                shrink: true,
              }}
              sx={{ 
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                  backgroundColor: '#f8fafc',
                  borderColor: '#e2e8f0',
                  height: '36px',
                  '&:hover': {
                    borderColor: '#8bc34a'
                  },
                  '&.Mui-focused': {
                    borderColor: '#8bc34a'
                  }
                },
                '& .MuiInputBase-input': {
                  fontWeight: 500,
                  color: '#1e293b',
                  fontSize: '14px'
                }
              }}
            />
          </div>
        </div>

        {/* Description Field */}
        <div className="grid grid-cols-5 gap-4 items-center">
          <div className="flex items-center col-span-2">
            <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <ReceiptIcon sx={{ fontSize: 18, color: '#7b1fa2' }} />
            </div>
            <span className="font-semibold text-gray-700 text-sm">Description</span>
          </div>
          <div className="col-span-3">
            <TextField
value={formData.name || ''}
              onChange={(e) => handleFieldChange('name', e.target.value)}
              variant="outlined"
              size="small"
              placeholder="Enter description"
              fullWidth
              sx={{ 
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                  backgroundColor: '#f8fafc',
                  borderColor: '#e2e8f0',
                  height: '36px',
                  '&:hover': {
                    borderColor: '#7b1fa2'
                  },
                  '&.Mui-focused': {
                    borderColor: '#7b1fa2'
                  }
                },
                '& .MuiInputBase-input': {
                  fontWeight: 500,
                  color: '#1e293b',
                  fontSize: '14px'
                }
              }}
            />
          </div>
        </div>

       {/* Category Field */}
     <div className="grid grid-cols-5 gap-4 items-center">
          <div className="flex items-center col-span-2">
            <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <CategoryIcon sx={{ fontSize: 18, color: '#f57c00' }} />
            </div>
            <span className="font-semibold text-gray-700 text-sm">Category</span>
          </div>
          <div className="col-span-3">
      <select
  value={formData.subcategory || ''}
  onChange={(e) => handleFieldChange('subcategory', e.target.value)}
  className="..."
>
  <option value="">Select a category & subcategory</option>
  {categories.map((cat) => (
    <optgroup key={cat.id} label={cat.category}>
      {subcategories
        .filter((sub) => sub.categoryId === cat.id)
        .map((sub) => (
          <option key={sub.id} value={sub.subCategory.trim()}>
            {sub.subCategory}
          </option>
        ))}
    </optgroup>
  ))}
</select>


  </div>
</div>
       
        {/* Amount Field - Highlighted */}
        <div className="grid grid-cols-5 gap-4 items-center">
          <div className="flex items-center col-span-2">
            <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-sm">
              <AttachMoneyIcon sx={{ fontSize: 18, color: 'white' }} />
            </div>
            <span className="font-bold text-gray-800 text-sm">Amount</span>
          </div>
          <div className="col-span-3">
            <TextField
              type="number"
              value={formData.amount || ''}
              onChange={(e) => handleFieldChange('amount', e.target.value)}
              variant="outlined"
              size="small"
              fullWidth
              InputProps={{
                startAdornment: <InputAdornment position="start" sx={{ color: '#8bc34a', fontWeight: 'bold' }}>$</InputAdornment>,
              }}
              sx={{ 
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                  backgroundColor: 'white',
                  borderColor: '#8bc34a',
                  height: '36px',
                  '&:hover': {
                    borderColor: '#7cb342'
                  },
                  '&.Mui-focused': {
                    borderColor: '#7cb342'
                  }
                },
                '& .MuiInputBase-input': {
                  fontWeight: 'bold',
                  fontSize: '16px',
                  color: '#7cb342'
                }
              }}
            />
          </div>
        </div>

        {/* Tax Field */}
        <div className="grid grid-cols-5 gap-4 items-center">
          <div className="flex items-center col-span-2">
            <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <ReceiptLongIcon sx={{ fontSize: 18, color: '#f59e0b' }} />
            </div>
            <span className="font-semibold text-gray-700 text-sm">Tax</span>
          </div>
          <div className="col-span-3">
            <TextField
              type="number"
              value={formData.tax || ''}
              onChange={(e) => handleFieldChange('tax', e.target.value)}
              variant="outlined"
              size="small"
              placeholder="0.00"
              fullWidth
              InputProps={{
                startAdornment: <InputAdornment position="start" sx={{ color: '#f59e0b', fontWeight: 'bold' }}>$</InputAdornment>,
              }}
              sx={{ 
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                  backgroundColor: '#f8fafc',
                  borderColor: '#e2e8f0',
                  height: '36px',
                  '&:hover': {
                    borderColor: '#f59e0b'
                  },
                  '&.Mui-focused': {
                    borderColor: '#f59e0b'
                  }
                },
                '& .MuiInputBase-input': {
                  fontWeight: 500,
                  color: '#1e293b',
                  fontSize: '14px'
                }
              }}
            />
          </div>
        </div>

  {/* Account Field */}
    <div className="grid grid-cols-5 gap-4 items-center">
  <div className="flex items-center col-span-2">
    <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
      <AccountBalanceIcon sx={{ fontSize: 18, color: '#3f51b5' }} />
    </div>
    <span className="font-semibold text-gray-700 text-sm">Account</span>
  </div>

  <div className="col-span-3">
  <select
  value={formData.bank || ''}
  onChange={(e) => handleFieldChange('bank', e.target.value)}
  className="..."
>
  <option value="">Select an account</option>
  {accounts.map((account) => (
    <option key={account.accountId} value={account.accountId.toString()}>
      {account.accountName} ****{account.accountMask}
    </option>
  ))}
</select>


    {loadingAccounts && (
      <p className="text-sm text-gray-500">Loading accounts...</p>
    )}
  </div>
</div>
 {/* Tag Field - NEW */}
        <div className="grid grid-cols-5 gap-4 items-center">
          <div className="flex items-center col-span-2">
            <div className="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <LocalOfferIcon sx={{ fontSize: 18, color: '#e91e63' }} />
            </div>
            <span className="font-semibold text-gray-700 text-sm">Tag</span>
          </div>
          <div className="col-span-3">
            <TextField
              value={formData.tag || ''}
              onChange={(e) => handleFieldChange('tag', e.target.value)}
              variant="outlined"
              size="small"
              placeholder="Add tag"
              fullWidth
              sx={{ 
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                  backgroundColor: '#f8fafc',
                  borderColor: '#e2e8f0',
                  height: '36px',
                  '&:hover': {
                    borderColor: '#e91e63'
                  },
                  '&.Mui-focused': {
                    borderColor: '#e91e63'
                  }
                },
                '& .MuiInputBase-input': {
                  fontWeight: 500,
                  color: '#1e293b',
                  fontSize: '14px'
                }
              }}
            />
          </div>
        </div>


        {/* Notes Field */}
        <div className="grid grid-cols-5 gap-4 items-start">
          <div className="flex items-center col-span-2">
            <div className="w-10 h-10 bg-teal-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <NotesIcon sx={{ fontSize: 18, color: '#0d9488' }} />
            </div>
            <span className="font-semibold text-gray-700 text-sm">Notes</span>
          </div>
          <div className="col-span-3">
            <TextField
              value={formData.notes || ''}
              onChange={(e) => handleFieldChange('notes', e.target.value)}
              variant="outlined"
              size="small"
              placeholder="Add notes..."
              multiline
              rows={3}
              fullWidth
              sx={{ 
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                  backgroundColor: '#f8fafc',
                  borderColor: '#e2e8f0',
                  '&:hover': {
                    borderColor: '#0d9488'
                  },
                  '&.Mui-focused': {
                    borderColor: '#0d9488'
                  }
                },
                '& .MuiInputBase-input': {
                  fontWeight: 500,
                  color: '#1e293b',
                  fontSize: '14px'
                }
              }}
            />
          </div>
        </div>

        {/* Hide from Budget Checkbox - Moved to left */}
        <div className="flex items-center">
          <FormControlLabel
            control={
              <Checkbox
                checked={formData.hideFromBudget || false}
                onChange={(e) => handleFieldChange('hideFromBudget', e.target.checked)}
                sx={{
                  color: '#dc2626',
                  '&.Mui-checked': {
                    color: '#dc2626',
                  },
                  '& .MuiSvgIcon-root': {
                    fontSize: 20,
                  }
                }}
              />
            }
            label={
              <span className="text-sm text-gray-600 font-medium">
                Exclude from budget calculations
              </span>
            }
            sx={{
              margin: 0,
              '& .MuiFormControlLabel-label': {
                fontSize: '14px'
              }
            }}
          />
        </div>

        {/* Hide Transaction Checkbox - Moved to left */}
        <div className="flex items-center">
          <FormControlLabel
            control={
              <Checkbox
                checked={formData.hidden || false}
                onChange={(e) => handleFieldChange('hidden', e.target.checked)}
                sx={{
                  color: '#6b7280',
                  '&.Mui-checked': {
                    color: '#6b7280',
                  },
                  '& .MuiSvgIcon-root': {
                    fontSize: 20,
                  }
                }}
              />
            }
            label={
              <span className="text-sm text-gray-600 font-medium">
                Hide this transaction from view
              </span>
            }
            sx={{
              margin: 0,
              '& .MuiFormControlLabel-label': {
                fontSize: '14px'
              }
            }}
          />
        </div>
         <div className="pt-6 flex justify-center">
        <Button
          variant="contained"
          onClick={handleSaveChanges}
          startIcon={<SaveIcon />}
          sx={{
            textTransform: 'none',
            fontWeight: '600',
            borderRadius: '10px',
            padding: '12px 28px',
            fontSize: '15px',
            background: 'linear-gradient(135deg, #8bc34a 0%, #7cb342 100%)',
            boxShadow: '0 4px 14px 0 rgba(139, 195, 74, 0.3)',
            '&:hover': {
              boxShadow: '0 6px 20px 0 rgba(139, 195, 74, 0.4)',
              transform: 'translateY(-1px)',
              background: 'linear-gradient(135deg, #7cb342 0%, #689f38 100%)'
            },
            transition: 'all 0.2s ease-in-out'
          }}
        >
          Save Changes
        </Button>
      </div>
      </div>
      )}
      
      {/* Save Button - Centered */}
     
  {dialogTab === 1 && (
            <Box p={3}>
              {receiptTransactionIds.includes(selectedTransaction.transaction_id) ? (
                <ReceiptModal
                  receiptData={selectedReceipt}
                  onClose={() => setDialogTab(0)}
                  handleFieldDoubleClick={handleFieldDoubleClick}
                  isEmbedded
                />
              ) : (
                <ReceiptHandler
                  isEmbedded={true}
                  onClose={() => {
                    setShowEmbeddedReceiptUpload(false);
                    setDialogTab(0);
                  }}
                  onSave={() => {
                    dispatch(fetchReceiptTransactionIdsRequest());
                    setShowEmbeddedReceiptUpload(false);
                    setDialogTab(1);
                  }}
                />
              )}
            </Box>
          )}
{dialogTab  === 2 && (
    <Box p={3}>
      <SplitTransactionPopup
        open={true}
        onClose={() => setDialogTab(0)}
        transaction={selectedTransaction}
        onSubmitSplit={handleSubmitSplit}
        isEmbedded
      />
    </Box>
    )}
    </DialogContent>
  </Dialog>
)}
    </div>
  );
};

export default TransactionPage1;