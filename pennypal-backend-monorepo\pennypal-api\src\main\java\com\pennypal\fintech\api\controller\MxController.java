package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.service.MxService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/api/v1/mx")
@Tag(name = "MX Integration", description = "APIs for MX financial data aggregation service")
public class MxController {

    @Autowired
    private MxService mxService;

    @PostMapping("/user")
    @Operation(summary = "Create MX user",
               description = "Creates a new user in the MX platform for financial data aggregation")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully created MX user",
            content = @Content(mediaType = "text/plain")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid user ID",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public String createUser(
            @Parameter(description = "ID of the user to create in MX platform", required = true)
            @RequestParam Integer userId) {
        try {
            return mxService.createMxUser(userId);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @GetMapping("/connect-url")
    @Operation(summary = "Get MX connect widget URL",
               description = "Generates a connect widget URL for user to link their financial accounts through MX")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully generated connect widget URL",
            content = @Content(mediaType = "text/plain")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid user ID",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public String getConnectWidget(
            @Parameter(description = "ID of the user to generate connect widget for", required = true)
            @RequestParam Integer userId) {
        try {
            return mxService.getConnectWidgetUrl(userId);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @GetMapping("/accounts")
    @Operation(summary = "Get user accounts from MX",
               description = "Retrieves all financial accounts for a user from the MX platform")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved user accounts",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid user ID",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public String getAccounts(
            @Parameter(description = "ID of the user to get accounts for", required = true)
            @RequestParam Integer userId) {
        try {
            return mxService.getAccounts(userId);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @GetMapping("/transactions")
    @Operation(summary = "Get user transactions from MX",
               description = "Retrieves all financial transactions for a user from the MX platform")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved user transactions",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid user ID",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public String getTransactions(
            @Parameter(description = "ID of the user to get transactions for", required = true)
            @RequestParam Integer userId) {
        try {
            return mxService.getTransactions(userId);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}