package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.ProfilePictureUploadDto;
import com.pennypal.fintech.dto.ProfilePictureResponseDto;
import com.pennypal.fintech.service.ProfilePictureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.io.IOException;

@Slf4j
@RestController
@RequestMapping("/api/users")
@CrossOrigin(origins = "*")
@Tag(name = "Profile Picture Management", description = "APIs for managing user profile pictures and avatars")
public class ProfilePictureController {
    
    private final ProfilePictureService profilePictureService;
    
    @Autowired
    public ProfilePictureController(ProfilePictureService profilePictureService) {
        this.profilePictureService = profilePictureService;
    }
    
    @PostMapping(value = "/{userId}/profile-picture", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "Upload profile picture",
               description = "Uploads a new profile picture for the specified user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Profile picture uploaded successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ProfilePictureResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid file or user ID",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error during file upload",
            content = @Content
        )
    })
    public ResponseEntity<ProfilePictureResponseDto> uploadProfilePicture(
            @Parameter(description = "ID of the user to upload profile picture for", required = true)
            @PathVariable Integer userId,
            @Parameter(description = "Profile picture file to upload", required = true)
            @RequestParam("file") MultipartFile file) {
        
        log.info("Received request to upload profile picture for user: {}", userId);
        
        try {
            ProfilePictureResponseDto response = profilePictureService.uploadProfilePicture(userId, file);
            return ResponseEntity.ok(response);
        } catch (IOException e) {
            log.error("Error uploading profile picture for user {}: {}", userId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * Upload profile picture using DTO (for base64 data)
     * POST /api/users/profile-picture/upload
     */
    @Operation(summary = "Upload profile picture from DTO",
               description = "Uploads a new profile picture for the specified user using a data transfer object")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Profile picture uploaded successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ProfilePictureResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid DTO data",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error during file upload",
            content = @Content
        )
    })
    @PostMapping(value = "/profile-picture/upload", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ProfilePictureResponseDto> uploadProfilePictureFromDto(
            @Parameter(description = "Profile picture DTO to upload", required = true)
            @RequestBody ProfilePictureUploadDto dto) {
        
        log.info("Received request to upload profile picture from DTO for user: {}", dto.getUserId());
        
        ProfilePictureResponseDto response = profilePictureService.uploadProfilePictureFromDto(dto);
        return ResponseEntity.ok(response);
    }
    
    /**
     * Get profile picture data (returns the actual image)
     * GET /api/users/{userId}/profile-picture
     */
    @Operation(summary = "Get profile picture",
               description = "Retrieves the profile picture for the specified user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved profile picture",
            content = @Content(
                mediaType = "image/*",
                schema = @Schema(description = "Profile picture image data")
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Profile picture not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/{userId}/profile-picture")
    public ResponseEntity<byte[]> getProfilePicture(
        @Parameter(description = "ID of the user to get profile picture for", required = true)
        @PathVariable Integer userId) {
        
        log.info("Received request to get profile picture for user: {}", userId);
        
        byte[] imageData = profilePictureService.getProfilePictureData(userId);
        String contentType = profilePictureService.getContentType(userId);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType(contentType));
        headers.setContentLength(imageData.length);
        
        // Add cache headers for better performance
        headers.setCacheControl("public, max-age=3600"); // Cache for 1 hour
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(imageData);
    }
    
    /**
     * Get profile picture information (metadata only)
     * GET /api/users/{userId}/profile-picture/info
     */
    @Operation(summary = "Get profile picture information",
               description = "Retrieves metadata for the profile picture of the specified user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved profile picture information",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ProfilePictureResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Profile picture not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/{userId}/profile-picture/info")
    public ResponseEntity<ProfilePictureResponseDto> getProfilePictureInfo(
        @Parameter(description = "ID of the user to get profile picture information for", required = true)
        @PathVariable Integer userId) {
        
        log.info("Received request to get profile picture info for user: {}", userId);
        
        ProfilePictureResponseDto response = profilePictureService.getProfilePictureInfo(userId);
        return ResponseEntity.ok(response);
    }
    
    /**
     * Delete profile picture
     * DELETE /api/users/{userId}/profile-picture
     */
    @Operation(summary = "Delete profile picture",
               description = "Deletes the profile picture for the specified user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "204",
            description = "Profile picture deleted successfully",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Profile picture not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @DeleteMapping("/{userId}/profile-picture")
    public ResponseEntity<Void> deleteProfilePicture(
        @Parameter(description = "ID of the user to delete profile picture for", required = true)
        @PathVariable Integer userId) {
        
        log.info("Received request to delete profile picture for user: {}", userId);
        
        boolean deleted = profilePictureService.deleteProfilePicture(userId);
        
        if (deleted) {
            return ResponseEntity.noContent().build();
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * Update profile picture using multipart file
     * PUT /api/users/{userId}/profile-picture
     */
    @Operation(summary = "Update profile picture",
               description = "Updates the profile picture for the specified user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Profile picture updated successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ProfilePictureResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid file or user ID",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error during file upload",
            content = @Content
        )
    })
    @PutMapping(value = "/{userId}/profile-picture", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ProfilePictureResponseDto> updateProfilePicture(
            @Parameter(description = "ID of the user to update profile picture for", required = true)
            @PathVariable Integer userId,
            @Parameter(description = "Profile picture file to update", required = true)
            @RequestParam("file") MultipartFile file) {
        
        log.info("Received request to update profile picture for user: {}", userId);
        
        try {
            ProfilePictureResponseDto response = profilePictureService.uploadProfilePicture(userId, file);
            return ResponseEntity.ok(response);
        } catch (IOException e) {
            log.error("Error updating profile picture for user {}: {}", userId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * Update profile picture using DTO
     * PUT /api/users/{userId}/profile-picture/data
     */
    @Operation(summary = "Update profile picture from DTO",
               description = "Updates the profile picture for the specified user using a data transfer object")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Profile picture updated successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ProfilePictureResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid DTO data",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error during file upload",
            content = @Content
        )
    })
    @PutMapping(value = "/{userId}/profile-picture/data", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ProfilePictureResponseDto> updateProfilePictureFromDto(
            @Parameter(description = "ID of the user to update profile picture for", required = true)
            @PathVariable Integer userId,
            @Parameter(description = "Profile picture DTO to update", required = true)
            @RequestBody ProfilePictureUploadDto dto) {
        
        log.info("Received request to update profile picture from DTO for user: {}", userId);
        
        // Ensure the userId in path matches the DTO
        dto.setUserId(userId);
        
        ProfilePictureResponseDto response = profilePictureService.uploadProfilePictureFromDto(dto);
        return ResponseEntity.ok(response);
    }
    
    /**
     * Health check endpoint for profile picture service
     * GET /api/users/profile-picture/health
     */
    @GetMapping("/profile-picture/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("Profile Picture Service is running");
    }
}