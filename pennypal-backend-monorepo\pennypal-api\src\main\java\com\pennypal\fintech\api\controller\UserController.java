package com.pennypal.fintech.api.controller;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.http.HttpStatus;

import com.pennypal.fintech.dto.UserDto;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.service.UserService;
import com.pennypal.fintech.dto.UserPermissionDto;

import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@Controller
@RequestMapping("/api/v1/user")
public class UserController {

    @Autowired
    private final UserService  userService;

    public UserController(UserService userService) {
        this.userService = userService;
    }

    @GetMapping("/token")
    @Operation(summary = "Get access token",
               description = "Retrieves the current access token for the user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved access token",
            content = @Content(mediaType = "text/plain")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Error retrieving access token",
            content = @Content
        )
    })
    public String getAccessToken() throws Exception {
        return userService.getAccessToken();
    }

    @Operation(summary = "Update access token",
               description = "Updates the access token for the user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully updated access token",
            content = @Content(mediaType = "text/plain")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request data",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping ("/update/{emailId}/{accessToken}")
    public ResponseEntity<?> updateAccessToken(
        @Parameter(name = "emailId", description = "User's email ID", required = true)
        @PathVariable String emailId,
        @Parameter(name = "accessToken", description = "New access token", required = true)
        @PathVariable String accessToken) {

        boolean res =  userService.updateAccessToken(accessToken,emailId);
        String msg = "Updated";
        if(!res){
            msg = "Failed";
        }
        return ResponseEntity.ok(msg);
    }

    @Operation(summary = "Get user by email",
               description = "Retrieves user information by email ID")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved user information",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/{emailId}")
    public ResponseEntity<?> getUserByEmailId(
        @Parameter(name = "emailId", description = "User's email ID", required = true)
        @PathVariable String emailId) {
        return ResponseEntity.ok(userService.fetchUserByEmailId(emailId));
    }

  @Operation(summary = "Get user profile",
               description = "Retrieves user profile information by user ID")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved user profile",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/profile/{userId}")
    public ResponseEntity<?> getUserById(
        @Parameter(name = "userId", description = "User's ID", required = true)
        @PathVariable Integer userId) {
        try {
            UserDto user = userService.fetchUserById(userId);
            if (user != null) {
                return ResponseEntity.ok(user);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("Error fetching user profile: " + e.getMessage());
        }
    }

    @Operation(summary = "Update user profile",
               description = "Updates user profile information")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully updated user profile",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PutMapping("/profile/{id}")
    public ResponseEntity<?> updateUser(
        @Parameter(name = "id", description = "User's ID", required = true)
        @PathVariable Integer id,
        @Parameter(name = "userDto", description = "User profile data", required = true)
        @Valid @RequestBody UserDto userDto) {
        try {
            userDto.setId(id);                     // keep path & body in sync
            UserDto updated = userService.updateUser(userDto);
            return ResponseEntity.ok(updated);     // 200 OK with updated data
        } catch (EntityNotFoundException ex) {     // e.g. JPA repository .orElseThrow()
            return ResponseEntity
                    .status(HttpStatus.NOT_FOUND)  // 404 if id not found
                    .body("User not found: " + id);
        } catch (Exception ex) {
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR) // 500 for anything else
                    .body("Error updating user: " + ex.getMessage());
        }
    } 
 
    @Operation(summary = "Get user by ID",
               description = "Retrieves user information by user ID")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved user information",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/{id}")
    public ResponseEntity<String> getUser(
        @Parameter(name = "id", description = "User's ID", required = true)
        @PathVariable Integer id) {
        Optional<Users> user =  userService.findById(id);
        return ResponseEntity.ok("User ID: " + user.get().getEmailId());
    }

    @Operation(summary = "Save user",
               description = "Saves a new user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully saved user",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PostMapping("/save")
    public ResponseEntity<UserDto> saveUser(
        @Parameter(name = "userDto", description = "User data", required = true)
        @RequestBody UserDto userDto) {
        UserDto savedUserDto = userService.addUser(userDto);
        return ResponseEntity.ok(savedUserDto);
    }
    
    @Operation(summary = "Sign in user",
               description = "Signs in a user and returns user information")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully signed in user",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PostMapping("/signIn/{emaiIld}")
    public ResponseEntity<UserDto> signIn(
        @Parameter(name = "emailId", description = "User's email ID", required = true)
        @PathVariable String emailId) {
        UserDto savedUserDto = userService.fetchUserByEmailId(emailId);
        return ResponseEntity.ok(savedUserDto);
    }

	//   @PostMapping("/signin")
	//   public ResponseEntity<String> signIn(@RequestBody SignInRequestDto signInRequestDto   ) {
	//       boolean isAuthenticated = userService.signIn(signInRequestDto.getEmailId());
  
	//       if (isAuthenticated) {
	//           return ResponseEntity.ok("Sign In Successful!");
	//       } else {
	//           return ResponseEntity.status(404).body("User not found. Please sign up.");
	//       }
	//   }

    @Operation(summary = "Sign in user",
               description = "Signs in a user and returns user information")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully signed in user",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
	@PostMapping("/signin")
    public ResponseEntity<UserDto> signin(
        @Parameter(name = "userDto", description = "User data", required = true)
        @RequestBody UserDto userDto,
        @Parameter(name = "response", description = "HTTP response", required = true)
        @Valid @RequestBody HttpServletResponse response) {
        UserDto responseDto = userService.signInUser(userDto, response);
        // Add debugging to verify ID is still correct
        System.out.println("Controller returning user with ID: " + responseDto.getId());
        return ResponseEntity.ok(responseDto);
    }

    @Operation(summary = "Get user permissions",
               description = "Retrieves all permissions and access control settings for a specific user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved user permissions",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "User permissions response containing user_id and permissions list")
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/permissions/{userId}")
    public ResponseEntity<?> getUserPermissions(
        @Parameter(name = "userId", description = "User's ID", required = true)
        @PathVariable Integer userId) {
        // Fetch user entity by ID
        Users user = userService.findById(userId)
            .orElseThrow(() -> new RuntimeException("User not found"));
        // Get permissions for user (primary or secondary)
        List<UserPermissionDto> permissions = userService.getPermissionsForUser(user);
        return ResponseEntity.ok(permissions);
    }
}