package com.pennypal.fintech.dto;

import lombok.Data;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProfilePictureUploadDto {
    @NotNull(message = "User ID is required")
    private Integer userId;
    
    @NotBlank(message = "Filename is required")
    @Size(max = 255, message = "Filename cannot exceed 255 characters")
    private String filename;
    
    @NotBlank(message = "Content type is required")
    private String contentType;
    
    @NotNull(message = "File size is required")
    @Max(value = 5242880, message = "File size cannot exceed 5MB")
    private Long size;
    
    @NotNull(message = "File data is required")
    private byte[] data;
}