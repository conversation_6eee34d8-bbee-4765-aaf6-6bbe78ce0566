package com.pennypal.fintech.service;

import org.springframework.stereotype.Service;

import com.pennypal.fintech.dto.InvoiceLineItemDto;
import com.pennypal.fintech.dto.InvoiceUpcomingResponseDto;
import com.pennypal.fintech.dto.StripeInvoiceDto;
import com.pennypal.fintech.dto.StripeProductsDto;
import com.pennypal.fintech.entity.StripeCustomer;
import com.pennypal.fintech.entity.StripeSubscription;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.repository.StripeCustomerRepository;
import com.pennypal.fintech.repository.StripeInvoiceRepository;
import com.pennypal.fintech.repository.StripeProductRepository;
import com.pennypal.fintech.repository.StripeSubscriptionRepository;
import com.pennypal.fintech.repository.UserRepository;
import com.stripe.exception.StripeException;
import com.stripe.model.Customer;
import com.stripe.model.Discount;
import com.stripe.model.Invoice;
import com.stripe.model.InvoiceLineItem;
import com.stripe.param.CustomerCreateParams;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;

@Service
@Slf4j
public class PaymentService {

    @Autowired
    private StripeCustomerRepository stripeCustomerRepository;

    @Autowired
    private StripeInvoiceRepository stripeInvoiceRepository;

    @Autowired
    private StripeProductRepository stripeProductRepository;

    @Autowired
    private StripeSubscriptionRepository stripeSubscriptionRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CacheManager cacheManager;

    public String getOrCreateStripeCustomer(Integer userId) {
        log.info("Inside getOrCreateStripeCustomer in PaymentService");
        Users user = userRepository.findById(userId).
            orElseThrow(() -> new RuntimeException("User not found"));
        log.info("User: {}", user);

        StripeCustomer stripeCustomer = stripeCustomerRepository.findByUserId(userId);
        log.info("StripeCustomer: " + stripeCustomer);
        if (stripeCustomer == null) {
            // Create a new Stripe customer
            stripeCustomer = new StripeCustomer();
        } else {
            return stripeCustomer.getStripeCustomerId();
        }

        try {
            log.info("Creating Stripe customer through Stripe API");
            Customer customer = Customer.create(CustomerCreateParams.builder()
                .setName(user.getName())
                .setEmail(user.getEmailId())
                .build());

            log.info("Customer created: " + customer);
            log.info("Customer ID: {}" + customer.getId());

            stripeCustomer.setUserId(userId);
            stripeCustomer.setStripeCustomerId(customer.getId());
            stripeCustomer.setStatus("ACTIVE");
            stripeCustomer.setCreatedAt(LocalDateTime.now());
            stripeCustomer.setUpdatedAt(LocalDateTime.now());
            stripeCustomer.setEmail(user.getEmailId());
            stripeCustomer.setName(user.getName());
            
            stripeCustomerRepository.save(stripeCustomer);
            
            return customer.getId();

        } catch (Exception e) {
            log.error("Error creating Stripe customer: " + e.getMessage());
            throw new RuntimeException("Error creating Stripe customer: " + e.getMessage(), e);
        }
    }

    public String getStripeCustomerId(Integer userId) {
        log.info("Inside getStripeCustomerId in PaymentService");
        StripeCustomer stripeCustomer = stripeCustomerRepository.findByUserId(userId);
        if (stripeCustomer == null) {
            throw new RuntimeException("Stripe customer not found for user");
        }

        log.info("Stripe customer ID for userId {} is {}", userId, stripeCustomer.getStripeCustomerId());
        return stripeCustomer.getStripeCustomerId();
    }

    @Cacheable(value = "paymentCache",
               key = "#root.methodName + '_' + #userId",
               unless = "#result == null")
    public Map<String, String> getSubscriptionInfo(Integer userId) {
        log.info("Inside getSubscriptionInfo in PaymentService");
        StripeSubscription stripeSubscription = 
            stripeSubscriptionRepository.findByUserIdAndStatus(userId);
        
        if (stripeSubscription == null) {
            return Collections.emptyMap();
        }

        String cancelAt = stripeSubscription.getCancelAt() != null ? stripeSubscription.getCancelAt().toString() : "null";
        String canceledAt = stripeSubscription.getCanceledAt() != null ? stripeSubscription.getCanceledAt().toString() : "null";

        Long stripeBalance = stripeCustomerRepository.findByUserId(userId).getStripeBalance();
        if (stripeBalance != null) {
            stripeBalance = stripeBalance * -1;
        }

        Map<String, String> subscriptionInfo = new HashMap<>();

        subscriptionInfo.put("customerId", stripeSubscription.getCustomerId());
        subscriptionInfo.put("subscriptionId", stripeSubscription.getSubscriptionId());
        subscriptionInfo.put("priceId", stripeSubscription.getPriceId());
        subscriptionInfo.put("amount", stripeSubscription.getAmount().toString());
        subscriptionInfo.put("frequency", stripeSubscription.getFrequency());
        subscriptionInfo.put("status", stripeSubscription.getStatus());
        subscriptionInfo.put("startDate", stripeSubscription.getStartDate().toString());
        subscriptionInfo.put("cancelAt", cancelAt);
        subscriptionInfo.put("canceledAt", canceledAt);
        subscriptionInfo.put("currentPeriodStart", stripeSubscription.getCurrentPeriodStart().toString());
        subscriptionInfo.put("currentPeriodEnd", stripeSubscription.getCurrentPeriodEnd().toString());
        if (stripeBalance != null) {
            subscriptionInfo.put("stripeBalance", stripeBalance.toString());
        } else {
            subscriptionInfo.put("stripeBalance", "0");
        }

        return subscriptionInfo;
    }

    @Cacheable(value = "paymentCache",
               key = "#root.methodName",
               unless = "#result == null")
    public List<StripeProductsDto> fetchProducts() {
        log.info("Inside fetchProducts in PaymentService");
        List<StripeProductsDto> products = stripeProductRepository.findAll().stream()
            .map(product -> new StripeProductsDto(
                product.getProductId(),
                product.getPriceId(),
                product.getProductName(),
                product.getUnitPrice(),
                product.getFrequencyCharge()
            ))
            .collect(Collectors.toList());

        return products;
    }

    @Cacheable(value = "paymentCache",
               key = "#root.methodName + '_' + #userId",
               unless = "#result == null")
    public List<StripeInvoiceDto> fetchInvoices(Integer userId) {
        log.info("Inside fetchInvoices in PaymentService");
        List<StripeInvoiceDto> invoices = stripeInvoiceRepository.findByUserIdActiveSubscription(userId).stream()
            .map(invoice -> new StripeInvoiceDto(
                invoice.getUserId(),
                invoice.getCustomerId(),
                invoice.getSubscriptionId(),
                invoice.getInvoiceId(),
                BigDecimal.valueOf(invoice.getAmountPaid()).movePointLeft(2),
                invoice.getCurrency(),
                invoice.getBillingReason(),
                invoice.getCollectionMethod(),
                invoice.getCustomerEmail(),
                invoice.getCustomerName(),
                invoice.getCustomerPhone(),
                invoice.getInvoiceUrl(),
                invoice.getInvoicePdf(),
                invoice.getCreatedAt().toString()
            ))
            .collect(Collectors.toList());

        return invoices;
    }

    @Cacheable(value = "paymentCache",
               key = "#root.methodName + '_' + #customerId + '_' + #subscriptionId",
               unless = "#result == null")
    public InvoiceUpcomingResponseDto getUpcomingInvoice(String customerId, String subscriptionId) throws StripeException {
        log.info("Inside getUpcomingInvoice in PaymentService");
        log.info("customerId: " + customerId);
        log.info("subscriptionId: " + subscriptionId);

        Map<String, Object> params = new HashMap<>();
        params.put("customer", customerId);
        params.put("subscription", subscriptionId);

        Invoice invoice = Invoice.upcoming(params);
        log.info("Invoice: " + invoice);

        List<InvoiceLineItemDto> lineItems = new ArrayList<>();
        
        long lineItemTotal = 0;
        // Add regular line items
        for (InvoiceLineItem item : invoice.getLines().getData()) {
            String description = item.getDescription();
            long amount = item.getAmount();
            boolean isCredit = amount < 0;
            String formattedAmount = formatAmount(amount, invoice.getCurrency());
            lineItems.add(new InvoiceLineItemDto(description, formattedAmount, isCredit));
            lineItemTotal += amount;
        }

        // Check if there is a discount and add as a separate line item
        Discount discount = invoice.getDiscount();
        if (discount != null && discount.getCoupon() != null) {
            String discountDescription = "Discount: " + discount.getCoupon().getName();
            String formattedDiscountAmount = null;
            
            // Check if the discount is amount_off or percent_off
            if (discount.getCoupon().getAmountOff() != null) {
                // If amount_off exists, use it directly
                formattedDiscountAmount = "-" + formatAmount(discount.getCoupon().getAmountOff(), invoice.getCurrency());
            } else if (discount.getCoupon().getPercentOff() != null) {
                BigDecimal totalAmount = new BigDecimal(lineItemTotal);
                System.out.println("Total amount: " + totalAmount);
                BigDecimal discountAmount = totalAmount.multiply(discount.getCoupon().getPercentOff())
                                                       .divide(new BigDecimal(100));
                System.out.println("Discount amount: " + discountAmount);
                
                long discountAmountInCents = discountAmount.longValue();
                System.out.println("discountAmountInCents: " + discountAmountInCents);
                formattedDiscountAmount = "-" + formatAmount(discountAmountInCents, invoice.getCurrency());
                System.out.println("Formatted discount amount: " + formattedDiscountAmount);
            }
            
            if (formattedDiscountAmount != null) {
                lineItems.add(new InvoiceLineItemDto(discountDescription, formattedDiscountAmount, true));
            }
        }

        // Total amount
        String totalAmount = formatAmount(invoice.getTotal(), invoice.getCurrency());
        String currency = invoice.getCurrency().toUpperCase();

        // Next payment date
        String nextPaymentDate = formatTimestamp(invoice.getNextPaymentAttempt());

        InvoiceUpcomingResponseDto response = new InvoiceUpcomingResponseDto(lineItems, totalAmount, currency, nextPaymentDate);
        return response;
    }

    private String formatAmount(long amountInCents, String currency) {
        BigDecimal amount = BigDecimal.valueOf(amountInCents).divide(BigDecimal.valueOf(100));
        return amount.setScale(2, RoundingMode.HALF_UP) + " " + currency.toUpperCase();
    }

    private String formatTimestamp(long timestamp) {
        Instant instant = Instant.ofEpochSecond(timestamp);
        ZoneId zoneId = ZoneId.of("UTC");
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, zoneId);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dateTime.format(formatter);
    }

    // Cache clear mechanism
    public void clearPaymentRelatedCache(Integer userId) {
        // Fetch customer ID and subscription ID
        String customerId = getStripeCustomerId(userId);
        StripeSubscription stripeSubscription = stripeSubscriptionRepository.findByUserIdAndStatus(userId);
        String subscriptionId = stripeSubscription != null ? stripeSubscription.getSubscriptionId() : null;
        
        // Clear cache
        clearCacheByPatterns("paymentCache",
            "getSubscriptionInfo_" + userId,
            "fetchProducts",
            "fetchInvoices_" + userId,
            "getUpcomingInvoice_" + customerId + "_" + subscriptionId
        );
    }

    public void clearCacheByPatterns(String cacheName, String... patterns) {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
            if (cache != null && cache instanceof CaffeineCache caffeineCache) {
                com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache =
                    (com.github.benmanes.caffeine.cache.Cache<Object, Object>) caffeineCache.getNativeCache();
    
                // Remove all cache entries that match the provided pattern
                nativeCache.asMap().keySet().removeIf(key -> {
                    if (key instanceof String keyStr) {
                        for (String pattern : patterns) {
                            if (keyStr.startsWith(pattern)) {
                                return true;
                            }
                        }
                    }
                    return false;
                });
    
                log.info("Cleared cache entries matching pattern: {} from cache: {}",
                    patterns, cacheName);
            } else {
                log.warn("Cache '{}' not found or not a CaffeineCache instance", cacheName);
            }
        } catch (Exception e) {
            log.error("Error clearing cache '{}' with pattern: {}",
                cacheName, patterns, e);
        }
    }
}