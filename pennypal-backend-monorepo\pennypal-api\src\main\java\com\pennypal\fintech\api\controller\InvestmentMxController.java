package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.service.InvestmentMxService;
import com.pennypal.fintech.entity.Investment;
import com.pennypal.fintech.dto.InvestmentAggregationResponseDto;
import com.pennypal.fintech.entity.DailyInvestmentStock;
import com.pennypal.fintech.repository.InvestmentRepository;
import com.pennypal.fintech.repository.DailyInvestmentStockRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/investments")
@Slf4j
@Tag(name = "MX Investment Management", description = "APIs for managing investment data through MX integration")
public class InvestmentMxController {

    @Autowired
    private InvestmentMxService investmentMxService;
    
    @Autowired
    private InvestmentRepository investmentRepository;
    
    @Autowired
    private DailyInvestmentStockRepository dailyInvestmentStockRepository;

    /**
     * Sync investment holdings data from MX for a specific user
     * POST /api/investments/sync/{userId}
     */
    @PostMapping("/sync/{userId}")
    @Operation(summary = "Sync investment data from MX",
               description = "Synchronizes investment holdings data from MX platform for a specific user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully synced investment data",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid user ID or sync failed",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json")
        )
    })
    public ResponseEntity<Map<String, Object>> syncInvestmentData(
            @Parameter(description = "ID of the user to sync investment data for", required = true)
            @PathVariable Integer userId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("Received request to sync investment data for user ID: {}", userId);
            
            String result = investmentMxService.syncInvestmentData(userId);
            
            response.put("success", true);
            response.put("message", result);
            response.put("userId", userId);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            log.error("Invalid request for user ID: {}", userId, e);
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("userId", userId);
            return ResponseEntity.badRequest().body(response);
            
        } catch (Exception e) {
            log.error("Error syncing investment data for user ID: {}", userId, e);
            response.put("success", false);
            response.put("error", "Failed to sync investment data: " + e.getMessage());
            response.put("userId", userId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get all investments for a specific user
     * GET /api/investments/user/{userId}
     */
    @Operation(summary = "Get all investments for a user",
               description = "Retrieves all investment holdings for a specific user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved user investments",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/user/{userId}")
    public ResponseEntity<Map<String, Object>> getUserInvestments(
            @Parameter(description = "ID of the user to get investments for", required = true)
            @PathVariable Integer userId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("Fetching investments for user ID: {}", userId);
            
            List<Investment> investments = investmentRepository.findByUser_Id(userId);
            
            response.put("success", true);
            response.put("userId", userId);
            response.put("investments", investments);
            response.put("count", investments.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error fetching investments for user ID: {}", userId, e);
            response.put("success", false);
            response.put("error", "Failed to fetch investments: " + e.getMessage());
            response.put("userId", userId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get daily investment stock data for a specific user
     * GET /api/investments/daily/{userId}
     * Optional query parameters: date, ticker
     */
    @Operation(summary = "Get daily investment stock data",
               description = "Retrieves daily investment stock data for a user with optional date and ticker filters")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved daily investment data",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/daily/{userId}")
    public ResponseEntity<Map<String, Object>> getDailyInvestmentStocks(
            @Parameter(description = "ID of the user to get daily investment data for", required = true)
            @PathVariable Integer userId,
            @Parameter(description = "Specific date to filter by (optional)", required = false)
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @Parameter(description = "Specific ticker symbol to filter by (optional)", required = false)
            @RequestParam(required = false) String ticker) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("Fetching daily investment stocks for user ID: {}, date: {}, ticker: {}", userId, date, ticker);
            
            List<DailyInvestmentStock> dailyStocks;
            
            if (date != null && ticker != null) {
                // Filter by both date and ticker
                dailyStocks = dailyInvestmentStockRepository.findByUserIdAndDateAndTicker(userId, date, ticker);
            } else if (date != null) {
                // Filter by date only
                dailyStocks = dailyInvestmentStockRepository.findByUserIdAndDate(userId, date);
            } else if (ticker != null) {
                // Filter by ticker only
                dailyStocks = dailyInvestmentStockRepository.findByUserIdAndTicker(userId, ticker);
            } else {
                // Get all for user
                dailyStocks = dailyInvestmentStockRepository.findByUserId(userId);
            }
            
            response.put("success", true);
            response.put("userId", userId);
            response.put("dailyStocks", dailyStocks);
            response.put("count", dailyStocks.size());
            response.put("filters", Map.of(
                "date", date != null ? date.toString() : "all",
                "ticker", ticker != null ? ticker : "all"
            ));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error fetching daily investment stocks for user ID: {}", userId, e);
            response.put("success", false);
            response.put("error", "Failed to fetch daily investment stocks: " + e.getMessage());
            response.put("userId", userId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get investment portfolio summary for a user
     * GET /api/investments/portfolio/{userId}
     */
    @Operation(summary = "Get investment portfolio summary",
               description = "Retrieves a comprehensive portfolio summary for a user including total value and performance")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved portfolio summary",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/portfolio/{userId}")
    public ResponseEntity<Map<String, Object>> getPortfolioSummary(
            @Parameter(description = "ID of the user to get portfolio summary for", required = true)
            @PathVariable Integer userId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("Fetching portfolio summary for user ID: {}", userId);
            
            List<Investment> investments = investmentRepository.findByUser_Id(userId);
            
            // Calculate portfolio summary
            double totalValue = investments.stream()
                .mapToDouble(inv -> inv.getValue() != null ? inv.getValue() : 0.0)
                .sum();
                
            double totalCostBasis = investments.stream()
                .mapToDouble(inv -> inv.getCostBasis() != null ? inv.getCostBasis() : 0.0)
                .sum();
                
            double totalGainLoss = totalValue - totalCostBasis;
            double totalGainLossPercent = totalCostBasis > 0 ? (totalGainLoss / totalCostBasis) * 100 : 0.0;
            
            Map<String, Object> summary = new HashMap<>();
            summary.put("totalValue", totalValue);
            summary.put("totalCostBasis", totalCostBasis);
            summary.put("totalGainLoss", totalGainLoss);
            summary.put("totalGainLossPercent", totalGainLossPercent);
            summary.put("totalPositions", investments.size());
            
            response.put("success", true);
            response.put("userId", userId);
            response.put("portfolioSummary", summary);
            response.put("investments", investments);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error fetching portfolio summary for user ID: {}", userId, e);
            response.put("success", false);
            response.put("error", "Failed to fetch portfolio summary: " + e.getMessage());
            response.put("userId", userId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    // @GetMapping("/history/{userId}/{ticker}")
    // public ResponseEntity<Map<String, Object>> getStockHistory(
    //         @PathVariable Integer userId,
    //         @PathVariable String ticker,
    //         @RequestParam(defaultValue = "1M") String period) {

    //     try {
    //         Map<String, Object> history = investmentMxService.getStockHistory(userId, ticker, period);
    //         return ResponseEntity.ok(history);
    //     } catch (IllegalArgumentException e) {
    //         return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
    //     } catch (Exception e) {
    //         return ResponseEntity.internalServerError().body(Map.of("error", "Failed to fetch stock history: " + e.getMessage()));
    //     }
    // }
    
    // @GetMapping("/history/{userId}/{ticker}/custom")
    // public ResponseEntity<Map<String, Object>> getStockHistoryCustom(
    //         @PathVariable Integer userId,
    //         @PathVariable String ticker,
    //         @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fromDate,
    //         @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate toDate) {

    //     try {
    //         Map<String, Object> history = investmentMxService.getStockHistoryByDateRange(userId, ticker, fromDate, toDate);
    //         return ResponseEntity.ok(history);
    //     } catch (IllegalArgumentException e) {
    //         return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
    //     } catch (Exception e) {
    //         return ResponseEntity.internalServerError().body(Map.of("error", "Failed to fetch stock history: " + e.getMessage()));
    //     }
    // }

    // @GetMapping("/tickers/{userId}")
    // public ResponseEntity<List<String>> getUserStockTickers(@PathVariable Integer userId) {
    //     try {
    //         List<String> tickers = investmentMxService.getUserStockTickers(userId);
    //         return ResponseEntity.ok(tickers);
    //     } catch (Exception e) {
    //         return ResponseEntity.internalServerError().build();
    //     }
    // }
    /**
     * Get investment by ticker for a specific user
     * GET /api/investments/user/{userId}/ticker/{ticker}
     */
    @Operation(summary = "Get investment by ticker",
               description = "Retrieves investment details for a specific ticker symbol for a user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved investment by ticker",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/user/{userId}/ticker/{ticker}")
    public ResponseEntity<Map<String, Object>> getInvestmentByTicker(
            @Parameter(description = "ID of the user to get investment for", required = true)
            @PathVariable Integer userId,
            @Parameter(description = "Ticker symbol of the investment", required = true)
            @PathVariable String ticker) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("Fetching investment for user ID: {} and ticker: {}", userId, ticker);
            
            List<Investment> userInvestments = investmentRepository.findByUser_Id(userId);
            Investment investment = userInvestments.stream()
                .filter(inv -> ticker.equalsIgnoreCase(inv.getTicker()))
                .findFirst()
                .orElse(null);
            
            if (investment != null) {
                response.put("success", true);
                response.put("userId", userId);
                response.put("ticker", ticker);
                response.put("investment", investment);
            } else {
                response.put("success", false);
                response.put("message", "Investment not found for ticker: " + ticker);
                response.put("userId", userId);
                response.put("ticker", ticker);
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error fetching investment for user ID: {} and ticker: {}", userId, ticker, e);
            response.put("success", false);
            response.put("error", "Failed to fetch investment: " + e.getMessage());
            response.put("userId", userId);
            response.put("ticker", ticker);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Health check endpoint
     * GET /api/investments/health
     */
    @Operation(summary = "Health check",
               description = "Health check endpoint to verify the investment service is running")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Service is healthy",
            content = @Content(mediaType = "application/json")
        )
    })
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "Investment Service");
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }
    /**
     * Get aggregated data for a specific stock ticker
     */
    @Operation(summary = "Get aggregated stock data",
               description = "Retrieves aggregated data for a specific stock ticker over a specified time period")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved aggregated stock data",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/aggregated")
    public ResponseEntity<?> getStockAggregatedData(
            @Parameter(description = "ID of the user", required = true)
            @RequestParam Integer userId,
            @Parameter(description = "Stock ticker symbol", required = true)
            @RequestParam String ticker,
            @Parameter(description = "Number of months to aggregate", required = true)
            @RequestParam Integer months,
            @Parameter(description = "Interval in days for aggregation", required = true)
            @RequestParam Integer intervalDays) {
        Map<String, Object> result = investmentMxService.getStockAggregatedData(userId, ticker, months, intervalDays);
        return ResponseEntity.ok(result);
    }

    /**
     * Get aggregated data for all stocks for a user
     */
  @Operation(summary = "Get aggregated data for all stocks",
               description = "Retrieves aggregated data for all stocks owned by a user over a specified time period")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved aggregated data for all stocks",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/aggregated/all")
    public ResponseEntity<InvestmentAggregationResponseDto> getAllStockAggregatedData(
            @Parameter(description = "ID of the user", required = true)
            @RequestParam Integer userId,
            @Parameter(description = "Number of months to aggregate", required = true)
            @RequestParam Integer months,
            @Parameter(description = "Interval in days for aggregation", required = true)
            @RequestParam Integer intervalDays
        ) {
        InvestmentAggregationResponseDto result = investmentMxService.getAllStocksAggregatedData(userId, months, intervalDays);
        return ResponseEntity.ok(result);
    }

    /**
     * Compare current vs past portfolio performance
     */
    @Operation(summary = "Compare portfolio performance",
               description = "Compares current vs past portfolio performance over specified time periods")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved portfolio performance comparison",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/compare")
    public ResponseEntity<?> comparePortfolioPerformance(
            @Parameter(description = "ID of the user", required = true)
            @RequestParam Integer userId,
            @Parameter(description = "Number of months for current period", required = true)
            @RequestParam Integer currentPeriodMonths,
            @Parameter(description = "Number of months for past period", required = true)
            @RequestParam Integer pastPeriodMonths,
            @Parameter(description = "Interval in days for comparison", required = true)
            @RequestParam Integer intervalDays) {
        Map<String, Object> result = investmentMxService.getPortfolioPerformanceComparison(
                userId, currentPeriodMonths, pastPeriodMonths, intervalDays);
        return ResponseEntity.ok(result);
    }

    /**
     * Get stock performance summary (gain/loss) for a given period
     */
    @Operation(summary = "Get stock performance summary",
               description = "Retrieves stock performance summary including gains/losses for a specified date range")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved performance summary",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/performance-summary")
    public ResponseEntity<?> getPerformanceSummary(
            @Parameter(description = "ID of the user", required = true)
            @RequestParam Integer userId,
            @Parameter(description = "Start date for performance calculation", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "End date for performance calculation", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return ResponseEntity.ok(investmentMxService.getStockPerformanceSummary(userId, startDate, endDate));
    }
}