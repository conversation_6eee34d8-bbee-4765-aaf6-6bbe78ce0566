package com.pennypal.fintech.dto;

import java.time.LocalDateTime;

import lombok.Data;

@Data
public class TransactionDto {

    private Integer id;
    private LocalDateTime transactionDate;
    private LocalDateTime insertDateTime;
    private LocalDateTime updateDateTime;
    private String description;
    private Double transactionAmount;
    private Integer accountId;
    private Integer userId;
    private String category; 
    private String accountName;
    private String transactionId;
    private Double amount;
    private String merchantName;
    private LocalDateTime date;
 
    private String reconcileId;
    private String reconcileFlag;
    private Boolean removed;
    private Double tax;
    private String notes;
    private String tag;
    private Integer subCategoryId;
    private Integer categoryId;
    private boolean hideFromBudget; // New field
    private Integer customSubCategoryId;
   private boolean excludeFromBudget;

}