package com.pennypal.fintech.repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.pennypal.fintech.entity.Transactions;

import org.springframework.data.jpa.repository.Query;



@Repository
public interface TransactionRepository extends JpaRepository<Transactions, Integer>, TransactionCustomRepository {
    List<Transactions> findByTransactionDateAndTransactionAmount(LocalDateTime transactionDate, BigDecimal transactionAmount);
    List<Transactions> findByAccountIdAndTransactionDateAfter(Integer accountId, LocalDateTime lastRefreshTime);
    Set<String> findTransactionIdsByAccountId(@Param("accountId") Long accountId);
    List<Transactions> findByAccount_Id(Integer accountId);
    Optional<Transactions> findByTransactionId(String transactionId);
    List<Transactions> findByTransactionIdIn(List<String> transactionIds);
    void deleteByTransactionIdIn(List<String> transactionIds);
    // Optional: Keep a method that returns Optional if needed elsewhere
    @Query("SELECT t FROM Transactions t WHERE t.transactionId = :transactionId ORDER BY t.updateDateTime DESC")
    Optional<Transactions> findLatestByTransactionId(@Param("transactionId") String transactionId);
    List<Transactions> findAllByTransactionId(String transactionId);
    List<Transactions> findByUserId(Long userId);
    List<Transactions> findByAccountId(Integer accountId);
      List<Transactions> findByRemovedFalse();
    @Query("SELECT t FROM Transactions t WHERE t.removed = false")
    List<Transactions> findAllActive();
List<Transactions> findAllByRemovedTrueAndUserId(Long userId);
    List<Transactions> findByRemoveFalse();
List<Transactions> findByRemoveTrue();

List<Transactions> findByUserIdAndAccountIdAndReconcileFlagIsNull(int userId, int accountId);
// List<Transactions> findByMerchantNameAndAccountIdAndRemovedFalse(String merchantName, Integer accountId);
// Add this method to your TransactionRepository
List<Transactions> findByAccount_IdAndReconcileFlagIsNullOrReconcileFlagNotIgnoreCase(
    int accountId, String reconcileFlag);
    
    @Query("SELECT DISTINCT t.account.id FROM Transactions t WHERE " +
       "(t.reconcileFlag IS NULL OR t.reconcileFlag != 'yes') AND " +
       "(t.removed IS NULL OR t.removed = false)")
List<Integer> findDistinctAccountIdsWithUnreconciledTransactions();
    // List<Transactions> findByUserIdAndRemovedFalseAndRemoveFalse(Long userId);

    List<Transactions> findByUserIdAndRemovedFalseAndRemoveFalse(Long userId);
List<Transactions> findByDescriptionAndAccountIdAndRemovedFalse(String description, Integer accountId);

@Query("SELECT t FROM Transactions t LEFT JOIN FETCH t.account LEFT JOIN FETCH t.user " +
       "WHERE t.user.id = :userId AND t.removed = :removed AND t.remove = :remove")
Page<Transactions> findByUserIdAndRemovedAndRemove(@Param("userId") Long userId,
                                                   @Param("removed") boolean removed,
                                                   @Param("remove") boolean remove,
                                                   Pageable pageable);



@Query(value = """
        WITH FilteredTransactions AS (
            SELECT transaction_date, transaction_amount,
                   FLOOR(DATEDIFF(transaction_date, DATE_SUB(CURDATE(), INTERVAL :duration MONTH)) / :interval) AS group_number
            FROM transactions
            WHERE transaction_date >= DATE_SUB(CURDATE(), INTERVAL :duration MONTH) AND
                  transaction_amount > 0 AND
                  user_id = :userId
        ),
        GroupedExpenses AS (
            SELECT
                -- MAX(transaction_date) as interval_end_date,
                group_number,
                SUM(transaction_amount) as total_expense,
                COUNT(*) as transaction_count
            FROM FilteredTransactions
            GROUP BY group_number
            ORDER BY group_number
        )
        SELECT 
            -- DATE(interval_end_date) as end_date,
            DATE(DATE_ADD(DATE_SUB(CURDATE(), INTERVAL :duration MONTH), INTERVAL (group_number + 1) * :interval - 1 DAY)) AS end_date,
            ROUND(total_expense, 2) as total_expense,
            transaction_count
        FROM GroupedExpenses
    """, nativeQuery = true)
    List<Object[]> getAggregatedExpenses(Integer userId, Integer duration, Integer interval);

    @Query(value = """
        WITH FilteredTransactions AS (
            SELECT transaction_date, transaction_amount,
                   FLOOR(DATEDIFF(transaction_date, DATE_SUB(CURDATE(), INTERVAL :duration MONTH)) / :interval) AS group_number
            FROM transactions
            WHERE transaction_date >= DATE_SUB(CURDATE(), INTERVAL :duration MONTH) AND
                  transaction_amount < 0 AND
                  user_id = :userId
        ),
        GroupedExpenses AS (
            SELECT
                -- MAX(transaction_date) as interval_end_date,
                group_number,
                SUM(transaction_amount) as total_income,
                COUNT(*) as transaction_count
            FROM FilteredTransactions
            GROUP BY group_number
            ORDER BY group_number
        )
        SELECT 
            -- DATE(interval_end_date) as end_date,
            DATE(DATE_ADD(DATE_SUB(CURDATE(), INTERVAL :duration MONTH), INTERVAL (group_number + 1) * :interval - 1 DAY)) AS end_date,
            ROUND(total_income * -1, 2) AS total_income,
            transaction_count
        FROM GroupedExpenses
    """, nativeQuery = true)
    List<Object[]> getAggregatedIncome(Integer userId, Integer duration, Integer interval);

    @Query(value = """
        SELECT * FROM transactions t
        WHERE t.user_id = :userId AND
              LOWER(t.description) LIKE LOWER(:pattern)
    """, nativeQuery = true)
    List<Transactions> findByUserIdAndDescriptionLikePattern(
        @Param("userId") Integer userId,
        @Param("pattern") String pattern
    );

    @Query(value = """
        SELECT * FROM transactions t
        WHERE t.user_id = :userId AND
              t.description = :description
    """, nativeQuery = true)
    List<Transactions> findByUserIdAndDescription(
        @Param("userId") Integer userId,
        @Param("description") String description
    );

    List<Transactions> findByTransactionIdIn(Collection<String> transactionIds);
}
