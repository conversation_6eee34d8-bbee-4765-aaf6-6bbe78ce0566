package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.GoalDto;
import com.pennypal.fintech.service.GoalService;
import com.pennypal.fintech.exception.ResourceNotFoundException;
import com.pennypal.fintech.exception.InvalidRequestException;

import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

@RestController
@RequestMapping("/api/goals")
@Tag(name = "Goal Management", description = "APIs for managing financial goals and tracking progress")
public class GoalController {
    private static final Logger logger = LoggerFactory.getLogger(GoalController.class);

    private final GoalService goalService;

    @Autowired
    public GoalController(GoalService goalService) {
        this.goalService = goalService;
    }
   
    /**
     * Get a specific goal by ID
     */
    @GetMapping("/{goalId}")
    @Operation(summary = "Get goal by ID",
               description = "Retrieves a specific financial goal by its unique identifier")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved the goal",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Goal not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<GoalDto.GoalResponse> getGoal(
            @Parameter(description = "ID of the user", required = true)
            @RequestHeader("User-Id") Integer userId,
            @Parameter(description = "ID of the goal to retrieve", required = true)
            @PathVariable Integer goalId) {
        try {
            logger.info("Retrieving goal: {} for user: {}", goalId, userId);
            GoalDto.GoalResponse response = goalService.getGoal(userId, goalId);
            return ResponseEntity.ok(response);
        } catch (ResourceNotFoundException ex) {
            logger.error("Goal not found: {}", ex.getMessage());
            throw ex;
        } catch (Exception ex) {
            logger.error("Error retrieving goal: {}", ex.getMessage(), ex);
            throw new RuntimeException("Error retrieving goal. Please try again later.", ex);
        }
    }

    /**
     * Get all goals for a user
     */
    @GetMapping
    @Operation(summary = "Get all goals for user",
               description = "Retrieves all financial goals for a specific user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved all goals",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<List<GoalDto.GoalResponse>> getAllGoals(
            @Parameter(description = "ID of the user", required = true)
            @RequestHeader("User-Id") Integer userId) {
        try {
            logger.info("Retrieving all goals for user: {}", userId);
            List<GoalDto.GoalResponse> responses = goalService.getAllGoals(userId);
            logger.info("Retrieved {} goals for user: {}", responses.size(), userId);
            return ResponseEntity.ok(responses);
        } catch (ResourceNotFoundException ex) {
            logger.error("User not found: {}", ex.getMessage());
            throw ex;
        } catch (Exception ex) {
            logger.error("Error retrieving goals: {}", ex.getMessage(), ex);
            throw new RuntimeException("Error retrieving goals. Please try again later.", ex);
        }
    }

  @PostMapping("/create")
    @Operation(summary = "Create a new goal",
               description = "Creates a new financial goal with optional initial contributions")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "201",
            description = "Goal created successfully",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request data",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<GoalDto.GoalResponse> createGoal(
            @Parameter(description = "ID of the user", required = true)
            @RequestHeader("User-Id") Integer userId,
            @Parameter(description = "Goal creation request data", required = true)
            @Valid @RequestBody GoalDto.CreateGoalRequest request) {
        try {
            logger.info("Creating goal for user: {}, goalName: {}", userId, request.getGoalName());
            
            // Log initial contributions if provided
            if (request.getInitialContributions() != null && !request.getInitialContributions().isEmpty()) {
                logger.info("Creating goal with {} initial contributions", request.getInitialContributions().size());
                request.getInitialContributions().forEach(contribution -> 
                    logger.info("Account ID: {}, Initial Contribution: {}", 
                            contribution.getAccountId(), 
                            contribution.getAmount()));
            }
            
            GoalDto.GoalResponse response = goalService.createGoal(userId, request);
            logger.info("Goal created successfully with ID: {}", response.getId());
            
            return new ResponseEntity<>(response, HttpStatus.CREATED);
        } catch (ResourceNotFoundException | InvalidRequestException ex) {
            logger.error("Validation error in createGoal: {}", ex.getMessage());
            throw ex;
        } catch (Exception ex) {
            logger.error("Unexpected error creating goal: {}", ex.getMessage(), ex);
            throw new RuntimeException("Error creating goal. Please try again later.", ex);
        }
    }

@Operation(summary = "Update an existing goal",
               description = "Updates an existing financial goal with optional initial contributions")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Goal updated successfully",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request data",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Goal not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PutMapping("/{goalId}")
    public ResponseEntity<?> updateGoal(
            @Parameter(description = "ID of the user", required = true)
            @RequestHeader("User-Id") Integer userId,
            @Parameter(description = "ID of the goal to update", required = true)
            @PathVariable Integer goalId,
            @Parameter(description = "Goal update request data", required = true)
            @Valid @RequestBody GoalDto.UpdateGoalRequest request) {

        logger.info("Updating goal: {} for user: {}", goalId, userId);
        if (request.getInitialContributions() != null) {
            request.getInitialContributions().forEach(c ->
                logger.info("Account ID: {}, Amount: {}", c.getAccountId(), c.getAmount()));
        }

        GoalDto.GoalResponse response = goalService.updateGoal(userId, goalId, request);
        return ResponseEntity.ok(response);
    }

    /**
     * Contribute additional funds to an existing goal
     */
    @Operation(summary = "Contribute additional funds to an existing goal",
               description = "Contributes additional funds to an existing financial goal")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Contribution processed successfully",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request data",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User or goal not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PostMapping("/contribute")
    public ResponseEntity<GoalDto.GoalResponse> contributeToGoal(
            @Parameter(description = "ID of the user", required = true)
            @RequestHeader("User-Id") Integer userId,
            @Parameter(description = "Contribution request data", required = true)
            @Valid @RequestBody GoalDto.ContributeToGoalRequest request) {
        try {
            logger.info("Processing contribution - UserId: {}, GoalId: {}, Amount: {}, AccountId: {}", 
                       userId, request.getGoalId(), request.getAmount(), request.getAccountId());
            
            GoalDto.GoalResponse response = goalService.contributeToGoal(userId, request);
            logger.info("Contribution processed successfully for goal: {}", request.getGoalId());
            
            return ResponseEntity.ok(response);
        } catch (ResourceNotFoundException | InvalidRequestException ex) {
            logger.error("Validation error in contributeToGoal: {}", ex.getMessage());
            throw ex;
        } catch (Exception ex) {
            logger.error("Error processing contribution: {}", ex.getMessage(), ex);
            throw new RuntimeException("Error processing contribution. Please try again later.", ex);
        }
    }

    /**
     * Get goal account balance details
     */
    @Operation(summary = "Get goal account balance details",
               description = "Retrieves account balance details for a specific goal")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved goal account balance",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Goal not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/{goalId}/balance")
    public ResponseEntity<GoalDto.GoalAccountBalanceResponse> getGoalAccountBalance(
            @Parameter(description = "ID of the user", required = true)
            @RequestHeader("User-Id") Integer userId,
            @Parameter(description = "ID of the goal to retrieve account balance for", required = true)
            @PathVariable Integer goalId) {
        try {
            logger.info("Getting goal account balance for goal: {} and user: {}", goalId, userId);
            GoalDto.GoalAccountBalanceResponse response = goalService.getGoalAccountBalance(userId, goalId);
            return ResponseEntity.ok(response);
        } catch (ResourceNotFoundException ex) {
            logger.error("Resource not found in getGoalAccountBalance: {}", ex.getMessage());
            throw ex;
        } catch (Exception ex) {
            logger.error("Error retrieving goal account balance: {}", ex.getMessage(), ex);
            throw new RuntimeException("Error retrieving goal account balance. Please try again later.", ex);
        }
    }

    /**
     * Get account goal summary - shows how much of an account is allocated to goals
     */
    @Operation(summary = "Get account goal summary",
               description = "Retrieves a summary of how much of an account is allocated to goals")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved account goal summary",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request data",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User or account not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/account/{accountId}/summary")
    public ResponseEntity<GoalDto.AccountGoalSummaryResponse> getAccountGoalSummary(
            @Parameter(description = "ID of the user", required = true)
            @RequestHeader("User-Id") Integer userId,
            @Parameter(description = "ID of the account to get goal summary for", required = true)
            @PathVariable Integer accountId) {
        try {
            logger.info("Getting account goal summary for account: {} and user: {}", accountId, userId);
            GoalDto.AccountGoalSummaryResponse response = goalService.getAccountGoalSummary(userId, accountId);
            return ResponseEntity.ok(response);
        } catch (ResourceNotFoundException | InvalidRequestException ex) {
            logger.error("Validation error in getAccountGoalSummary: {}", ex.getMessage());
            throw ex;
        } catch (Exception ex) {
            logger.error("Error retrieving account goal summary: {}", ex.getMessage(), ex);
            throw new RuntimeException("Error retrieving account goal summary. Please try again later.", ex);
        }
    }
    
    /**
     * Get goals summary for a user
     */
    @Operation(summary = "Get goals summary for user",
               description = "Retrieves a summary of all financial goals for a specific user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved goals summary",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content(mediaType = "text/plain")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "text/plain")
        )
    })
    @GetMapping("/summary")
    public ResponseEntity<GoalDto.GoalSummaryResponse> getGoalsSummary(
            @Parameter(description = "ID of the user", required = true)
            @RequestHeader("User-Id") Integer userId) {
        try {
            logger.info("Getting goals summary for user: {}", userId);
            GoalDto.GoalSummaryResponse response = goalService.getGoalsSummary(userId);
            return ResponseEntity.ok(response);
        } catch (ResourceNotFoundException ex) {
            logger.error("User not found in getGoalsSummary: {}", ex.getMessage());
            throw ex;
        } catch (Exception ex) {
            logger.error("Error retrieving goals summary: {}", ex.getMessage(), ex);
            throw new RuntimeException("Error retrieving goals summary. Please try again later.", ex);
        }
    }

    /**
     * Get goals for a specific month
     */
    @Operation(summary = "Get goals for a specific month",
               description = "Retrieves all financial goals for a specific user for a given month")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved goals for the month",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid month parameter",
            content = @Content(mediaType = "text/plain")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content(mediaType = "text/plain")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "text/plain")
        )
    })
    @GetMapping("/month/{year}/{month}")
    public ResponseEntity<List<GoalDto.GoalResponse>> getGoalsForMonth(
            @Parameter(description = "ID of the user", required = true)
            @RequestHeader("User-Id") Integer userId,
            @Parameter(description = "Year for which to retrieve goals", required = true)
            @PathVariable int year,
            @Parameter(description = "Month for which to retrieve goals (1-12)", required = true)
            @PathVariable int month) {
        try {
            logger.info("Getting goals for month: {}/{} and user: {}", year, month, userId);
            
            // Validate month parameter
            if (month < 1 || month > 12) {
                throw new InvalidRequestException("Month must be between 1 and 12");
            }
            
            List<GoalDto.GoalResponse> responses = goalService.getGoalsForMonth(userId, year, month);
            logger.info("Retrieved {} goals for month {}/{} and user: {}", responses.size(), year, month, userId);
            
            return ResponseEntity.ok(responses);
        } catch (InvalidRequestException ex) {
            logger.error("Invalid request in getGoalsForMonth: {}", ex.getMessage());
            throw ex;
        } catch (ResourceNotFoundException ex) {
            logger.error("User not found in getGoalsForMonth: {}", ex.getMessage());
            throw ex;
        } catch (Exception ex) {
            logger.error("Error retrieving goals for month: {}", ex.getMessage(), ex);
            throw new RuntimeException("Error retrieving goals for month. Please try again later.", ex);
        }
    }
}