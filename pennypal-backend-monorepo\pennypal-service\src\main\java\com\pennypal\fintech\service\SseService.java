package com.pennypal.fintech.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

@Service
@Slf4j
public class SseService {
    
    // Map to store SSE connections by userId
    private final Map<Integer, CopyOnWriteArrayList<SseEmitter>> userConnections = new ConcurrentHashMap<>();
    
    /**
     * Create a new SSE connection for a user
     */
    public SseEmitter createConnection(Integer userId) {
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE); // No timeout
        
        // Add connection to user's connection list
        userConnections.computeIfAbsent(userId, k -> new CopyOnWriteArrayList<>()).add(emitter);
        
        // Handle connection cleanup
        emitter.onCompletion(() -> removeConnection(userId, emitter));
        emitter.onTimeout(() -> removeConnection(userId, emitter));
        emitter.onError((ex) -> {
            log.error("SSE error for user {}: {}", userId, ex.getMessage());
            removeConnection(userId, emitter);
        });
        
        log.info("SSE connection created for user: {}", userId);
        
        // Send initial connection confirmation
        try {
            emitter.send(SseEmitter.event()
                .name("connection")
                .data("Connected to PennyPal real-time updates"));
        } catch (IOException e) {
            log.error("Failed to send initial SSE message to user {}: {}", userId, e.getMessage());
            removeConnection(userId, emitter);
        }
        
        return emitter;
    }
    
    /**
     * Remove a specific connection for a user
     */
    private void removeConnection(Integer userId, SseEmitter emitter) {
        CopyOnWriteArrayList<SseEmitter> connections = userConnections.get(userId);
        if (connections != null) {
            connections.remove(emitter);
            if (connections.isEmpty()) {
                userConnections.remove(userId);
            }
        }
        log.info("SSE connection removed for user: {}", userId);
    }
    
    /**
     * Send an event to all connections for a specific user
     */
    public void sendEventToUser(Integer userId, String eventName, Object data) {
        CopyOnWriteArrayList<SseEmitter> connections = userConnections.get(userId);
        if (connections == null || connections.isEmpty()) {
            log.debug("No SSE connections found for user: {}", userId);
            return;
        }
        
        log.info("Sending SSE event '{}' to {} connections for user: {}", eventName, connections.size(), userId);
        
        // Send to all connections for this user
        connections.removeIf(emitter -> {
            try {
                emitter.send(SseEmitter.event()
                    .name(eventName)
                    .data(data));
                return false; // Keep this connection
            } catch (IOException e) {
                log.error("Failed to send SSE event to user {}: {}", userId, e.getMessage());
                return true; // Remove this connection
            }
        });
        
        // Clean up user entry if no connections remain
        if (connections.isEmpty()) {
            userConnections.remove(userId);
        }
    }
    
    /**
     * Check if a user has any active SSE connections
     */
    public boolean hasActiveConnections(Integer userId) {
        CopyOnWriteArrayList<SseEmitter> connections = userConnections.get(userId);
        return connections != null && !connections.isEmpty();
    }
    
    /**
     * Get the number of active connections for a user
     */
    public int getConnectionCount(Integer userId) {
        CopyOnWriteArrayList<SseEmitter> connections = userConnections.get(userId);
        return connections != null ? connections.size() : 0;
    }
    
    /**
     * Get total number of active connections across all users
     */
    public int getTotalConnectionCount() {
        return userConnections.values().stream()
            .mapToInt(CopyOnWriteArrayList::size)
            .sum();
    }
    
    /**
     * Send recurring transaction cache invalidation event
     */
    public void sendRecurringTransactionCacheInvalidation(Integer userId) {
        if (hasActiveConnections(userId)) {
            sendEventToUser(userId, "cache-invalidation", Map.of(
                "type", "recurring-transactions",
                "userId", userId,
                "timestamp", System.currentTimeMillis()
            ));
            log.info("Sent recurring transaction cache invalidation event to user: {}", userId);
        } else {
            log.debug("User {} not connected via SSE, skipping cache invalidation event", userId);
        }
    }

    /**
     * Send comprehensive account sync cache invalidation event
     * This invalidates multiple cache types when account sync completes
     */
    public void sendAccountSyncCacheInvalidation(Integer userId) {
        if (hasActiveConnections(userId)) {
            sendEventToUser(userId, "cache-invalidation", Map.of(
                "type", "account-sync",
                "userId", userId,
                "timestamp", System.currentTimeMillis(),
                "cacheTypes", List.of(
                    "accounts",
                    "transactions",
                    "budgets",
                    "subcategories",
                    "investments"
                )
            ));
            log.info("Sent account sync cache invalidation event to user: {}", userId);
        } else {
            log.debug("User {} not connected via SSE, skipping account sync cache invalidation event", userId);
        }
    }

    /**
     * Send specific cache type invalidation event
     */
    public void sendCacheInvalidation(Integer userId, String cacheType) {
        if (hasActiveConnections(userId)) {
            sendEventToUser(userId, "cache-invalidation", Map.of(
                "type", cacheType,
                "userId", userId,
                "timestamp", System.currentTimeMillis()
            ));
            log.info("Sent {} cache invalidation event to user: {}", cacheType, userId);
        } else {
            log.debug("User {} not connected via SSE, skipping {} cache invalidation event", userId, cacheType);
        }
    }

    /**
     * Send multiple cache types invalidation event
     */
    public void sendMultipleCacheInvalidation(Integer userId, List<String> cacheTypes) {
        if (hasActiveConnections(userId)) {
            sendEventToUser(userId, "cache-invalidation", Map.of(
                "type", "multiple-caches",
                "userId", userId,
                "timestamp", System.currentTimeMillis(),
                "cacheTypes", cacheTypes
            ));
            log.info("Sent multiple cache invalidation event to user: {} for caches: {}", userId, cacheTypes);
        } else {
            log.debug("User {} not connected via SSE, skipping multiple cache invalidation event", userId);
        }
    }

    /**
     * Send notification cache invalidation event after recurring transaction rules check
     */
    public void sendRecurringTransactionRulesNotificationInvalidation(Integer userId) {
        if (hasActiveConnections(userId)) {
            sendEventToUser(userId, "cache-invalidation", Map.of(
                "type", "notifications-recurring-rules",
                "userId", userId,
                "timestamp", System.currentTimeMillis(),
                "source", "recurring-transaction-rules-check"
            ));
            log.info("Sent recurring transaction rules notification cache invalidation event to user: {}", userId);
        } else {
            log.debug("User {} not connected via SSE, skipping recurring transaction rules notification cache invalidation event", userId);
        }
    }

    /**
     * Send notification cache invalidation event after budget rules check
     */
    public void sendBudgetRulesNotificationInvalidation(Integer userId) {
        if (hasActiveConnections(userId)) {
            sendEventToUser(userId, "cache-invalidation", Map.of(
                "type", "notifications-budget-rules",
                "userId", userId,
                "timestamp", System.currentTimeMillis(),
                "source", "budget-rules-check"
            ));
            log.info("Sent budget rules notification cache invalidation event to user: {}", userId);
        } else {
            log.debug("User {} not connected via SSE, skipping budget rules notification cache invalidation event", userId);
        }
    }

    /**
     * Send general notification cache invalidation event
     */
    public void sendNotificationCacheInvalidation(Integer userId, String source) {
        if (hasActiveConnections(userId)) {
            sendEventToUser(userId, "cache-invalidation", Map.of(
                "type", "notifications",
                "userId", userId,
                "timestamp", System.currentTimeMillis(),
                "source", source
            ));
            log.info("Sent notification cache invalidation event to user: {} from source: {}", userId, source);
        } else {
            log.debug("User {} not connected via SSE, skipping notification cache invalidation event from source: {}", userId, source);
        }
    }
}