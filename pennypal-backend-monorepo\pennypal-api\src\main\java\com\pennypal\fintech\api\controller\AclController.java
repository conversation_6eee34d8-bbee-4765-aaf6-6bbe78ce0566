package com.pennypal.fintech.api.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pennypal.fintech.dto.UserPermissionDto;
import com.pennypal.fintech.service.AclService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/api/v1/permissions")
@Tag(name = "Access Control", description = "APIs for managing user permissions and access control")
public class AclController {
    private final AclService aclService;

    public AclController(AclService aclService) {
        this.aclService = aclService;
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "Get user permissions",
               description = "Retrieves all permissions and access control settings for a specific user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved user permissions",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "User permissions response containing user_id and permissions list")
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<Map<String, Object>> getPermissions(
            @Parameter(description = "ID of the user to get permissions for", required = true)
            @PathVariable Integer userId) {
        List<UserPermissionDto> permissions = aclService.getPermissionsByUserId(userId);
        Map<String, Object> response = new HashMap<>();
        response.put("user_id", userId);
        response.put("permissions", permissions);
        return ResponseEntity.ok(response);
    }
}