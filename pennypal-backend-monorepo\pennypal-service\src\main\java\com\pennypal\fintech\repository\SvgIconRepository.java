package com.pennypal.fintech.repository;

import com.pennypal.fintech.entity.SvgIcon;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SvgIconRepository extends JpaRepository<SvgIcon, Integer> {
    
    Optional<SvgIcon> findByIconName(String iconName);
    
    Optional<SvgIcon> findByTickerSymbol(String tickerSymbol);
    
    Optional<SvgIcon> findByTickerSymbolIgnoreCase(String tickerSymbol);
}