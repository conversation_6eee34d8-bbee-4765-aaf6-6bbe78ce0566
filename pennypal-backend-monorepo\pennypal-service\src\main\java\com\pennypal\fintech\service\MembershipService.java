package com.pennypal.fintech.service;

import com.pennypal.fintech.dto.FamilyMemberDto;
import com.pennypal.fintech.dto.FamilyMemberInviteRequestDto;
import com.pennypal.fintech.dto.FamilyMemberInviteResponseDto;
import com.pennypal.fintech.dto.UserPermissionDto;
import com.pennypal.fintech.dto.ValidateInviteLinkResponseDto;
import com.pennypal.fintech.entity.UserRelationship;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.repository.MembershipRepository;
import com.pennypal.fintech.repository.UserRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class MembershipService {

    private final MembershipRepository membershipRepository;
    private final UserRepository userRepository;
    private final JavaMailSender emailSender;
    private final PasswordEncoder passwordEncoder;
    private final JWTService jwtService;
    private final AclService aclService;

    // Use a direct value instead of property injection to avoid configuration issues
    private final String frontendUrl = "http://localhost:5173";
    
    /**
     * Get all family members (secondary users) for the authenticated primary user
     * This includes both ACTIVE and PENDING relationships
     * @return List of FamilyMemberDto containing details of family members
     */
    public List<FamilyMemberDto> getFamilyMembers() {
        // Get the currently authenticated user
        String email = SecurityContextHolder.getContext().getAuthentication().getName();
        log.info("Fetching family members for user: {}", email);
        
        List<Users> users = userRepository.findByEmailId(email);
        
        if (users.isEmpty()) {
            log.error("Authenticated user not found: {}", email);
            throw new RuntimeException("Authenticated user not found");
        }
        
        Users authenticatedUser = users.get(0);
        
        // Check if the authenticated user is a primary user
        if (authenticatedUser.getIsPrimary() == null || !authenticatedUser.getIsPrimary()) {
            log.warn("Non-primary user {} attempted to view family members", email);
            throw new RuntimeException("Only primary users can view family members");
        }
        
        int primaryUserId = authenticatedUser.getId();
        log.info("Primary user {} is authorized to view family members", email);
        
        // Find all relationships where this user is the primary user and status is ACTIVE OR PENDING
        List<UserRelationship> relationships = membershipRepository.findAll().stream()
                .filter(r -> r.getPrimaryUserId() == primaryUserId)
                .filter(r -> "ACTIVE".equals(r.getStatus()) || "PENDING".equals(r.getStatus()))
                .collect(Collectors.toList());
        
        log.info("Found {} family member relationships for user ID: {}", relationships.size(), primaryUserId);
        
        List<FamilyMemberDto> familyMembers = new ArrayList<>();
        
        for (UserRelationship relationship : relationships) {
            FamilyMemberDto member = new FamilyMemberDto();
            
            // Set relationship details
            member.setRelationshipId(relationship.getId());
            member.setRelationshipType(relationship.getRelationshipType());
            member.setPermissionType(relationship.getPermissionType());
            member.setStatus(relationship.getStatus());
            member.setCreatedAt(relationship.getCreatedAt());
            
            // Set secondary user details if available (for ACTIVE members)
            if (relationship.getSecondaryUserId() != null) {
                Optional<Users> secondaryUserOpt = userRepository.findAll().stream()
                        .filter(u -> u.getId() == relationship.getSecondaryUserId())
                        .findFirst();
                
                if (secondaryUserOpt.isPresent()) {
                    Users secondaryUser = secondaryUserOpt.get();
                    member.setSecondaryUserId(secondaryUser.getId());
                    member.setName(secondaryUser.getName());
                    member.setEmailId(secondaryUser.getEmailId());
                    member.setPhoneNumber(secondaryUser.getPhoneNumber());
                } else {
                    // Fallback to email from relationship
                    member.setEmailId(relationship.getSecondaryUserEmail());
                }
            } else {
                // For PENDING invites, we only have the email
                member.setEmailId(relationship.getSecondaryUserEmail());
                // For pending invites, we can extract the name from the invite request if stored
                // For now, we'll show "Pending Invite" as name
                member.setName("Pending Invite");
            }
            
            familyMembers.add(member);
        }
        
        return familyMembers;
    }

    /**
     * Invite a family member - creates a PENDING relationship
     */
    public FamilyMemberInviteResponseDto inviteFamilyMember(FamilyMemberInviteRequestDto request) {
        if (request == null) {
            log.error("Invite request is null");
            throw new IllegalArgumentException("Request cannot be null");
        }
        
        if (request.getEmailId() == null || request.getEmailId().trim().isEmpty()) {
            log.error("Email ID is missing from request");
            throw new IllegalArgumentException("Email ID is required");
        }
        
        // Validate permission type
        if (request.getPermissionType() == null || request.getPermissionType().trim().isEmpty()) {
            log.error("Permission type is missing from request");
            throw new IllegalArgumentException("Permission type is required (READ_ONLY or WRITE)");
        }
        
        // Ensure permission type is valid
        String permissionType = request.getPermissionType().toUpperCase();
        if (!permissionType.equals("READ_ONLY") && !permissionType.equals("WRITE")) {
            log.error("Invalid permission type: {}", permissionType);
            throw new IllegalArgumentException("Permission type must be READ_ONLY or WRITE");
        }
        
        log.info("Processing invite request for email: {} with permission: {}", 
                request.getEmailId(), permissionType);
        
        // Get the currently authenticated user
        String email = SecurityContextHolder.getContext().getAuthentication().getName();
        log.info("Authenticated user email: {}", email);
        
        List<Users> users = userRepository.findByEmailId(email);
        
        if (users.isEmpty()) {
            log.error("Authenticated user not found: {}", email);
            throw new RuntimeException("Authenticated user not found");
        }
        
        Users authenticatedUser = users.get(0);
        
        // Check if the authenticated user is a primary user
        if (authenticatedUser.getIsPrimary() == null || !authenticatedUser.getIsPrimary()) {
            log.warn("Non-primary user {} attempted to invite family member", email);
            return FamilyMemberInviteResponseDto.builder()
                    .success(false)
                    .message("Only primary users can invite family members")
                    .build();
        }
        
        log.info("Primary user {} is authorized to invite family members", email);

        // Check if the email is already associated with another household
        List<UserRelationship> existingRelationships = membershipRepository.findAll().stream()
                .filter(r -> r.getSecondaryUserEmail() != null && 
                      r.getSecondaryUserEmail().equalsIgnoreCase(request.getEmailId()) &&
                      r.getPrimaryUserId() != authenticatedUser.getId() &&
                      (r.getStatus().equals("PENDING") || r.getStatus().equals("ACTIVE")))
                .collect(Collectors.toList());
        
        if (!existingRelationships.isEmpty()) {
            log.warn("Email {} is already associated with another household", request.getEmailId());
            return FamilyMemberInviteResponseDto.builder()
                    .success(false)
                    .message("This email is already part of another household or has a pending invitation")
                    .build();
        }

        // Check if this email belongs to an existing user who is already in another household
        List<Users> existingUsers = userRepository.findByEmailId(request.getEmailId());
        if (!existingUsers.isEmpty()) {
            // Check if this user already belongs to another household
            List<UserRelationship> userExistingRelationships = membershipRepository.findAll().stream()
                    .filter(r -> r.getSecondaryUserId() == existingUsers.get(0).getId() &&
                           r.getPrimaryUserId() != authenticatedUser.getId() &&
                           (r.getStatus().equals("PENDING") || r.getStatus().equals("ACTIVE")))
                    .collect(Collectors.toList());
        
            if (!userExistingRelationships.isEmpty()) {
                log.warn("User with email {} already belongs to another household", request.getEmailId());
                return FamilyMemberInviteResponseDto.builder()
                        .success(false)
                        .message("This user already belongs to another household")
                        .build();
            }
        }

        // Check if this email is already invited by this primary user
        List<UserRelationship> duplicateInvites = membershipRepository.findAll().stream()
                .filter(r -> r.getSecondaryUserEmail() != null && 
                      r.getSecondaryUserEmail().equalsIgnoreCase(request.getEmailId()) &&
                      r.getPrimaryUserId() == authenticatedUser.getId() &&
                      r.getStatus().equals("PENDING"))
                .collect(Collectors.toList());
        
        if (!duplicateInvites.isEmpty()) {
            log.warn("Email {} already has a pending invitation from this user", request.getEmailId());
            return FamilyMemberInviteResponseDto.builder()
                    .success(false)
                    .message("You have already sent an invitation to this email that is still pending")
                    .build();
        }

        // Create relationship with PENDING status
        UserRelationship relationship = new UserRelationship();
        relationship.setPrimaryUserId(authenticatedUser.getId());
        relationship.setSecondaryUserEmail(request.getEmailId());
        relationship.setRelationshipType(request.getRelationshipType());
        relationship.setPermissionType(permissionType);
        relationship.setStatus("PENDING"); // Always starts as PENDING
        
        // Generate a secure token that includes encrypted user info
        String token = generateInviteToken(authenticatedUser.getId(), request.getEmailId());
        relationship.setInviteToken(token);
        
        // Set expiration - 7 days from now
        relationship.setTokenExpiresAt(LocalDateTime.now().plusDays(7));
        relationship.setCreatedAt(LocalDateTime.now());
        relationship.setUpdatedAt(LocalDateTime.now());
        
        membershipRepository.save(relationship);
        log.info("PENDING relationship saved with token: {} and permission: {}", token, permissionType);
        
        // Send email
        try {
            sendInvitationEmail(request.getEmailId(), token, authenticatedUser.getName(), permissionType);
            log.info("Invitation email sent successfully");
        } catch (Exception e) {
            log.error("Failed to send invitation email", e);
            // Consider whether to fail the operation or just log the error
        }
        
        return FamilyMemberInviteResponseDto.builder()
                .success(true)
                .message("Invitation sent successfully")
                .build();
    }
    
    private String generateInviteToken(int primaryUserId, String email) {
        // Create a unique token that contains encrypted information
        String rawData = primaryUserId + ":" + email + ":" + UUID.randomUUID().toString();
        return Base64.getUrlEncoder().encodeToString(rawData.getBytes());
    }
    
    private void sendInvitationEmail(String email, String token, String primaryUserName, String permissionType) {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setTo(email);
        message.setSubject("Join PennyPal Family Plan");
        
        String inviteLink = frontendUrl + "/signup?token=" + token;
        
        String accessLevel = permissionType.equals("READ_ONLY") ? "view-only" : "full editing";
        
        message.setText("Hello,\n\n" +
                primaryUserName + " has invited you to join their PennyPal family plan with " + 
                accessLevel + " access. " +
                "Click the link below to accept the invitation and create your account:\n\n" +
                inviteLink + "\n\n" +
                "This link will expire in 7 days.\n\n" +
                "Thank you,\nPennyPal Team");
        
        emailSender.send(message);
    }
    
    public ValidateInviteLinkResponseDto validateInviteLink(String token) {
        try {
            log.info("Validating invite token: {}", token);
            
            // Find the relationship by token
            Optional<UserRelationship> relationshipOpt = membershipRepository.findAll().stream()
                    .filter(r -> r.getInviteToken().equals(token))
                    .findFirst();
            
            if (relationshipOpt.isEmpty()) {
                log.warn("No relationship found for token: {}", token);
                return ValidateInviteLinkResponseDto.builder()
                        .valid(false)
                        .message("Invalid invitation link")
                        .build();
            }
            
            UserRelationship relationship = relationshipOpt.get();
            
            // Check if token is expired
            if (relationship.getTokenExpiresAt().isBefore(LocalDateTime.now())) {
                log.warn("Token expired for relationship: {}", relationship.getId());
                return ValidateInviteLinkResponseDto.builder()
                        .valid(false)
                        .message("Invitation link has expired")
                        .build();
            }
            
            // Check if already used
            if (!"PENDING".equals(relationship.getStatus())) {
                log.warn("Token already used for relationship: {}", relationship.getId());
                return ValidateInviteLinkResponseDto.builder()
                        .valid(false)
                        .message("Invitation has already been used")
                        .build();
            }
            
            // Get primary user details
            List<Users> primaryUsers = userRepository.findAll().stream()
                    .filter(u -> u.getId() == relationship.getPrimaryUserId())
                    .toList();
                    
            if (primaryUsers.isEmpty()) {
                log.error("Primary user not found for id: {}", relationship.getPrimaryUserId());
                throw new RuntimeException("Primary user not found");
            }
            
            Users primaryUser = primaryUsers.get(0);
            log.info("Invitation validated successfully for email: {}", relationship.getSecondaryUserEmail());
            
            return ValidateInviteLinkResponseDto.builder()
                    .valid(true)
                    .message("Valid invitation link")
                    .primaryUserName(primaryUser.getName())
                    .emailId(relationship.getSecondaryUserEmail())
                    .relationshipType(relationship.getRelationshipType())
                    .permissionType(relationship.getPermissionType())
                    .build();
            
        } catch (Exception e) {
            log.error("Error validating invite link", e);
            return ValidateInviteLinkResponseDto.builder()
                    .valid(false)
                    .message("Invalid invitation link")
                    .build();
        }
    }
    
    /**
     * Complete signup and change relationship status from PENDING to ACTIVE
     */
    public Map<String, Object> completeSignup(String token, Map<String, String> signupDetails) {
        log.info("Completing signup for token: {}", token);
        
        // Validate token first
        ValidateInviteLinkResponseDto validation = validateInviteLink(token);
        if (!validation.isValid()) {
            log.error("Invalid token during signup completion: {}", token);
            throw new RuntimeException(validation.getMessage());
        }
        
        // Find the relationship
        UserRelationship relationship = membershipRepository.findAll().stream()
                .filter(r -> r.getInviteToken().equals(token))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Invalid invitation"));
        
        // Create new user - SET isPrimary to false for invited users
        Users newUser = new Users();
        newUser.setEmailId(relationship.getSecondaryUserEmail());
        newUser.setPassword(passwordEncoder.encode(signupDetails.get("password")));
        newUser.setName(signupDetails.get("name"));
        newUser.setPhoneNumber(signupDetails.get("phoneNumber"));
        newUser.setIsPrimary(false); // Set as secondary user (not primary)
        newUser.setInsertDateTime(LocalDateTime.now());
        newUser.setUpdateDateTime(LocalDateTime.now());
        
        Users savedUser = userRepository.save(newUser);
        log.info("New secondary user created with id: {} (isPrimary: false)", savedUser.getId());
        
        // Update relationship status from PENDING to ACTIVE
        relationship.setSecondaryUserId(savedUser.getId());
        relationship.setStatus("ACTIVE"); // Change status to ACTIVE
        relationship.setUpdatedAt(LocalDateTime.now());
        membershipRepository.save(relationship);
        log.info("Relationship updated from PENDING to ACTIVE with permission type: {}", relationship.getPermissionType());
        
        // Get user permissions
        List<UserPermissionDto> permissions = aclService.getPermissionsByUserId(savedUser.getId());

        // Generate JWT token for the new user
        String jwtToken = jwtService.generateToken(savedUser.getEmailId(), savedUser.getId(), permissions);
        
        return Map.of(
            "success", true,
            "message", "Account created successfully",
            "token", jwtToken,
            "userId", savedUser.getId(),
            "primaryUserId", relationship.getPrimaryUserId(),
            "permissionType", relationship.getPermissionType(),
            "isPrimary", false
        );
    }

    /**
     * Revokes access for a family member
     * Only primary users are allowed to revoke family member access
     * @param relationshipId The ID of the relationship to revoke
     * @return A response indicating success or failure
     */
    public Map<String, Object> revokeFamilyMember(int relationshipId) {
        // Get the currently authenticated user
        String email = SecurityContextHolder.getContext().getAuthentication().getName();
        log.info("Revoking family member with relationship ID: {} by user: {}", relationshipId, email);
        
        List<Users> users = userRepository.findByEmailId(email);
        
        if (users.isEmpty()) {
            log.error("Authenticated user not found: {}", email);
            throw new RuntimeException("Authenticated user not found");
        }
        
        Users authenticatedUser = users.get(0);
        
        // Check if the authenticated user is a primary user
        if (authenticatedUser.getIsPrimary() == null || !authenticatedUser.getIsPrimary()) {
            log.warn("Non-primary user {} attempted to revoke family member access", email);
            return Map.of(
                "success", false,
                "message", "Only primary users can revoke family member access"
            );
        }
        
        int primaryUserId = authenticatedUser.getId();
        log.info("Primary user {} is authorized to revoke access", email);
        
        // Find the relationship
        Optional<UserRelationship> relationshipOpt = membershipRepository.findAll().stream()
                .filter(r -> r.getId() == relationshipId && r.getPrimaryUserId() == primaryUserId)
                .findFirst();
        
        if (relationshipOpt.isEmpty()) {
            log.error("Relationship not found or user is not authorized to revoke it: {}", relationshipId);
            return Map.of(
                "success", false,
                "message", "Relationship not found or you are not authorized to revoke it"
            );
        }
        
        UserRelationship relationship = relationshipOpt.get();
        
        // Check if the relationship is already revoked or inactive
        if ("REVOKED".equals(relationship.getStatus()) || "INACTIVE".equals(relationship.getStatus())) {
            log.warn("Relationship {} is already revoked or inactive", relationshipId);
            return Map.of(
                "success", false,
                "message", "Family member access is already revoked"
            );
        }
        
        // Update the relationship status to REVOKED
        relationship.setStatus("REVOKED");
        relationship.setUpdatedAt(LocalDateTime.now());
        membershipRepository.save(relationship);
        
        log.info("Relationship {} successfully revoked by primary user {}", relationshipId, email);
        
        return Map.of(
            "success", true,
            "message", "Family member access revoked successfully"
        );
    }
}