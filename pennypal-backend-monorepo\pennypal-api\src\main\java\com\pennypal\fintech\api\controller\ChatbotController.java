package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.service.ChatbotService;

import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/api/v1/chatbot")
@RequiredArgsConstructor
@Tag(name = "AI Chatbot", description = "APIs for AI-powered financial chatbot functionality")
public class ChatbotController {

    private final ChatbotService chatbotService;
    
    private final RestTemplate restTemplate;

    @PostMapping("/query")
    @Operation(summary = "Query AI chatbot",
               description = "Sends a natural language query to the AI chatbot and returns SQL-based financial insights")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully processed AI query",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "AI response with SQL query and results")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request format",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error or AI service unavailable",
            content = @Content
        )
    })
    public ResponseEntity<?> queryAI(
            @Parameter(description = "Request containing user_id and user_query", required = true)
            @RequestBody Map<String, Object> request) {
        System.out.println("Querying AI with request: " + request);
        System.out.println("User ID: " + request.get("user_id"));
        System.out.println("User Query: " + request.get("user_query"));
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // Get previous queries of the user
        List<String> previousQueries = chatbotService.getPreviousQueries(Integer.parseInt((String) request.get("user_id")));

        Map<String, Object> body = new HashMap<>();
        body.put("user_query", request.get("user_query"));
        body.put("previous_queries", previousQueries);
        body.put("user_id", request.get("user_id"));

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(body, headers);

        ResponseEntity<String> response = restTemplate.postForEntity(
            "http://localhost:5000/generate-sql", entity, String.class
        );

        // Add to context
        chatbotService.addQuery(Integer.parseInt((String) request.get("user_id")), (String) request.get("user_query"));

        // Add to history
        Map<String, Object> modifiedResponse = chatbotService.addHistory(request, response);

        return ResponseEntity.ok(modifiedResponse);
    }

    @GetMapping("/history/{userId}")
    @Operation(summary = "Get user chatbot history",
               description = "Retrieves the conversation history for a specific user with the AI chatbot")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved chatbot history",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of previous chatbot interactions")
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<?> getHistory(
            @Parameter(description = "ID of the user to get chatbot history for", required = true)
            @PathVariable Integer userId) {
        System.out.println("Getting history for user: " + userId);
        System.out.println("User ID: " + userId);
        return ResponseEntity.ok(chatbotService.getHistory(userId));
    }
}