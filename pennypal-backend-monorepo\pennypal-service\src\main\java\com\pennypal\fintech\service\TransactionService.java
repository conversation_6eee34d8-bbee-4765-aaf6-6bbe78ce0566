package com.pennypal.fintech.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.pennypal.fintech.dto.FinicityTransaction;
import com.pennypal.fintech.dto.TransactionDto;
import com.pennypal.fintech.entity.Accounts;
import com.pennypal.fintech.entity.Reconcile;
import com.pennypal.fintech.entity.SubCategory;
import com.pennypal.fintech.entity.TransactionSummary;
import com.pennypal.fintech.entity.Transactions;
import com.pennypal.fintech.entity.Users;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.pennypal.fintech.repository.AccountRepository;
import com.pennypal.fintech.repository.ReconcileRepository;
import com.pennypal.fintech.repository.SubCategoryRepository;
import com.pennypal.fintech.repository.TransactionRepository;
import com.pennypal.fintech.repository.TransactionSummaryRepository;
import com.pennypal.fintech.repository.UserRepository;

import java.math.BigDecimal;
import java.sql.Date;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TransactionService {

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private ReconcileRepository reconcileRepository;
    
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private final ApplicationEventPublisher eventPublisher;

    @Autowired
    private TransactionSummaryRepository transactionSummaryRepository;

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private SubCategoryRepository subCategoryRepository;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private CacheManager cacheManager;

    public TransactionService(TransactionRepository transactionRepository,
                              UserRepository userRepository,
                              ApplicationEventPublisher eventPublisher,
                              TransactionSummaryRepository transactionSummaryRepository,
                              AccountRepository accountRepository,
                              SubCategoryRepository subCategoryRepository,
                              CategoryService categoryService,
                              CacheManager cacheManager) {
        this.transactionRepository = transactionRepository;
        this.userRepository = userRepository;
        this.eventPublisher = eventPublisher;
        this.transactionSummaryRepository = transactionSummaryRepository;
        this.accountRepository = accountRepository;
        this.subCategoryRepository = subCategoryRepository;
        this.categoryService = categoryService;
        this.cacheManager = cacheManager;
    }
    /* 
    public String getAccessTokenFromUserAccount(int userId) {
        List<UserAccount> listOfUserAccount = userAccountRepository.findByUserId(userId);

        UserAccount userAccount = listOfUserAccount != null && !listOfUserAccount.isEmpty() ? listOfUserAccount.get(0) : null;
        return userAccount != null ? userAccount.getAccessToken() : null;
    }*/
    
    /* 
    public TransactionsGetResponse getAllTransactions(int userId) throws Exception {
        // Fetch the access token for the given user
        String accessToken = getAccessTokenFromUserAccount(userId);
        
        if (accessToken == null) {
            throw new Exception("Access token not found for user " + userId);
        }

        // Use the tokenService to get transactions using the access token
        return tokenService.getTransactions(accessToken);
    }*/
    @Cacheable(value = "transactionCache", 
               key = "#root.methodName + '_' + #userId + '_' + #removed + '_' + #remove",
               unless = "#result == null")
    @Transactional(readOnly = true)
    public Page<TransactionDto> getTransactionsByUserId(Long userId, boolean removed, boolean remove, Pageable pageable) {
        Page<Transactions> transactions = transactionRepository.findByUserIdAndRemovedAndRemove(userId, removed, remove, pageable);
            return transactions.map(this::convertToDto);
    }

    public TransactionDto convertToDto(Transactions t) {
        TransactionDto dto = new TransactionDto();
        dto.setId(t.getId());
        dto.setTransactionDate(t.getTransactionDate());
        dto.setInsertDateTime(t.getInsertDateTime());
        dto.setUpdateDateTime(t.getUpdateDateTime());
        dto.setDescription(t.getDescription());
        dto.setTransactionAmount(t.getTransactionAmount());
        dto.setCategory(t.getCategory());  
        dto.setSubCategoryId(t.getSubCategoryId());
        dto.setReconcileId(t.getReconcileId());
        dto.setCategoryId(t.getCategoryId()); 

        dto.setRemoved(t.getRemoved());
        dto.setTax(t.getTax());
        dto.setNotes(t.getNotes());
        dto.setTag(t.getTag());
        
        if (t.getAccount() != null) {
            dto.setAccountName(t.getAccount().getAccountName());
            dto.setAccountId(t.getAccount().getId());
        }

        if (t.getUser() != null) {
            dto.setUserId(t.getUser().getId());
        }

        return dto;
    }

    public List<TransactionDto> getAllTransactions() {
        // First get only non-removed transactions
        List<Transactions> activeTransactions = transactionRepository.findByRemovedFalse();
        
        // Convert to DTOs
        List<TransactionDto> result = new ArrayList<>();
        
        for (Transactions t : activeTransactions) {
            TransactionDto dto = new TransactionDto();
            // Map all fields
            dto.setId(t.getId());
            dto.setTransactionDate(t.getTransactionDate());
            dto.setInsertDateTime(t.getInsertDateTime());
            dto.setUpdateDateTime(t.getUpdateDateTime());
            dto.setDescription(t.getDescription());
            dto.setTransactionAmount(t.getTransactionAmount());
            dto.setCategory(t.getCategory());
            dto.setSubCategoryId(t.getSubCategoryId());  
            dto.setReconcileId(t.getReconcileId());
            dto.setRemoved(t.getRemoved());
            dto.setTax(t.getTax());
            dto.setNotes(t.getNotes());
            
            // Handle account data
            if (t.getAccount() != null) {
                dto.setAccountName(t.getAccount().getAccountName());
                dto.setAccountId(t.getAccount().getId());
            }
            
            // Handle user data
            if (t.getUser() != null) {
                dto.setUserId(t.getUser().getId());
            }
            
            result.add(dto);
        }
        
        return result;
    }

    public void hideTransactionsFromBudget(List<Integer> transactionIds) {
        log.info("Hiding transactions from budget: {}", transactionIds);
        List<Transactions> transactions = transactionRepository.findAllById(transactionIds);
        if (transactions.isEmpty()) {
            throw new RuntimeException("No transactions found for the given IDs");
        }
        transactions.forEach(transaction -> {
            transaction.setHideFromBudget(true);
            transaction.setUpdateDateTime(LocalDateTime.now());
        });
        transactionRepository.saveAll(transactions);

        // Clear cache
        clearTransactionRelatedCache(transactions.get(0).getUser().getId());

        // Publish event for updated transactions
        List<TransactionDto> dtos = transactions.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
        eventPublisher.publishEvent(new TransactionEvent(this, dtos));
    }
    
    @Transactional
    public void saveTransaction(Transactions transaction) {
        log.info("Inside saveTransaction in TransactionService for description: {}, amount: {}, accountId: {}",
                transaction.getDescription(), transaction.getTransactionAmount(),
                transaction.getAccount() != null ? transaction.getAccount().getId() : "null");

        // Validate the account
        if (transaction.getAccount() == null || transaction.getAccount().getId() == 0) {
            log.error("Transaction has no valid account");
            throw new IllegalArgumentException("Transaction must have a valid account");
        }

        // Save the transaction
        Transactions savedTransaction = transactionRepository.save(transaction);
        log.info("Saved transaction with generated ID (transactionId): {}, reconcileFlag: {}, reconcileId: {}",
                savedTransaction.getId(), savedTransaction.getReconcileFlag(), savedTransaction.getReconcileId());

        // Optionally set transactionId = id (if you're using both)
        // savedTransaction.setTransactionId(String.valueOf(savedTransaction.getId()));

        // Convert saved entity to DTO
        TransactionDto dto = convertToDto(savedTransaction);
        log.info("Transaction saved and converted to DTO: {}", dto);

        // Clear cache
        clearTransactionRelatedCache(savedTransaction.getUser().getId());

        // Publish event for reconciliation
        eventPublisher.publishEvent(new TransactionEvent(this, List.of(dto)));
    }

    @Cacheable(value = "transactionCache", 
               key = "#root.methodName + '_' + #userId + '_' + #duration + '_' + #interval",
               unless = "#result == null")
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getAggregatedExpenses(
        Integer userId, Integer duration, Integer interval) {
            log.info("Inside getAggregatedExpenses in TransactionService for user: {} with duration: {} and interval: {}",
                userId, duration, interval);

            if (!userRepository.existsById(userId)) {
                log.error("User not found");
                throw new RuntimeException("User not found");
            }

            if (interval > duration * 30) {
                log.error("Interval cannot be larger than the total duration period");
                throw new IllegalArgumentException("Interval cannot be larger than the total duration period");
            }

            List<Object[]> results = transactionRepository.getAggregatedExpenses(userId, duration, interval);
            log.info("Raw data size: {}", results.size());
            
            return results.stream().map(
                row -> {
                    Map<String, Object> rowMap = new HashMap<>();
                    rowMap.put("endDate", row[0]);
                    rowMap.put("totalExpense", row[1]);
                    rowMap.put("transactionCount", row[2]);
                    return rowMap;
                }
            ).collect(Collectors.toList());
        }

    
    @Cacheable(value = "transactionCache", 
               key = "#root.methodName + '_' + #userId + '_' + #duration + '_' + #interval",
               unless = "#result == null")
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getAggregatedIncome(
        Integer userId, Integer duration, Integer interval) {
            log.info("Inside getAggregatedIncome in TransactionService for user: {} with duration: {} and interval: {}",
                userId, duration, interval);

            if (!userRepository.existsById(userId)) {
                log.error("User not found");
                throw new RuntimeException("User not found");
            }

            if (interval > duration * 30) {
                log.error("Interval cannot be larger than the total duration period");
                throw new IllegalArgumentException("Interval cannot be larger than the total duration period");
            }

            List<Object[]> results = transactionRepository.getAggregatedIncome(userId, duration, interval);
            log.info("Raw data size: {}", results.size());
            
            return results.stream().map(
                row -> {
                    Map<String, Object> rowMap = new HashMap<>();
                    rowMap.put("endDate", row[0]);
                    rowMap.put("totalIncome", row[1]);
                    rowMap.put("transactionCount", row[2]);
                    return rowMap;
                }
            ).collect(Collectors.toList());
        }

    public void hideTransactions(List<Integer> transactionIds) {
        List<Transactions> transactions = transactionRepository.findAllById(transactionIds);
        transactions.forEach(t -> t.setRemoved(true));
        transactionRepository.saveAll(transactions);

        // Clear cache
        clearTransactionRelatedCache(transactions.get(0).getUser().getId());
    }

    // Transaction summary

    public TransactionSummary createOrUpdateTransactionSummary(Long userId) {
        List<Transactions> transactions = transactionRepository.findByUserIdAndRemovedFalseAndRemoveFalse(userId);

        if (transactions.isEmpty()) {
            throw new IllegalStateException("No transactions found for user");
        }

        int totalTransactions = transactions.size();

        BigDecimal totalCredit = transactions.stream()
            .filter(t -> BigDecimal.valueOf(t.getTransactionAmount()).compareTo(BigDecimal.ZERO) > 0)
            .map(t -> BigDecimal.valueOf(t.getTransactionAmount()))
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalDebit = transactions.stream()
            .filter(t -> BigDecimal.valueOf(t.getTransactionAmount()).compareTo(BigDecimal.ZERO) < 0)
            .map(t -> BigDecimal.valueOf(t.getTransactionAmount()).abs())
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalAmount = totalCredit.add(totalDebit);

        BigDecimal largestTransaction = transactions.stream()
            .map(t -> BigDecimal.valueOf(t.getTransactionAmount()).abs())
            .max(BigDecimal::compareTo)
            .orElse(BigDecimal.ZERO);

        int disputeCount = (int) transactions.stream()
            .filter(t -> t.getReconcileId() != null && t.getReconcileId().equals("DISPUTE"))
            .count();

        LocalDateTime firstTransaction = transactions.stream()
            .map(Transactions::getTransactionDate)
            .min(LocalDateTime::compareTo)
            .orElse(null);

        LocalDateTime latestTransaction = transactions.stream()
            .map(Transactions::getTransactionDate)
            .max(LocalDateTime::compareTo)
            .orElse(null);

        TransactionSummary summary = transactionSummaryRepository.findByUserId(userId)
            .orElse(new TransactionSummary());

        summary.setUserId(userId);
        summary.setTotalTransaction(totalTransactions);
        summary.setTotalCredit(totalCredit);
        summary.setTotalDebit(totalDebit);
        summary.setTotalAmount(totalAmount);
        summary.setLargestTransaction(largestTransaction);
        summary.setDisputeTransaction(disputeCount);
        summary.setFirstTransactionDate(firstTransaction);
        summary.setLatestTransactionDate(latestTransaction);

        return transactionSummaryRepository.save(summary);
    }

    public void manuallyReconcileTransactions(List<Long> transactionIds) {
        // Convert List<Long> to List<Integer>
        List<Integer> intIds = transactionIds.stream()
            .map(Long::intValue)  // Convert each Long to Integer
            .collect(Collectors.toList());
        
        List<Transactions> transactions = transactionRepository.findAllById(intIds);
        
        // Group by potential matches (same description, opposite amounts)
        Map<String, List<Transactions>> potentialGroups = transactions.stream()
            .collect(Collectors.groupingBy(t -> t.getDescription().trim().toLowerCase()));
        
        for (List<Transactions> group : potentialGroups.values()) {
            if (group.size() >= 2) {
                // Find matching pairs
                for (int i = 0; i < group.size(); i++) {
                    for (int j = i + 1; j < group.size(); j++) {
                        Transactions t1 = group.get(i);
                        Transactions t2 = group.get(j);
                        
                        if (Math.abs(t1.getTransactionAmount() + t2.getTransactionAmount()) < 0.01) {
                            String reconcileId = UUID.randomUUID().toString();
                            
                            // Update transactions
                            t1.setReconcileFlag("yes");
                            t2.setReconcileFlag("yes");
                            t1.setReconcileId(reconcileId);
                            t2.setReconcileId(reconcileId);
                            t1.setHideFromBudget(true);  // Exclude from budget
                            t2.setHideFromBudget(true);  // Exclude from budget
                            
                            // Create reconcile records
                            createReconcileRecord(t1, reconcileId, true);
                            createReconcileRecord(t2, reconcileId, true);
                            
                            transactionRepository.saveAll(List.of(t1, t2));
                        }
                    }
                }
            }
        }

        // Clear cache
        clearTransactionRelatedCache(transactions.get(0).getUser().getId());
    }

    private void createReconcileRecord(Transactions tx, String reconcileId, boolean excludeFromBudget) {
        Reconcile reconcile = new Reconcile();
        reconcile.setTransactionDate(tx.getTransactionDate());
        reconcile.setDescription(tx.getDescription());
        reconcile.setCategory(tx.getCategory());
        reconcile.setAmount(tx.getTransactionAmount());
        reconcile.setReconcileId(reconcileId);
        reconcile.setReconcileFlag("yes");
        reconcile.setAccountName(tx.getAccount().getAccountName());
        reconcile.setExcludeFromBudget(excludeFromBudget);
        reconcileRepository.save(reconcile);
    }

    @Transactional
    public void syncFinicityTransactions(List<FinicityTransaction> finicityTransactions, Integer userId) {
        log.info("Syncing {} Finicity transactions for user ID: {}", 
            finicityTransactions != null ? finicityTransactions.size() : 0, userId);

        if (finicityTransactions == null || finicityTransactions.isEmpty()) {
            log.info("No transactions to sync for user ID: {}", userId);
            return;
        }
                
        Users user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));

        // List of categories from finicityTransactions
        Set<String> finicityCategories = finicityTransactions
            .stream()
            .map(txn -> txn.getCategorization() != null ? txn.getCategorization().getCategory() : null)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        log.info("Finicity categories: {}", finicityCategories);

        // Get category mappings
        Map<String, String> finalMappings = getCategoryMappings(finicityCategories);
        log.info("Final mappings: {}", finalMappings);

        // Collect all transaction IDs of new transactions
        Set<String> transactionIds = finicityTransactions
            .stream()
            .map(txn -> String.valueOf(txn.getId()))
            .collect(Collectors.toSet());
        log.info("Number of new transaction IDs: {}", transactionIds.size());

        // Fetch existing transactions
        Map<String, Transactions> existingTransactionsMap = transactionRepository
            .findByTransactionIdIn(transactionIds)
            .stream()
            .collect(Collectors.toMap(Transactions::getTransactionId, Function.identity()));
        log.info("Number of existing transactions: {}", existingTransactionsMap.size());

        // Collect all account IDs of new transactions
        Set<Long> accountIds = finicityTransactions
            .stream()
            .map(FinicityTransaction::getAccountId)
            .collect(Collectors.toSet());
        log.info("Number of new account IDs: {}", accountIds.size());

        // Fetch existing accounts
        Map<String, Accounts> accountsMap = accountRepository
            .findByFinictyAcctIdIn(accountIds)
            .stream()
            .collect(Collectors.toMap(Accounts::getPlaidUniqueNo, Function.identity()));
        log.info("Number of existing accounts: {}", accountsMap.size());

        Map<String, Integer> subCategoryIdMap = subCategoryRepository
            .findAll()
            .stream()
            .collect(Collectors.toMap(SubCategory::getSubCategory, SubCategory::getId, (a, b) -> a));
        log.info("Number of sub categories: {}", subCategoryIdMap.size());

        List<Transactions> transactionsToSave = new ArrayList<>();
        List<TransactionDto> newTransactionDtos = new ArrayList<>();

        for (FinicityTransaction finTxn : finicityTransactions) {
            try {
                // Skip transactions with invalid IDs
                if (finTxn.getId() == 0) {
                    log.warn("Skipping transaction with invalid ID: {}", finTxn);
                    continue;
                }

                String txnId = String.valueOf(finTxn.getId());
                
                boolean isNew = !existingTransactionsMap.containsKey(txnId);
                
                Transactions txn = isNew ? new Transactions() : existingTransactionsMap.get(txnId);

                mapFinicityToTransaction(finTxn, txn, finalMappings, subCategoryIdMap, accountsMap, user);

                if (txn.getAccount() == null) {
                    log.warn("Account not found for Finicity account ID: {}, skipping transaction", 
                        finTxn.getAccountId());
                    continue;
                }
                
                transactionsToSave.add(txn);
                
                if (isNew) {
                    newTransactionDtos.add(convertToDto(txn));
                }
            } catch (Exception e) {
                log.error("Error processing transaction ID {}: {}", 
                finTxn.getId(), e.getMessage(), e);
            }
        }

        try {
            if (!transactionsToSave.isEmpty()) {
                transactionRepository.saveAll(transactionsToSave);
                log.info("Saved/updated {} Finicity transactions", transactionsToSave.size());

                // Clear cache
                clearTransactionRelatedCache(userId);
            }
            
            // Publish events for new transactions if needed
            if (!newTransactionDtos.isEmpty()) {
                log.info("Publishing transaction event with {} new transactions", newTransactionDtos.size());
                eventPublisher.publishEvent(new TransactionEvent(this, newTransactionDtos));
            }
        } catch (Exception e) {
            log.error("Error saving transactions batch: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to save transactions", e);
        }
    }

    private Map<String, String> getCategoryMappings(Set<String> finicityCategories) {
        if (finicityCategories.isEmpty()) {
            return Collections.emptyMap();
        }
        
        // Step 1: Fetch matching categories from sub_category table
        List<Object[]> categoryMappingsList = subCategoryRepository.fetchSubCategoryMappings(
            new ArrayList<String>(finicityCategories));
        log.info("categoryMappingsList: {}", categoryMappingsList);

        Map<String, String> dbCategoryMappings = categoryMappingsList.stream()
            .collect(Collectors.toMap(
                row -> (String) row[0],  // finicity_category as key
                row -> (String) row[1],  // sub_category as value
                (existing, replacement) -> existing  // In case of duplicate keys, keep the existing value
            ));
        log.info("dbCategoryMappings: {}", dbCategoryMappings);
        
        // Step 2: Get unmatched categories
        Set<String> unmatchedCategories = finicityCategories.stream()
            .filter(cat -> !dbCategoryMappings.containsKey(cat))
            .collect(Collectors.toSet());
        log.info("unmatchedCategories: {}", unmatchedCategories);
        
        // Step 3: Fetch PFC mappings for unmatched categories if needed
        Map<String, String> pfcMappings = unmatchedCategories.isEmpty() ? 
            Collections.emptyMap() : categoryService.fetchPfcMappings(unmatchedCategories, "finicity");
        log.info("pfcMappings: {}", pfcMappings);
        
        // Step 4: Combine both maps
        Map<String, String> finalMappings = new HashMap<>(dbCategoryMappings);
        finalMappings.putAll(pfcMappings);
        log.info("finalMappings: {}", finalMappings);
        
        return finalMappings;
    }

    private void mapFinicityToTransaction(
        FinicityTransaction finTxn, 
        Transactions txn, 
        Map<String, String> categoryMappings,
        Map<String, Integer> subCategoryIdMap,
        Map<String, Accounts> accountsMap,
        Users user) {
        
        if (finTxn.getCategorization() != null) {
            String finicityCategory = finTxn.getCategorization().getCategory();
            String pfcCategory = categoryMappings.getOrDefault(finicityCategory, "Uncategorized");
            
            txn.setCategory(pfcCategory);
            
            txn.setSubCategoryId(subCategoryIdMap.getOrDefault(pfcCategory, null));
            
            txn.setDescription(finTxn.getCategorization().getNormalizedPayeeName());
        } else {
            txn.setDescription(finTxn.getDescription());
            txn.setCategory("Uncategorized");
        }
        
        txn.setTransactionAmount(finTxn.getAmount());
        
        try {
            txn.setTransactionDate(LocalDateTime.ofInstant(
                Instant.ofEpochSecond(finTxn.getTransactionDate()), 
                ZoneId.systemDefault()));
        } catch (Exception e) {
            log.warn("Invalid transaction date: {}, using current time", 
                finTxn.getTransactionDate());
            txn.setTransactionDate(LocalDateTime.now());
        }
        
        txn.setTransactionId(String.valueOf(finTxn.getId()));
        txn.setTransactionType(finTxn.getAmount() < 0 ? "debit" : "credit");
        
        Accounts account = accountsMap.get(String.valueOf(finTxn.getAccountId()));
        txn.setAccount(account);
        txn.setUser(user);
        txn.setMerchantName(finTxn.getDescription());
    }

    // Clear cache - convenience method for common transaction-related patterns
    public void clearTransactionRelatedCache(Integer userId) {
        clearCacheByPatterns("transactionCache",
            "getTransactionsByUserId_" + userId + "_",
            "getAggregatedExpenses_" + userId + "_",
            "getAggregatedIncome_" + userId + "_"
        );
    }

    // Clear cache - flexible method that accepts multiple patterns
    private void clearCacheByPatterns(String cacheName, String... patterns) {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
            if (cache != null && cache instanceof CaffeineCache caffeineCache) {
                com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache =
                    (com.github.benmanes.caffeine.cache.Cache<Object, Object>) caffeineCache.getNativeCache();

                // Remove all cache entries that match any of the provided patterns
                nativeCache.asMap().keySet().removeIf(key -> {
                    if (key instanceof String keyStr) {
                        for (String pattern : patterns) {
                            if (keyStr.startsWith(pattern)) {
                                return true;
                            }
                        }
                    }
                    return false;
                });

                log.info("Cleared cache entries matching patterns: {} from cache: {}",
                    java.util.Arrays.toString(patterns), cacheName);
            } else {
                log.warn("Cache '{}' not found or not a CaffeineCache instance", cacheName);
            }
        } catch (Exception e) {
            log.error("Error clearing cache '{}' with patterns: {}",
                cacheName, java.util.Arrays.toString(patterns), e);
        }
    }
}