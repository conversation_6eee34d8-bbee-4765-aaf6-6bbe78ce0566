package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.AccountBalanceHistoryDto;
import com.pennypal.fintech.service.AccountSummaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;
import java.util.logging.Logger;

@RestController
@RequestMapping("/api")
@Tag(name = "Account Summary", description = "APIs for retrieving account summaries and balance history")
public class AccountSummaryController {
    
    private static final Logger logger = Logger.getLogger(AccountSummaryController.class.getName());
    
    @Autowired
    private AccountSummaryService accountSummaryService;
    
    /**
     * Get account summaries for a specific user
     */
    @GetMapping("/accounts/summary/{userId}")
    @Operation(summary = "Get account summaries",
               description = "Retrieves account summaries for a specific user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved account summaries",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Error fetching account summaries",
            content = @Content(mediaType = "text/plain")
        )
    })
    public ResponseEntity<?> getAccountSummaries(
            @Parameter(description = "ID of the user to get account summaries for", required = true)
            @PathVariable int userId) {
        logger.info("Fetching account summaries for user ID: " + userId);
        try {
            List<?> summaries = accountSummaryService.getAccountSummariesByUserId(userId);
            return ResponseEntity.ok(summaries);
        } catch (Exception e) {
            logger.severe("Error fetching account summaries: " + e.getMessage());
            return ResponseEntity.badRequest().body("Error fetching account summaries: " + e.getMessage());
        }
    }
    
    /**
     * Get balance history for charts based on account type and time period
     */
    @Operation(summary = "Get balance history for charts",
               description = "Retrieves balance history data for charts based on account type and time period")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved balance history",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of balance history data")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input parameters",
            content = @Content(mediaType = "text/plain")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "text/plain")
        )
    })
    @GetMapping("/balance-history/{chartType}/{userId}")
    public ResponseEntity<Object> getBalanceHistory(
            @Parameter(description = "Type of chart (e.g., depository, credit, loan, investment)", required = true)
            @PathVariable String chartType,
            @Parameter(description = "ID of the user to get balance history for", required = true)
            @PathVariable int userId,
            @Parameter(description = "Time period for the data (e.g., yearly, monthly)", required = false)
            @RequestParam(name = "timePeriod", defaultValue = "yearly") String timePeriod) {
    
        logger.info("Fetching balance history for user ID: " + userId + ", chart type: " + chartType + ", time period: " + timePeriod);
    
        try {
            List<AccountBalanceHistoryDto> balanceHistory = accountSummaryService.getBalanceHistory(userId, chartType, timePeriod);
            return ResponseEntity.ok(balanceHistory);
        } catch (IllegalArgumentException e) {
            logger.warning("Invalid input: " + e.getMessage());
            return ResponseEntity.badRequest().body("Invalid input: " + e.getMessage());
        } catch (Exception e) {
            logger.severe("Internal server error: " + e.getMessage());
            e.printStackTrace();  // shows full stack trace
            return ResponseEntity.internalServerError().body("Internal server error: " + e.getMessage());
        }
    }

    /**
     * Get quarterly aggregated balance history for charts
     */
    @Operation(summary = "Get quarterly aggregated balance history",
               description = "Retrieves quarterly aggregated balance history data for charts")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved quarterly balance history",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = AccountBalanceHistoryDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid parameters",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/history/quarterly/{userId}")
    public ResponseEntity<List<AccountBalanceHistoryDto>> getQuarterlyBalanceHistory(
            @Parameter(description = "ID of the user to get quarterly balance history for", required = true)
            @PathVariable int userId,
            @Parameter(description = "Type of chart (e.g., depository, credit, loan, investment)", required = true)
            @RequestParam String chartType) {
        
        logger.info("Received request for quarterly balance history for userId: " + userId +
                   ", chartType: " + chartType);
        
        try {
            // Use the quarterly-aggregate time period
            List<AccountBalanceHistoryDto> balanceHistory = 
                accountSummaryService.getBalanceHistory(userId, chartType, "quarterly-aggregate");
            return ResponseEntity.ok(balanceHistory);
        } catch (IllegalArgumentException e) {
            logger.warning("Invalid parameters: " + e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.severe("Error fetching quarterly balance history: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * Get account count for a specific account type
     */
    // @GetMapping("/accounts/count/{accountType}/{userId}")
    // public ResponseEntity<?> getAccountCount(
    //         @PathVariable String accountType,
    //         @PathVariable int userId) {
        
    //     logger.info("Fetching account count for user ID: " + userId + ", account type: " + accountType);
        
    //     try {
    //         int count = accountSummaryService.getAccountCountByType(userId, accountType);
    //         AccountCountDto accountCountDto = new AccountCountDto(count);
    //         return ResponseEntity.ok(accountCountDto);
    //     } catch (Exception e) {
    //         logger.severe("Error fetching account count: " + e.getMessage());
    //         return ResponseEntity.badRequest().body("Error fetching account count: " + e.getMessage());
    //     }
    // }
}