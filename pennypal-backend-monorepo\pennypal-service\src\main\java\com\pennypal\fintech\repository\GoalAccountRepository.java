package com.pennypal.fintech.repository;

import com.pennypal.fintech.entity.Accounts;
import com.pennypal.fintech.entity.Goal;
import com.pennypal.fintech.entity.GoalAccount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.Optional;
import java.util.List;

@Repository
public interface GoalAccountRepository extends JpaRepository<GoalAccount, Integer> {
    
    List<GoalAccount> findByGoal(Goal goal);
    
    List<GoalAccount> findByAccount(Accounts account);
    Optional<GoalAccount> findByGoalIdAndAccountId(Integer goalId, Integer accountId);

    @Query("SELECT ga FROM GoalAccount ga WHERE ga.goal.id = ?1")
    List<GoalAccount> findByGoalId(Integer goalId);
    
    @Query("SELECT SUM(ga.allocationPercentage) FROM GoalAccount ga WHERE ga.goal.id = ?1")
    Double getTotalAllocationPercentageForGoal(Integer goalId);
    
    @Query("SELECT SUM(ga.currentContribution) FROM GoalAccount ga WHERE ga.goal.id = ?1")
    Double getTotalContributionForGoal(Integer goalId);
    
     // NEW: Get total goal account balance for a goal
    @Query("SELECT SUM(ga.goalAccountBalance) FROM GoalAccount ga WHERE ga.goal.id = ?1")
    Double getTotalGoalAccountBalanceForGoal(Integer goalId);

    // NEW: Get total goal account balance for a specific account across all goals
    @Query("SELECT SUM(ga.goalAccountBalance) FROM GoalAccount ga WHERE ga.account.id = ?1")
    Double getTotalGoalAccountBalanceForAccount(Integer accountId);

    // NEW: Find goal accounts with insufficient balance for recurring contributions
    @Query("SELECT ga FROM GoalAccount ga WHERE ga.goalAccountBalance < ?1")
    List<GoalAccount> findGoalAccountsWithInsufficientBalance(Double minimumAmount);

    // NEW: Find goal accounts by user ID
    @Query("SELECT ga FROM GoalAccount ga WHERE ga.goal.user.id = ?1")
    List<GoalAccount> findByUserId(Integer userId);

    GoalAccount findByGoalAndAccount(Goal goal, Accounts account);

    @Modifying
    @Query("DELETE FROM GoalAccount ga WHERE ga.goal.id = ?1")
    void deleteByGoalId(Integer goalId);
List<GoalAccount> findByAccountAndGoalStatus(Accounts account, Goal.GoalStatus status);
}