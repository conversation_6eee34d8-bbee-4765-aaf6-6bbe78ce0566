package com.pennypal.fintech.service;

import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import lombok.extern.slf4j.Slf4j;

import com.pennypal.fintech.dto.UserNotificationRulesDto;
import com.pennypal.fintech.entity.NotificationRules;
import com.pennypal.fintech.entity.UserNotificationRules;
import com.pennypal.fintech.repository.NotificationRuleRepository;
import com.pennypal.fintech.repository.UserNotificationRulesRepository;

@Service
@Slf4j
public class UserNotificationRulesService {

    @Autowired
    private UserNotificationRulesRepository userNotificationRulesRepository;

    @Autowired
    private NotificationRuleRepository notificationRuleRepository;

    public UserNotificationRulesService(
        UserNotificationRulesRepository userNotificationRulesRepository) {
        this.userNotificationRulesRepository = userNotificationRulesRepository;
        this.notificationRuleRepository = notificationRuleRepository;
    }

    @Cacheable(value = "notificationRuleCache",
               key = "#root.methodName + '_' + #userId")
    @Transactional
    public List<UserNotificationRulesDto> getNotificationRulesByUser(Integer userId) {
        log.info("Inside getNotificationRulesByUser in UserNotificationRulesService");
        // Fetch master notification rules
        List<NotificationRules> masterRules = notificationRuleRepository.findAll();
        log.info("Fetched master notification rules: {}", masterRules);

        // Fetch user specific notification rules
        List<UserNotificationRules> userRules = userNotificationRulesRepository.findByUserId(userId);
        log.info("Fetched user notification rules: {}", userRules);
        
        Map<Integer, UserNotificationRules> userRulesMap = userRules.stream()
            .filter(rule -> rule.getRuleId() != null)
            .collect(Collectors.toMap(UserNotificationRules::getRuleId, Function.identity()));

        List<UserNotificationRulesDto> result = new ArrayList<>();

        for (NotificationRules masterRule : masterRules) {
            if (userRulesMap.containsKey(masterRule.getId())) {
                // Add user-specific rule
                UserNotificationRules userRule = userRulesMap.get(masterRule.getId());
                result.add(convertToDto(userRule));
            } else {
                // Add master rule if user specific rule not found
                UserNotificationRulesDto dto = new UserNotificationRulesDto();
                dto.setId(null);
                dto.setUserId(userId);
                dto.setRuleId(masterRule.getId());
                dto.setRuleName(masterRule.getRuleName());
                dto.setRuleType(masterRule.getRuleType());
                dto.setConditionType(masterRule.getConditionType());
                dto.setIsEnabled(true);
                dto.setAmountThreshold(masterRule.getAmountThreshold());
                dto.setDaysThreshold(masterRule.getDaysThreshold());
                dto.setPercentageThreshold(masterRule.getPercentageThreshold());
                dto.setMessageTemplate(masterRule.getMessageTemplate());
                dto.setCreatedAt(masterRule.getCreatedAt());
                dto.setUpdatedAt(masterRule.getUpdatedAt());
                dto.setPhoneEnabled(true);
                dto.setEmailEnabled(true);
                dto.setSeverity(masterRule.getSeverity().name());
                result.add(dto);
            }
        }
        
        // Order by rule ID
        result.sort((r1, r2) -> r1.getRuleId().compareTo(r2.getRuleId()));
        log.info("Sorted final notification rules: {}", result);
        
        return result;

    }

    private UserNotificationRulesDto convertToDto(UserNotificationRules rule) {
        UserNotificationRulesDto dto = new UserNotificationRulesDto();
        dto.setId(rule.getId());
        dto.setUserId(rule.getUserId());
        dto.setRuleId(rule.getRuleId());
        dto.setRuleName(rule.getRuleName());
        dto.setRuleType(rule.getRuleType());
        dto.setConditionType(rule.getConditionType());
        dto.setIsEnabled(rule.getIsEnabled());
        dto.setAmountThreshold(rule.getAmountThreshold());
        dto.setDaysThreshold(rule.getDaysThreshold());
        dto.setPercentageThreshold(rule.getPercentageThreshold());
        dto.setMessageTemplate(rule.getMessageTemplate());
        dto.setCreatedAt(rule.getCreatedAt());
        dto.setUpdatedAt(rule.getUpdatedAt());
        dto.setPhoneEnabled(rule.getPhoneEnabled());
        dto.setEmailEnabled(rule.getEmailEnabled());
        dto.setSeverity(rule.getSeverity());
        return dto;
    }

    @CacheEvict(value = "notificationRuleCache",
                key = "'getNotificationRulesByUser' + '_' + #ruleDto.userId")
    @Transactional
    public void editRule(UserNotificationRulesDto ruleDto) {
        log.info("Editing rule: {}" + ruleDto);
        UserNotificationRules rule;
        if (ruleDto.getId() == null) {
            rule = new UserNotificationRules();
            log.info("Creating new user notification rule");
        } else {
            rule = userNotificationRulesRepository.findById(ruleDto.getId())
                .orElseThrow(() -> new RuntimeException("Rule not found"));
            rule.setId(ruleDto.getId());
            log.info("Editing existing user notification rule: {}", rule);
        }
        
        rule.setUserId(ruleDto.getUserId());
        rule.setRuleId(ruleDto.getRuleId());
        rule.setRuleName(ruleDto.getRuleName());
        rule.setRuleType(ruleDto.getRuleType());
        rule.setConditionType(ruleDto.getConditionType());
        rule.setIsEnabled(ruleDto.getIsEnabled());
        if (ruleDto.getAmountThreshold() != null) {
            log.info("Setting custom amount threshold: " + ruleDto.getAmountThreshold());
            rule.setAmountThreshold(ruleDto.getAmountThreshold());
        }
        if (ruleDto.getDaysThreshold() != null) {
            log.info("Setting custom days threshold: " + ruleDto.getDaysThreshold());
            rule.setDaysThreshold(ruleDto.getDaysThreshold());
        }
        if (ruleDto.getPercentageThreshold() != null) {
            log.info("Setting custom percentage threshold: " + ruleDto.getPercentageThreshold());
            rule.setPercentageThreshold(ruleDto.getPercentageThreshold());
        }
        if (ruleDto.getMessageTemplate() != null) {
            log.info("Setting custom message template: " + ruleDto.getMessageTemplate());
            rule.setMessageTemplate(ruleDto.getMessageTemplate());
        }
        rule.setUpdatedAt(ruleDto.getUpdatedAt());
        rule.setPhoneEnabled(ruleDto.getPhoneEnabled());
        rule.setEmailEnabled(ruleDto.getEmailEnabled());
        rule.setSeverity(ruleDto.getSeverity());

        userNotificationRulesRepository.save(rule);
    }
}