package com.pennypal.fintech.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.pennypal.fintech.dto.TransactionDto;
import com.pennypal.fintech.entity.Transactions;
import com.pennypal.fintech.repository.TransactionRepository;
import com.pennypal.fintech.service.BudgetRulesService;
import com.pennypal.fintech.service.NotificationRuleService;
import com.pennypal.fintech.service.RecurringTransactionService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

class TransactionEvent {
    private final List<TransactionDto> transactions;
    
    public TransactionEvent(Object source, List<TransactionDto> transactions) {
        this.transactions = transactions;
    }
    
    public List<TransactionDto> getTransactions() {
        return transactions;
    }
}

@Component
public class TransactionEventListenerService {
    private static final Logger log = LoggerFactory.getLogger(TransactionEventListenerService.class);
    
    @Autowired
    private BudgetRulesService budgetRulesService;

    @Autowired
    private RecurringTransactionService recurringTransactionService;

    @Autowired
    private NotificationRuleService notificationRulesService;
    
      @Autowired
    private ReconcileService reconcileService;

    @Autowired
    private TransactionRepository transactionRepository;

    @EventListener
    @Async
    public String updateExistingRecurrencePattern(TransactionEvent event) {
        List<TransactionDto> newTransactions = event.getTransactions();
        
        if (newTransactions == null || newTransactions.isEmpty()) {
            return ("No transactions were provided");
        }

        for (TransactionDto newTransaction : newTransactions) {
            recurringTransactionService.processNewTransaction(newTransaction);
        }
        
        return "All transactions were processed successfully";
    }
    
    @EventListener
    @Async
    public String createAlertLargeTransaction(TransactionEvent event) {
        List<TransactionDto> newTransactions2 = event.getTransactions();
        
        if (newTransactions2 == null || newTransactions2.isEmpty()) {
            return ("No transactions were provided");
        }
        
        for (TransactionDto newTransaction : newTransactions2) {
            notificationRulesService.checkLargeTransaction(newTransaction);
        }
        
        return "All transactions were processed successfully";
    }

    @EventListener
    @Async
    public String processMerchantCategoryRules(TransactionEvent event){
        log.info("New/Processing merchant category rules for transactions");
        List<TransactionDto> newTransactions3 = event.getTransactions();
        
        if (newTransactions3 == null || newTransactions3.isEmpty()) {
            return ("No transactions were provided");
        }

        for (TransactionDto newTransaction : newTransactions3) {
            log.info("Processing transactions:", newTransaction);
            budgetRulesService.processMerchantCategoryRules(newTransaction);
        }
        
        return "All transactions were processed successfully";
    }

     @EventListener
@Async
public String processTransactionReconciliation(TransactionEvent event) {
    log.info("Processing reconciliation for transactions");
    List<TransactionDto> newTransactions = event.getTransactions();

    if (newTransactions == null || newTransactions.isEmpty()) {
        return "No transactions were provided";
    }

    for (TransactionDto newTransaction : newTransactions) {
        log.info("Processing reconciliation for transaction: {}", newTransaction);

        // ✅ Use primary key 'id' instead of nullable transactionId
        if (newTransaction.getId() == null) {
            log.warn("Skipping transaction: ID is null");
            continue;
        }

        Transactions transaction = transactionRepository
            .findById(newTransaction.getId())
            .orElse(null);

        if (transaction == null) {
            log.warn("Transaction not found for id: {}", newTransaction.getId());
            continue;
        }

        reconcileService.reconcileTransaction(transaction);
    }

    return "All transactions were processed for reconciliation successfully";
}

}