import { combineEpics, ofType } from 'redux-observable';
import { from, of, merge } from 'rxjs';
import { catchError, map, mergeMap, switchMap } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchGoalsSuccess as cacheGoalsSuccess
} from '../redux/cacheSlice';
import {
  fetchGoalsRequest,
  fetchGoalsSuccess,
  fetchGoalsFailure,
  fetchGoalRequest,
  fetchGoalSuccess,
  fetchGoalFailure,
  createGoalRequest,
  createGoalSuccess,
  createGoalFailure,
  updateGoalRequest,
  updateGoalSuccess,
  updateGoalFailure,
  contributeToGoalRequest,
  contributeToGoalSuccess,
  contributeToGoalFailure,
  fetchMonthlyGoalsRequest,
  fetchMonthlyGoalsSuccess,
  fetchMonthlyGoalsFailure,
  fetchGoalSummaryRequest,
  fetchGoalSummarySuccess,
  fetchGoalSummaryFailure,
  fetchGoalAccountBalanceRequest,
  fetchAccountGoalSummaryRequest,
  fetchAccountGoalSummaryFailure,
  fetchAccountGoalSummarySuccess,
  fetchGoalAccountBalanceSuccess,
  fetchGoalAccountBalanceFailure,
} from '../redux/goalSlice';

// Fetch all goals
const fetchGoalsEpic = (action$) => action$.pipe(
  ofType(fetchGoalsRequest.type),
  switchMap(() => {
    console.log('Fetching all goals');
    return from(axiosInstance.get('/pennypal/api/goals')).pipe(
      mergeMap(response => {
        console.log('Goals fetched successfully:', response.data);
        // Return both the goal slice success and cache success actions
        return merge(
          of(fetchGoalsSuccess(response.data)),
          of(cacheGoalsSuccess(response.data))
        );
      }),
      catchError(error => {
        console.error('Error fetching goals:', error);
        return of(fetchGoalsFailure(error.response?.data?.message || 'Failed to fetch goals'));
      })
    );
  })
);

// Fetch single goal
const fetchGoalEpic = (action$) => action$.pipe(
  ofType(fetchGoalRequest.type),
  mergeMap(action => {
    const goalId = action.payload;
    console.log(`Fetching goal with ID: ${goalId}`);
    return from(axiosInstance.get(`/pennypal/api/goals/${goalId}`)).pipe(
      map(response => {
        console.log('Goal fetched successfully:', response.data);
        return fetchGoalSuccess(response.data);
      }),
      catchError(error => {
        console.error(`Error fetching goal ${goalId}:`, error);
        return of(fetchGoalFailure(error.response?.data?.message || 'Failed to fetch goal'));
      })
    );
  })
);

// Create goal - Handles initial contributions with account selection and amounts
const createGoalEpic = (action$) => action$.pipe(
  ofType(createGoalRequest.type),
  mergeMap(action => {
    const goalData = action.payload;
    console.log('Creating new goal:', goalData);
    
    // Transform the data to match backend expectations
    const transformedData = {
      goalName: goalData.goalName,
      goalAmount: goalData.goalAmount,
      startDate: goalData.startDate,
      targetDate: goalData.targetDate,
      goalType: goalData.goalType,
      description: goalData.description,
      initialContributions: goalData.initialContributions || []
    };
    
    // Log initial contributions if provided
    if (transformedData.initialContributions && transformedData.initialContributions.length > 0) {
      console.log(`Creating goal with ${transformedData.initialContributions.length} initial contributions`);
      transformedData.initialContributions.forEach(contribution => 
        console.log(`Account ID: ${contribution.accountId}, Initial Contribution: ${contribution.amount}`)
      );
    }
    
    return from(axiosInstance.post('/pennypal/api/goals/create', transformedData)).pipe(
      map(response => {
        console.log('Goal created successfully:', response.data);
        return createGoalSuccess(response.data);
      }),
      catchError(error => {
        console.error('Error creating goal:', error);
        return of(createGoalFailure(error.response?.data?.message || 'Failed to create goal'));
      })
    );
  })
);

// Update goal - Handles updated contributions with account selection and amounts
const updateGoalEpic = (action$) => action$.pipe(
  ofType(updateGoalRequest.type),
  mergeMap(action => {
    const { goalId, goalData } = action.payload;
    console.log(`Updating goal ${goalId}:`, goalData);
    
    // Transform the data to match backend expectations
    const transformedData = {
      goalName: goalData.goalName,
      goalAmount: goalData.goalAmount,
      startDate: goalData.startDate,
      targetDate: goalData.targetDate,
      goalType: goalData.goalType,
      description: goalData.description,
      // Map account contributions to initialContributions for update
      initialContributions: goalData.initialContributions || goalData.accountContributions || []
    };
    
    // Log contributions if provided
    if (transformedData.initialContributions && transformedData.initialContributions.length > 0) {
      console.log(`Updating goal with ${transformedData.initialContributions.length} contributions`);
      transformedData.initialContributions.forEach(contribution => 
        console.log(`Account ID: ${contribution.accountId}, Contribution: ${contribution.amount}`)
      );
    }
    
    return from(axiosInstance.put(`/pennypal/api/goals/${goalId}`, transformedData)).pipe(
      map(response => {
        console.log('Goal updated successfully:', response.data);
        return updateGoalSuccess(response.data);
      }),
      catchError(error => {
        console.error(`Error updating goal ${goalId}:`, error);
        return of(updateGoalFailure(error.response?.data?.message || 'Failed to update goal'));
      })
    );
  })
);

// Contribute to goal - Simple account selection and amount contribution
const contributeToGoalEpic = (action$) => action$.pipe(
  ofType(contributeToGoalRequest.type),
  mergeMap(action => {
    const contributionData = action.payload;
    console.log('Contributing to goal:', contributionData);
    
    // Ensure data format matches backend expectations
    const transformedContribution = {
      goalId: contributionData.goalId,
      accountId: contributionData.accountId,
      amount: contributionData.amount
    };
    
    return from(axiosInstance.post('/pennypal/api/goals/contribute', transformedContribution)).pipe(
      map(response => {
        console.log('Contribution added successfully:', response.data);
        return contributeToGoalSuccess(response.data);
      }),
      catchError(error => {
        console.error('Error contributing to goal:', error);
        return of(contributeToGoalFailure(error.response?.data?.message || 'Failed to add contribution'));
      })
    );
  })
);

// Fetch goals for month
const fetchMonthlyGoalsEpic = (action$) => action$.pipe(
  ofType(fetchMonthlyGoalsRequest.type),
  mergeMap(action => {
    const { year, month } = action.payload;
    console.log(`Fetching goals for ${month}/${year}`);
    return from(axiosInstance.get(`/pennypal/api/goals/month/${year}/${month}`)).pipe(
      map(response => {
        console.log('Monthly goals fetched successfully:', response.data);
        return fetchMonthlyGoalsSuccess(response.data);
      }),
      catchError(error => {
        console.error(`Error fetching goals for ${month}/${year}:`, error);
        return of(fetchMonthlyGoalsFailure(error.response?.data?.message || 'Failed to fetch monthly goals'));
      })
    );
  })
);

// Fetch goals summary
const fetchGoalSummaryEpic = (action$) => action$.pipe(
  ofType(fetchGoalSummaryRequest.type),
  switchMap(() => {
    console.log('Fetching goals summary');
    return from(axiosInstance.get('/pennypal/api/goals/summary')).pipe(
      map(response => {
        console.log('Goal summary fetched successfully:', response.data);
        return fetchGoalSummarySuccess(response.data);
      }),
      catchError(error => {
        console.error('Error fetching goal summary:', error);
        return of(fetchGoalSummaryFailure(error.response?.data?.message || 'Failed to fetch goal summary'));
      })
    );
  })
);

// Fetch goal account balance
const fetchGoalAccountBalanceEpic = (action$) => action$.pipe(
  ofType(fetchGoalAccountBalanceRequest.type),
  mergeMap(action => {
    const goalId = action.payload;
    console.log(`Fetching account balance for goal: ${goalId}`);
    return from(axiosInstance.get(`/pennypal/api/goals/${goalId}/balance`)).pipe(
      map(response => {
        console.log('Goal account balance fetched successfully:', response.data);
        return fetchGoalAccountBalanceSuccess(response.data);
      }),
      catchError(error => {
        console.error(`Error fetching goal account balance for ${goalId}:`, error);
        return of(fetchGoalAccountBalanceFailure(error.response?.data?.message || 'Failed to fetch goal account balance'));
      })
    );
  })
);
const fetchAccountGoalSummaryEpic = (action$) => action$.pipe(
  ofType(fetchAccountGoalSummaryRequest.type),
  mergeMap(action => {
    const accountId = action.payload;
    console.log(`Fetching goal summary for account: ${accountId}`);
    return from(axiosInstance.get(`/pennypal/api/goals/account/${accountId}/summary`)).pipe(
      map(response => {
        console.log('Account goal summary fetched successfully:', response.data);
        return fetchAccountGoalSummarySuccess(response.data);
      }),
      catchError(error => {
        console.error(`Error fetching account goal summary for ${accountId}:`, error);
        return of(fetchAccountGoalSummaryFailure(error.response?.data?.message || 'Failed to fetch account goal summary'));
      })
    );
  })
);
export const goalEpics = combineEpics(
  fetchGoalsEpic,
  fetchGoalEpic,
  createGoalEpic,
  updateGoalEpic,
  contributeToGoalEpic,
  fetchMonthlyGoalsEpic,
  fetchGoalSummaryEpic,
  fetchGoalAccountBalanceEpic,
  fetchAccountGoalSummaryEpic
);
export default goalEpics;
