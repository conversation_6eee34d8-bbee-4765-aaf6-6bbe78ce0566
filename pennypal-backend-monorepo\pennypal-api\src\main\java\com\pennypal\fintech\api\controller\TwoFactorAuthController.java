package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.ApiResponseDto;
import com.pennypal.fintech.dto.TwoFactorAuthDto;
import com.pennypal.fintech.service.TwoFactorAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@Slf4j
@RestController
@RequestMapping("/api/v1/auth/2fa")
@CrossOrigin
@Tag(name = "Two-Factor Authentication", description = "APIs for managing two-factor authentication setup and verification")
public class TwoFactorAuthController {
    
    @Autowired
    private TwoFactorAuthService twoFactorAuthService;
    
    @PostMapping("/setup")
    @Operation(summary = "Setup two-factor authentication",
               description = "Initiates two-factor authentication setup for a user and returns QR code")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "2FA setup successful",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid user ID or setup failed",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json")
        )
    })
    public ResponseEntity<ApiResponseDto> setupTwoFactorAuth(
            @Parameter(description = "ID of the user to setup 2FA for", required = true)
            @RequestParam Integer userId) {
        try {
            TwoFactorAuthDto result = twoFactorAuthService.setupTwoFactorAuth(userId);
            return ResponseEntity.ok(new ApiResponseDto(true, "2FA setup successful", result));
        } catch (Exception e) {
            log.error("Error setting up 2FA for user: {}", userId, e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponseDto(false, e.getMessage()));
        }
    }
    
    @PostMapping("/enable")
    @Operation(summary = "Enable two-factor authentication",
               description = "Enables two-factor authentication for a user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "2FA enabled successfully",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid user ID or verification code",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json")
        )
    })
    public ResponseEntity<ApiResponseDto> enableTwoFactorAuth(
            @Parameter(description = "ID of the user to enable 2FA for", required = true)
            @RequestParam Integer userId,
            @Parameter(description = "Verification code to enable 2FA", required = true)
            @RequestParam String verificationCode) {
        try {
            boolean result = twoFactorAuthService.enableTwoFactorAuth(userId, verificationCode);
            if (result) {
                return ResponseEntity.ok(new ApiResponseDto(true, "2FA enabled successfully"));
            } else {
                return ResponseEntity.badRequest()
                        .body(new ApiResponseDto(false, "Invalid verification code"));
            }
        } catch (Exception e) {
            log.error("Error enabling 2FA for user: {}", userId, e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponseDto(false, e.getMessage()));
        }
    }
    
    @PostMapping("/disable")
    @Operation(summary = "Disable two-factor authentication",
               description = "Disables two-factor authentication for a user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "2FA disabled successfully",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid user ID or verification code",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json")
        )
    })
    public ResponseEntity<ApiResponseDto> disableTwoFactorAuth(
            @Parameter(description = "ID of the user to disable 2FA for", required = true)
            @RequestParam Integer userId,
            @Parameter(description = "Verification code to disable 2FA", required = true)
            @RequestParam String verificationCode) {
        try {
            boolean result = twoFactorAuthService.disableTwoFactorAuth(userId, verificationCode);
            if (result) {
                return ResponseEntity.ok(new ApiResponseDto(true, "2FA disabled successfully"));
            } else {
                return ResponseEntity.badRequest()
                        .body(new ApiResponseDto(false, "Invalid verification code"));
            }
        } catch (Exception e) {
            log.error("Error disabling 2FA for user: {}", userId, e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponseDto(false, e.getMessage()));
        }
    }
    
    @PostMapping("/verify")
    @Operation(summary = "Verify two-factor authentication code",
               description = "Verifies a two-factor authentication code for a user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Code verified successfully",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid user ID or verification code",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json")
        )
    })
    public ResponseEntity<ApiResponseDto> verifyTwoFactorCode(
            @Parameter(description = "ID of the user to verify 2FA code for", required = true)
            @RequestParam Integer userId,
            @Parameter(description = "Verification code to verify", required = true)
            @RequestParam String code) {
        try {
            boolean result = twoFactorAuthService.verifyTwoFactorCode(userId, code);
            return ResponseEntity.ok(new ApiResponseDto(result, 
                    result ? "Code verified successfully" : "Invalid verification code"));
        } catch (Exception e) {
            log.error("Error verifying 2FA code for user: {}", userId, e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponseDto(false, e.getMessage()));
        }
    }
    
    @GetMapping("/status")
    @Operation(summary = "Get two-factor authentication status",
               description = "Retrieves the status of two-factor authentication for a user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved 2FA status",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid user ID",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json")
        )
    })
    public ResponseEntity<ApiResponseDto> getTwoFactorStatus(
        @Parameter(description = "ID of the user to get 2FA status for", required = true)
        @RequestParam Integer userId) {
        try {
            TwoFactorAuthDto status = twoFactorAuthService.getTwoFactorStatus(userId);
            return ResponseEntity.ok(new ApiResponseDto(true, "Status retrieved successfully", status));
        } catch (Exception e) {
            log.error("Error getting 2FA status for user: {}", userId, e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponseDto(false, e.getMessage()));
        }
    }
}