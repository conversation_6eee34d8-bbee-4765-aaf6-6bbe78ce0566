package com.pennypal.fintech.service;

import com.pennypal.fintech.dto.NotificationDto;
import com.pennypal.fintech.entity.Notification;
import com.pennypal.fintech.repository.NotificationRepository;
import com.pennypal.fintech.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.cache.CacheManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class NotificationService {
    
    @Autowired
    private final NotificationRepository notificationRepository;

    @Autowired
    private final UserRepository userRepository;

    @Autowired
    private final CacheManager cacheManager;

    public NotificationService(NotificationRepository notificationRepository, 
                               UserRepository userRepository,
                               CacheManager cacheManager) {
        this.notificationRepository = notificationRepository;
        this.userRepository = userRepository;
        this.cacheManager = cacheManager;
    }
    
    @Cacheable(value = "notificationCache",
               key = "#root.methodName + '_' + #userId")
    @Transactional(readOnly = true)
    public List<NotificationDto> getUserNotifications(Integer userId) {
        log.info("Fetching notifications for user: {}", userId);
        if (!userRepository.existsById(userId)) {
            log.error("User not found");
            throw new RuntimeException("User not found");
        }

        // return notificationRepository.findByUserIdOrderByCreateTimestampDesc(userId);
        List <Object[]> results = notificationRepository.findByUserIdOrderByCreateTimestampDesc(userId);
        return results.stream()
            .map(result -> new NotificationDto(
                    (Integer) result[0],
                    ((Timestamp) result[1]).toLocalDateTime(),
                    (Integer) result[2],
                    (String) result[3],
                    (String) result[4],
                    (String) result[5],
                    result[6] != null ? ((Timestamp) result[6]).toLocalDateTime() : null,
                    result[7] != null ? new String((byte[]) result[7], StandardCharsets.UTF_8) : null
            ))
            .collect(Collectors.toList());
    }

    @Transactional
    public void markNotificationsAsRead(Integer notificationId) {
        log.info("Marking notification as read: {}", notificationId);
        Notification notification = notificationRepository.findByNotificationId(notificationId);
        
        notification.setState(Notification.NotificationState.read);
        notification.setReadTimestamp(LocalDateTime.now());
        
        notificationRepository.save(notification);

        // Clear cache
        clearCacheByPatterns("notificationCache", "getUserNotifications_" + notification.getUserId());
    }

    @CacheEvict(value = "notificationCache",
                key = "'getUserNotifications' + '_' + #userId")
    @Transactional
    public void deleteAllNotificationsForUser(Integer userId) {
        log.info("Deleting all notifications for user: {}", userId);
        notificationRepository.deleteByUserId(userId);
    }

    @Transactional
    public void deleteNotification(Integer notificationId) {
        log.info("Deleting notification: {}", notificationId);
        
        // Get userId
        Integer userId = notificationRepository.findByNotificationId(notificationId).getUserId();
        
        // Delete notification
        notificationRepository.deleteByNotificationId(notificationId);

        // Clear cache
        clearCacheByPatterns("notificationCache", "getUserNotifications_" + userId);
    }

    private void clearCacheByPatterns(String cacheName, String... patterns) {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
            if (cache != null && cache instanceof CaffeineCache caffeineCache) {
                com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache =
                    (com.github.benmanes.caffeine.cache.Cache<Object, Object>) caffeineCache.getNativeCache();
    
                // Remove all cache entries that match any of the provided patterns
                nativeCache.asMap().keySet().removeIf(key -> {
                    if (key instanceof String keyStr) {
                        for (String pattern : patterns) {
                            if (keyStr.startsWith(pattern)) {
                                return true;
                            }
                        }
                    }
                    return false;
                });
    
                log.info("Cleared cache entries matching patterns: {} from cache: {}",
                    java.util.Arrays.toString(patterns), cacheName);
            } else {
                log.warn("Cache '{}' not found or not a CaffeineCache instance", cacheName);
            }
        } catch (Exception e) {
            log.error("Error clearing cache '{}' with patterns: {}",
                cacheName, java.util.Arrays.toString(patterns), e);
        }
    }
}