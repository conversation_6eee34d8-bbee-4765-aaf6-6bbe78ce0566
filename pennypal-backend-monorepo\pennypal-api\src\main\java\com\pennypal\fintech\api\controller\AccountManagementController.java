package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.ApiResponseDto;
import com.pennypal.fintech.dto.ChangePasswordDto;
import com.pennypal.fintech.dto.DeleteAccountDto;
import com.pennypal.fintech.service.AccountManagementService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@Slf4j
@RestController
@RequestMapping("/api/v1/accounts")
@CrossOrigin
@Tag(name = "Account Management", description = "APIs for managing user accounts including password changes and account deletion")
public class AccountManagementController {
    
    @Autowired
    private AccountManagementService accountManagementService;
    
    @Operation(summary = "Change user password",
               description = "Changes the password for an existing user account")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Password changed successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid password data or password change failed",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PostMapping("/change-password")
    public ResponseEntity<ApiResponseDto> changePassword(
            @Parameter(description = "Password change request data", required = true)
            @Valid @RequestBody ChangePasswordDto changePasswordDto) {
        try {
            boolean result = accountManagementService.changePassword(changePasswordDto);
            if (result) {
                return ResponseEntity.ok(new ApiResponseDto(true, "Password changed successfully"));
            } else {
                return ResponseEntity.badRequest()
                        .body(new ApiResponseDto(false, "Failed to change password"));
            }
        } catch (Exception e) {
            log.error("Error changing password for user: {}", changePasswordDto.getUserId(), e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponseDto(false, e.getMessage()));
        }
    }
    
    @Operation(summary = "Delete user account permanently",
               description = "Permanently deletes a user account and all associated data")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Account deleted successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid account data or deletion failed",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PostMapping("/delete-permanently")
    public ResponseEntity<ApiResponseDto> deleteAccountPermanently(
            @Parameter(description = "Account deletion request data", required = true)
            @Valid @RequestBody DeleteAccountDto deleteAccountDto) {
        try {
            boolean result = accountManagementService.deleteAccountPermanently(deleteAccountDto);
            if (result) {
                return ResponseEntity.ok(new ApiResponseDto(true, "Account deleted permanently"));
            } else {
                return ResponseEntity.badRequest()
                        .body(new ApiResponseDto(false, "Failed to delete account"));
            }
        } catch (Exception e) {
            log.error("Error deleting account for user: {}", deleteAccountDto.getUserId(), e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponseDto(false, e.getMessage()));
        }
    }
    
    @Operation(summary = "Check account deletion status",
               description = "Checks if an account has been marked for deletion by email address")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved deletion status",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid email address or error checking status",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/deletion-status")
    public ResponseEntity<ApiResponseDto> checkDeletionStatus(
            @Parameter(description = "Email address to check deletion status for", required = true)
            @RequestParam String emailId) {
        try {
            boolean isDeleted = accountManagementService.isAccountDeleted(emailId);
            return ResponseEntity.ok(new ApiResponseDto(true, "Status retrieved successfully", isDeleted));
        } catch (Exception e) {
            log.error("Error checking deletion status for email: {}", emailId, e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponseDto(false, e.getMessage()));
        }
    }
}