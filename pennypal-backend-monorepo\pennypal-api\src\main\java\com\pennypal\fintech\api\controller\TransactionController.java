package com.pennypal.fintech.api.controller;

import jakarta.servlet.http.HttpSession;
import jakarta.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import com.pennypal.fintech.dto.TransactionDto;
import com.pennypal.fintech.dto.RecurringTransactionDto;
import com.pennypal.fintech.dto.RecurringTransactionUiDto;
import com.pennypal.fintech.api.events.TransactionEvent;
import com.pennypal.fintech.dto.FutureRecurringTransactionDto;
import com.pennypal.fintech.entity.Accounts;
import com.pennypal.fintech.entity.SubCategory;
import com.pennypal.fintech.entity.TransactionSummary;
import com.pennypal.fintech.entity.Transactions;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.repository.AccountRepository;
import com.pennypal.fintech.repository.SubCategoryRepository;
import com.pennypal.fintech.repository.TransactionRepository;
import com.pennypal.fintech.service.RecurringTransactionService;
import com.pennypal.fintech.service.TokenService;
import com.pennypal.fintech.service.TransactionService;
import com.pennypal.fintech.service.UserService;
import com.pennypal.fintech.service.NotificationRuleService;
import com.pennypal.fintech.service.PlaidService;
import com.pennypal.fintech.service.BudgetRulesService;
import com.pennypal.fintech.service.BudgetService;
import com.pennypal.fintech.dto.CategoryMonthlySummaryDto;
import com.pennypal.fintech.service.ReconcileService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/v1/transaction")
@Tag(name = "Transaction", description = "Transaction management APIs")
@Slf4j
//@RequiredArgsConstructor
public class TransactionController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private TokenService tokenService;

    @Autowired
    private BudgetService budgetService;

    @Autowired
    private TransactionService transactionService;

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private PlaidService plaidService;

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private SubCategoryRepository subCategoryRepository;

    @Autowired
    private ReconcileService reconcileService;
    
    public TransactionController(TransactionService transactionService, TokenService tokenService, RecurringTransactionService recurringTransactionService, NotificationRuleService notificationRulesService, BudgetRulesService budgetRulesService,BudgetService budgetService) {
        this.transactionService = transactionService;       
        this.tokenService = tokenService;
        this.recurringTransactionService = recurringTransactionService;
        this.notificationRulesService = notificationRulesService;
        this.budgetRulesService = budgetRulesService;
        this.budgetService=budgetService;
    }

    @Autowired
    private RecurringTransactionService recurringTransactionService;

    @Autowired
    private NotificationRuleService notificationRulesService;

    @Autowired
    private BudgetRulesService budgetRulesService;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @GetMapping("/user/{userId}")
    @Operation(summary = "Get user transactions",
               description = "Retrieves transaction information for a specific user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved user transactions",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<Users> getUserTransactions(
            @Parameter(description = "ID of the user to get transactions for", required = true)
            @PathVariable Long userId) {
        Optional<Users> user = userService.findById(1);
        return ResponseEntity.ok(user.get());
       
    }

@GetMapping("/transactions/user/{userId}")
    @Operation(summary = "Get paginated transactions by user ID",
               description = "Retrieves a paginated list of transactions for a specific user with filtering options")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved transactions",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid parameters",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<Page<TransactionDto>> getTransactionsByUserId(
            @Parameter(description = "ID of the user to get transactions for", required = true)
            @PathVariable Long userId,
            @Parameter(description = "Page number (0-based)", required = false)
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Number of items per page", required = false)
            @RequestParam(defaultValue = "250") int pageSize,
            @Parameter(description = "Include removed transactions", required = false)
            @RequestParam(defaultValue = "false") boolean removed,
            @Parameter(description = "Filter by removal status", required = false)
            @RequestParam(name = "is_remove", defaultValue = "false") boolean isRemoveParam) {

        Pageable pageable = PageRequest.of(page, pageSize, Sort.by(Sort.Direction.DESC, "transactionDate"));
        Page<TransactionDto> transactions = transactionService.getTransactionsByUserId(userId, removed, isRemoveParam, pageable);
        return ResponseEntity.ok(transactions);
    }

    @GetMapping("/category/summary")
    @Operation(summary = "Get category transaction summary",
               description = "Retrieves transaction summary for a specific category and optional subcategory over a specified number of months")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved category summary",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid parameters",
            content = @Content(mediaType = "text/plain")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "text/plain")
        )
    })
    public ResponseEntity<?> getCategorySummary(
            @Parameter(description = "ID of the category", required = true)
            @RequestParam Integer categoryId,
            @Parameter(description = "ID of the subcategory (optional)", required = false)
            @RequestParam(required = false) Integer subCategoryId,
            @Parameter(description = "ID of the user", required = true)
            @RequestParam Integer userId,
            @Parameter(description = "Number of months to include in summary", required = false)
            @RequestParam(defaultValue = "3") Integer months) {

        if (categoryId == null || categoryId <= 0) {
            return ResponseEntity.badRequest().body("Category ID must be a positive integer");
        }

        if (userId == null || userId <= 0) {
            return ResponseEntity.badRequest().body("User ID must be a positive integer");
        }

        if (months == null || months <= 0) {
            return ResponseEntity.badRequest().body("Months must be a positive integer");
        }

        try {
            List<CategoryMonthlySummaryDto> summary = budgetService.getCategorySummary(
                    categoryId, subCategoryId, userId, months
            );
            Map<String, Object> response = new HashMap<>();
            response.put("categoryId", categoryId);
            if (subCategoryId != null) {
                response.put("subCategoryId", subCategoryId);
            }
            response.put("userId", userId);
            response.put("summary", summary);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to retrieve category summary: " + e.getMessage());
        }
    }


@GetMapping("/summary/user/{userId}")
    @Operation(summary = "Get transaction summary for user",
               description = "Creates or updates and retrieves transaction summary for a specific user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved transaction summary",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<TransactionSummary> getTransactionSummary(
            @Parameter(description = "ID of the user to get transaction summary for", required = true)
            @PathVariable Long userId) {
        System.out.println("Called getTransactionSummary for userId: " + userId);
        TransactionSummary summary = transactionService.createOrUpdateTransactionSummary(userId);
        return ResponseEntity.ok(summary);
    }

    @GetMapping("/test")
        public String test() {
            return "Transaction controller is working!";
    }

    @GetMapping("/create_link_token")
    public ResponseEntity<Map<String,String>> createLinkToken(HttpSession session) throws Exception{
        Map<String,String> res =  tokenService.createLinkToken();
        return ResponseEntity.ok(res);
    }

    @GetMapping("/addUser")
    public ResponseEntity<String> addUser(@RequestParam String emailId, @RequestParam String mobile) throws Exception{
       // userService.addUser(emailid,mobile);
        //return ResponseEntity.ok(res);
        return ResponseEntity.ok("");
    }
	
	 // Aggregate root
  // tag::get-aggregate-root[]
	// @PostMapping("/transactions/{transactionId}/upload-receipt")
	// public ResponseEntity<String> uploadReceipt(@PathVariable Long transactionId, @RequestParam("file") MultipartFile file) {
	//     try {
	//         String directoryPath = "uploads/receipts/"; // Adjust the directory path
	//         File dir = new File(directoryPath);
	//         if (!dir.exists()) {
	//             dir.mkdirs(); // Create directory if it doesn't exist
	//         }

	//         String filePath = directoryPath + file.getOriginalFilename();
	//         file.transferTo(new File(filePath));

	//         return ResponseEntity.ok("File uploaded successfully: " + filePath);
	//     } catch (Exception e) {
	//         return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("File upload failed: " + e.getMessage());
	//     }
	// }
    @PostMapping("/add")
    public ResponseEntity<Map<String, String>> addTransaction(@RequestBody TransactionDto transactionDto) {
        log.info("Inside addTransaction method of TransactionController");
        log.info("Received TransactionDto: " + transactionDto);
        try {
            Transactions transaction = new Transactions();
            transaction.setTransactionDate(transactionDto.getTransactionDate());
            transaction.setCategory(transactionDto.getCategory());
            transaction.setCategoryId(transactionDto.getCategoryId());
            log.info("Category ID set: " + transaction.getCategoryId());
            Optional<SubCategory> subCategory = 
                subCategoryRepository.findBySubCategory(transactionDto.getCategory());
            if (subCategory.isPresent()) {
                transaction.setSubCategoryId(subCategory.get().getId());
            }
            log.info("Subcategory ID set: " + transaction.getSubCategoryId());
            transaction.setDescription(transactionDto.getDescription());
            transaction.setTransactionAmount(transactionDto.getTransactionAmount());
            transaction.setTax(transactionDto.getTax());
            transaction.setNotes(transactionDto.getNotes());
            transaction.setTag(transactionDto.getTag());
            transaction.setHideFromBudget(transactionDto.isHideFromBudget()); // Set hideFromBudget
            // transaction.setisRemove(transactionDto.isRemove());
            // Handle account relationship
            if (transactionDto.getAccountId() != null) {
                Accounts account = accountRepository.findById(transactionDto.getAccountId())
                    .orElseThrow(() -> new RuntimeException("Account not found"));
                transaction.setAccount(account);
            }

            if (transactionDto.getUserId() != null) {
                Users user = userService.findById(transactionDto.getUserId())
                    .orElseThrow(() -> new RuntimeException("User not found"));
                transaction.setUser(user);
            }

            transactionService.saveTransaction(transaction);

            Map<String, String> response = new HashMap<>();
            response.put("message", "Transaction saved successfully");
            response.put("transactionId", String.valueOf(transaction.getId()));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "Error while saving transaction: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

  // Aggregate root
  // tag::get-aggregate-root[]
  @GetMapping("/transactions")
  List<TransactionDto> all() {
    //Call UserService to fetch User Object
    // Fetch access Token from User Object 
    return transactionService.getAllTransactions();
  }
/* 
   @GetMapping("/transactions/{userId}")
    public ResponseEntity<?> getTransactions(@PathVariable int userId) {
        try {
            // Fetch the access token from user_account table
            List<UserAccount> userAccountList = userAccountRepository.findByUserId(userId);
            if (userAccountList != null && !userAccountList.isEmpty()){
                UserAccount userAccount =  userAccountList.get(0);
                if( userAccount == null || userAccount.getAccessToken() == null) {
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body("Access token not found for this user.");
                }
            

                // Fetch transactions using the access token
                String accessToken = userAccount.getAccessToken();
                TransactionsGetResponse transactions = tokenService.getTransactions(accessToken);

                // Create a simplified response object
                Map<String, Object> response = new HashMap<>();
                response.put("transactions", transactions.getTransactions());
                response.put("total_transactions", transactions.getTotalTransactions());

                return ResponseEntity.ok(response);
            }

        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
        return null;
    }*/

    // Recurring Transactions - api1
    @Operation(
        summary = "Store recurring transactions",
        description = "Analyzes and stores recurring transaction patterns for a user. " +
                      "This endpoint processes the user's transaction history to identify and store recurring patterns " +
                      "of types such as monthly, quarterly, half-yearly, and yearly."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully analyzed and stored recurring transactions",
            content = @Content(
                mediaType = "application/json"
            )
        ),
        @ApiResponse(
            responseCode = "204",
            description = "No recurring transactions found for the user",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid user ID provided",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found or data not available",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error while processing the request",
            content = @Content
        )
    })
    @PostMapping("/recurring/store/{userId}")
    public ResponseEntity<String> storeRecurringTransactions(
        @Parameter(
            description = "ID of the user to analyze transactions for",
            required = true
        )
        @PathVariable Integer userId) {
            try {
                if (userId == null) {
                    return ResponseEntity.badRequest().body("User ID is required.");
                }

                recurringTransactionService.analyzeAndStoreRecurringTransactions(userId);
                return ResponseEntity.ok("Recurring transactions analyzed and stored successfully");

            } catch (IllegalArgumentException e) {
                return ResponseEntity.badRequest().body(e.getMessage());
            } catch (Exception e) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error processing request: " + e.getMessage());
            }
        }

    // Recurring Transactions - api2
    @Operation(
        summary = "Fetch basic recurring transactions",
        description = "Retrieves basic recurring transactions for a specific user without transaction details. "
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved recurring transactions",
            content = @Content(
                mediaType = "application/json"
            )
        ),
        @ApiResponse(
            responseCode = "204",
            description = "No recurring transactions found for the user",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid user ID provided",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error while processing the request",
            content = @Content
        )
    })
    @GetMapping("/recurring/fetch/{userId}")
    public ResponseEntity<?> fetchRecurringTransactions(
        @Parameter(
            description = "ID of the user to fetch recurring transactions for",
            required = true
        )
        @PathVariable Integer userId) {
            try {
                if (userId == null) {
                    return ResponseEntity.badRequest().body("User ID is required.");
                }

                List<RecurringTransactionDto> recurringTrans = recurringTransactionService.fetchRecurringTransactions(userId);
                
                if (recurringTrans.isEmpty()) {
                    return ResponseEntity.status(HttpStatus.NO_CONTENT).body("No recurring transactions found for the user");
                }
                
                return ResponseEntity.ok(recurringTrans);
                
            } catch (IllegalArgumentException e) {
                return ResponseEntity.badRequest().body(e.getMessage());
            } catch (Exception e) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error processing request: " + e.getMessage());
            }
        }

    @Operation(
        summary = "Fetch recurring transactions with details",
        description = "Retrieves all recurring transactions with their complete transaction details for a specific user. "
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved recurring transactions with details",
            content = @Content(
                mediaType = "application/json"
            )
        ),
        @ApiResponse(
            responseCode = "204",
            description = "No recurring transactions found for the user",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid user ID provided",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error while processing the request",
            content = @Content
        )
    })
    @GetMapping("/recurring/fetch2/{userId}")
    public ResponseEntity<?> fetchRecurringTransactionsWithTransactionDetails(
        @Parameter(
            description = "ID of the user to fetch recurring transactions for",
            required = true
        )
        @PathVariable Integer userId) {
            try {
                if (userId == null) {
                    return ResponseEntity.badRequest().body("User ID is required.");
                }

                List<RecurringTransactionUiDto> recurringTransWithTransactionDetails = 
                    recurringTransactionService.fetchRecurringTransactionsWithTransactionDetails(userId);

                if (recurringTransWithTransactionDetails.isEmpty()) {
                    return ResponseEntity.status(HttpStatus.NO_CONTENT).body("No recurring transactions found for the user");
                }

                return ResponseEntity.ok(recurringTransWithTransactionDetails);

            } catch (IllegalArgumentException e) {
                return ResponseEntity.badRequest().body(e.getMessage());
            } catch (Exception e) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error processing request: " + e.getMessage());
            }
        }

    @Operation(
        summary = "Fetch future recurring transactions",
        description = "Retrieves predicted future recurring transactions for the next 13 months based on a specificuser's recurring transaction history. " +
                      "This includes monthly, quarterly, half-yearly, and yearly recurring patterns."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved future recurring transactions",
            content = @Content(
                mediaType = "application/json"
            )
        ),
        @ApiResponse(
            responseCode = "204",
            description = "No future recurring transactions found for the user",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid user ID provided",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error while processing the request",
            content = @Content
        )
    })
    @GetMapping("/recurring/fetch_future/{userId}")
    public ResponseEntity<?> fetchFutureRecurringTransactions(
        @Parameter(
            description = "ID of the user to fetch future recurring transactions for",
            required = true
        )
        @PathVariable Integer userId) {
            try {
                if (userId == null) {
                    return ResponseEntity.badRequest().body("User ID is required.");
                }

                List<FutureRecurringTransactionDto> futureRecurringTransactions = 
                    recurringTransactionService.fetchFutureRecurringTransactions(userId);

                if (futureRecurringTransactions.isEmpty()) {
                    return ResponseEntity.status(HttpStatus.NO_CONTENT).body("No future recurring transactions found for the user");
                }

                return ResponseEntity.ok(futureRecurringTransactions);

            } catch (IllegalArgumentException e) {
                return ResponseEntity.badRequest().body(e.getMessage());
            } catch (Exception e) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error processing request: " + e.getMessage());
            }
        }

    // @PostMapping("/trigger/event")
    // public void triggerRecurring() {
    //     TransactionDto newTransactions1 = new TransactionDto();
    //     newTransactions1.setId(46);
    //     newTransactions1.setTransactionDate(LocalDateTime.of(2025, 2, 15, 0, 0, 0));
    //     newTransactions1.setDescription("Walmart");
    //     newTransactions1.setTransactionAmount(147.00);
    //     newTransactions1.setAccountId(1);
    //     newTransactions1.setUserId(1);

    //     TransactionDto newTransactions2 = new TransactionDto();
    //     newTransactions2.setId(47);
    //     newTransactions2.setTransactionDate(LocalDateTime.of(2025, 2, 15, 0, 0, 0));
    //     newTransactions2.setDescription("GameStop");
    //     newTransactions2.setTransactionAmount(147.00);
    //     newTransactions2.setAccountId(1);
    //     newTransactions2.setUserId(1);

    //     List<TransactionDto> newTransactions0 = Arrays.asList(newTransactions1, newTransactions2);
    //     System.out.println(newTransactions0);

    //     eventPublisher.publishEvent(new TransactionEvent(this, newTransactions0));
    // }
        
    @EventListener
    @Async
    public String updateExistingRecurrencePattern(TransactionEvent event) {
        List<TransactionDto> newTransactions = event.getTransactions();
        
        if (newTransactions == null || newTransactions.isEmpty()) {
            return ("No transactions were provided");
        }

        for (TransactionDto newTransaction : newTransactions) {
            recurringTransactionService.processNewTransaction(newTransaction);
        }
        
        return "All transactions were processed successfully";
    }
    
    @EventListener
    @Async
    public String createAlertLargeTransaction(TransactionEvent event) {
        List<TransactionDto> newTransactions2 = event.getTransactions();
        
        if (newTransactions2 == null || newTransactions2.isEmpty()) {
            return ("No transactions were provided");
        }
        
        for (TransactionDto newTransaction : newTransactions2) {
            notificationRulesService.checkLargeTransaction(newTransaction);
        }
        
        return "All transactions were processed successfully";
    }

    @EventListener
    @Async
    public String processMerchantCategoryRules(TransactionEvent event){
        log.info("Processing merchant category rules for transactions");
        List<TransactionDto> newTransactions3 = event.getTransactions();
        
        if (newTransactions3 == null || newTransactions3.isEmpty()) {
            return ("No transactions were provided");
        }

        for (TransactionDto newTransaction : newTransactions3) {
            log.info("Processing transactions:", newTransaction);
            budgetRulesService.processMerchantCategoryRules(newTransaction);
        }
        
        return "All transactions were processed successfully";
    }    

    @Operation(
        summary = "Fetch aggregated expenses",
        description = "Retrieves aggregated expense data for a user within specified time in specified intervals"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved aggregated expenses",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "204",
            description = "Empty response",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid parameters provided",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error while processing the request",
            content = @Content
        )
    })
    @GetMapping("/fetch/expenses/agg/{userId}/{duration}/{interval}")
    public ResponseEntity<?> getAggregatedExpenses(
        @Parameter(
            description = "ID of the user to get expenses for",
            required = true
        )
        @PathVariable Integer userId,

        @Parameter(
            description = "Duration in months to look back",
            required = true
        )
        @PathVariable Integer duration,

        @Parameter(
            description = "Interval in days for grouping the data",
            required = true
        )
        @PathVariable Integer interval) {
            try {
                if (userId == null) {
                    return ResponseEntity.badRequest().body("User ID is required.");
                }

                if (duration == null) {
                    return ResponseEntity.badRequest().body("Duration is required.");
                }

                if (duration < 1) {
                    return ResponseEntity.badRequest().body("Duration must be at least 1 month.");
                }

                if (interval == null) {
                    return ResponseEntity.badRequest().body("Interval is required.");
                }

                if (interval < 1) {
                    return ResponseEntity.badRequest().body("Interval must be at least 1 day.");
                }

                List<Map<String, Object>> response = transactionService.getAggregatedExpenses(userId, duration, interval);
                
                if (response.isEmpty()) {
                    return ResponseEntity.status(HttpStatus.NO_CONTENT).body("No expenses found for the user");
                }
                
                return ResponseEntity.ok(response);
                
            } catch (IllegalArgumentException e) {
                return ResponseEntity.badRequest().body(e.getMessage());
            } catch (Exception e) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error processing request: " + e.getMessage());
            }
        }

    @Operation(
        summary = "Fetch aggregated income",
        description = "Retrieves aggregated income data for a user within specified time in specified intervals"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved aggregated income",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "204",
            description = "Empty response",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid parameters provided",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error while processing the request",
            content = @Content
        )
    })
    @GetMapping("/fetch/income/agg/{userId}/{duration}/{interval}")
    public ResponseEntity<?> getAggregatedIncome(
        @Parameter(
            description = "ID of the user to get income for",
            required = true
        )
        @PathVariable Integer userId,

        @Parameter(
            description = "Duration in months to look back",
            required = true
        )
        @PathVariable Integer duration,

        @Parameter(
            description = "Interval in days for grouping the data",
            required = true
        )
        @PathVariable Integer interval) {
            try {
                if (userId == null) {
                    return ResponseEntity.badRequest().body("User ID is required.");
                }

                if (duration == null) {
                    return ResponseEntity.badRequest().body("Duration is required.");
                }

                if (duration < 1) {
                    return ResponseEntity.badRequest().body("Duration must be at least 1 month.");
                }

                if (interval == null) {
                    return ResponseEntity.badRequest().body("Interval is required.");
                }

                if (interval < 1) {
                    return ResponseEntity.badRequest().body("Interval must be at least 1 day.");
                }

                List<Map<String, Object>> response = transactionService.getAggregatedIncome(userId, duration, interval);
                
                if (response.isEmpty()) {
                    return ResponseEntity.status(HttpStatus.NO_CONTENT).body("No income found for the user");
                }
                
                return ResponseEntity.ok(response);
                
            } catch (IllegalArgumentException e) {
                return ResponseEntity.badRequest().body(e.getMessage());
            } catch (Exception e) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error processing request: " + e.getMessage());
            }
        }

       @GetMapping("/test-reconcile/{accountId}")
public ResponseEntity<String> testReconcile(@PathVariable int accountId) {
    plaidService.reconcileTransactionsByAccountId(accountId);
    return ResponseEntity.ok("Manual reconciliation complete.");
}
   @PostMapping("/manual-reconcile")
    public ResponseEntity<?> manualReconcile(@RequestBody List<Long> txnIds) {
        try {
            log.info("Received manual-reconcile request with transaction IDs: {}", txnIds);
            if (txnIds == null || txnIds.isEmpty()) {
                log.warn("Empty or null transaction ID list received");
                return ResponseEntity.badRequest().body("Transaction ID list is empty or null");
            }
            List<Integer> intIds = txnIds.stream()
                                        .map(Long::intValue)
                                        .collect(Collectors.toList());
            reconcileService.confirmReconciliation(intIds);
            log.info("Reconciliation completed successfully for transaction IDs: {}", txnIds);
            return ResponseEntity.ok("Reconciliation confirmed successfully");
        } catch (Exception e) {
            log.error("Error during manual reconciliation: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body("Failed to reconcile transactions: " + e.getMessage());
        }
    }

@PostMapping("/hide")
public ResponseEntity<?> hideTransactions(@RequestBody Map<String, List<Integer>> request) {
    try {
        List<Integer> transactionIds = request.get("transactionIds");
        if (transactionIds == null || transactionIds.isEmpty()) {
            return ResponseEntity.badRequest().body("No transaction IDs provided");
        }

        List<Transactions> transactions = transactionRepository.findAllById(transactionIds);
        transactions.forEach(t -> t.setRemoved(true));
        transactionRepository.saveAll(transactions);
        
        return ResponseEntity.ok().build();
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error hiding transactions: " + e.getMessage());
    }
}
@GetMapping("/hidden/user/{userId}")
public ResponseEntity<List<TransactionDto>> getHiddenTransactionsByUserId(@PathVariable Long userId) {
    List<Transactions> hidden = transactionRepository.findAllByRemovedTrueAndUserId(userId);

    List<TransactionDto> result = hidden.stream().map(t -> {
        TransactionDto dto = new TransactionDto();
        dto.setId(t.getId());
        dto.setTransactionDate(t.getTransactionDate());
        dto.setInsertDateTime(t.getInsertDateTime());
        dto.setUpdateDateTime(t.getUpdateDateTime());
        dto.setDescription(t.getDescription());
        dto.setTransactionAmount(t.getTransactionAmount());
        dto.setCategory(t.getCategory());
        dto.setReconcileId(t.getReconcileId());
        dto.setRemoved(t.getRemoved());

        if (t.getAccount() != null) {
            dto.setAccountName(t.getAccount().getAccountName());
            dto.setAccountId(t.getAccount().getId());
        }

        if (t.getUser() != null) {
            dto.setUserId(t.getUser().getId());
        }

        return dto;
    }).collect(Collectors.toList());

    return ResponseEntity.ok(result);
}


@PutMapping("/{transactionId}")
public ResponseEntity<?> updateTransaction(
        @PathVariable Integer transactionId,  // Changed from Long to Integer
        @RequestBody TransactionDto transactionDto) {
    try {
        // Find the existing transaction
        Transactions existingTransaction = transactionRepository.findById(transactionId)
                .orElseThrow(() -> new RuntimeException("Transaction not found with id: " + transactionId));

        // Update the transaction fields
        existingTransaction.setTransactionDate(transactionDto.getTransactionDate());
        existingTransaction.setDescription(transactionDto.getDescription());
        existingTransaction.setCategory(transactionDto.getCategory());
        existingTransaction.setTransactionAmount(transactionDto.getTransactionAmount());
        existingTransaction.setMerchantName(transactionDto.getMerchantName());
        existingTransaction.setTax(transactionDto.getTax());
        existingTransaction.setNotes(transactionDto.getNotes());
        existingTransaction.setTag(transactionDto.getTag());
        existingTransaction.setHideFromBudget(transactionDto.isHideFromBudget());
        // Update other fields as needed
        if (transactionDto.getRemoved() != null) {
            existingTransaction.setRemoved(transactionDto.getRemoved());
        }

        // Update account if changed
        if (transactionDto.getAccountId() != null && 
            (existingTransaction.getAccount() == null || 
             existingTransaction.getAccount().getId() != transactionDto.getAccountId())) {  // Changed to use != for primitive int
            Accounts account = accountRepository.findById(transactionDto.getAccountId())
                    .orElseThrow(() -> new RuntimeException("Account not found"));
            existingTransaction.setAccount(account);
        }

        // Save the updated transaction
        transactionService.saveTransaction(existingTransaction);
        
        // Refresh the entity to get any database-generated values
        Transactions updatedTransaction = transactionRepository.findById(transactionId)
                .orElse(existingTransaction);

        // Convert to DTO and return
        TransactionDto updatedDto = convertToDto(updatedTransaction);
        return ResponseEntity.ok(updatedDto);
    } catch (RuntimeException e) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(Map.of("error", e.getMessage()));
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to update transaction: " + e.getMessage()));
    }
}

// Updated helper method to match your DTO
private TransactionDto convertToDto(Transactions transaction) {
    TransactionDto dto = new TransactionDto();
    dto.setId(transaction.getId());
    dto.setTransactionDate(transaction.getTransactionDate());
    dto.setDescription(transaction.getDescription());
    dto.setCategory(transaction.getCategory());
    dto.setTransactionAmount(transaction.getTransactionAmount());
    dto.setMerchantName(transaction.getMerchantName());
    dto.setRemoved(transaction.getRemoved());
    
    if (transaction.getAccount() != null) {
        dto.setAccountId(transaction.getAccount().getId());
        dto.setAccountName(transaction.getAccount().getAccountName()); // Assuming Accounts has getName()
    }
    
    if (transaction.getUser() != null) {
        dto.setUserId(transaction.getUser().getId());
    }
    
    // Set other fields as needed
    dto.setTransactionId(transaction.getTransactionId());
    dto.setReconcileId(transaction.getReconcileId());
    dto.setReconcileFlag(transaction.getReconcileFlag());
    dto.setTax(transaction.getTax());
    dto.setNotes(transaction.getNotes());
    dto.setTag(transaction.getTag());
    return dto;
}

@PostMapping("/{transactionId}")
    @Transactional
    public ResponseEntity<?> deleteTransaction(@PathVariable Integer transactionId) {
        try {
            // Check if transaction exists
            Transactions transaction = transactionRepository.findById(transactionId)
                .orElseThrow(() -> new RuntimeException("Transaction not found with id: " + transactionId));

            // For soft delete:
            transaction.setRemoved(true);
            transactionRepository.save(transaction);

            // For hard delete (uncomment if you prefer):
            // transactionRepository.deleteById(transactionId);

            return ResponseEntity.ok(Map.of(
                "message", "Transaction deleted successfully",
                "transactionId", transactionId,
                "isRemove", true  // Include this for soft delete
            ));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to delete transaction: " + e.getMessage()));
        }
    }
      @PostMapping("/hideFromBudget")
public ResponseEntity<Map<String, String>> hideTransactionsFromBudget(@RequestBody List<Integer> transactionIds) {
    log.info("Inside hideTransactionsFromBudget method of TransactionController");
    try {
        transactionService.hideTransactionsFromBudget(transactionIds);
        Map<String, String> response = new HashMap<>();
        response.put("message", "Transactions hidden from budget successfully");
        return ResponseEntity.ok(response);
    } catch (Exception e) {
        Map<String, String> errorResponse = new HashMap<>();
        errorResponse.put("error", "Error hiding transactions from budget: " + e.getMessage());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
}
}


