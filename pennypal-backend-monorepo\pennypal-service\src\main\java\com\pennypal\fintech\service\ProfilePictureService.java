package com.pennypal.fintech.service;

import com.pennypal.fintech.dto.ProfilePictureUploadDto;
import com.pennypal.fintech.dto.ProfilePictureResponseDto;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.exception.UserNotFoundException;
import com.pennypal.fintech.exception.ProfilePictureNotFoundException;
import com.pennypal.fintech.exception.InvalidFileException;
import com.pennypal.fintech.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class ProfilePictureService {
    
    private final UserRepository userRepository;
    
    // Allowed image types
    private static final List<String> ALLOWED_CONTENT_TYPES = Arrays.asList(
        "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"
    );
    
    // Maximum file size (5MB)
    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024;
    
    @Autowired
    public ProfilePictureService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }
    
    @Transactional
    public ProfilePictureResponseDto uploadProfilePicture(Integer userId, MultipartFile file) throws IOException {
        log.info("Uploading profile picture for user: {}", userId);
        
        // Validate user exists
        Users user = findUserById(userId);
        
        // Validate file
        validateFile(file);
        
        // Process and save profile picture
        updateUserProfilePicture(user, file.getBytes(), file.getContentType(), 
                                file.getOriginalFilename(), file.getSize());
        
        userRepository.save(user);
        
        log.info("Profile picture uploaded successfully for user: {}", userId);
        
        return createResponseDto(user);
    }
    
    @Transactional
    public ProfilePictureResponseDto uploadProfilePictureFromDto(ProfilePictureUploadDto dto) {
        log.info("Uploading profile picture from DTO for user: {}", dto.getUserId());
        
        // Validate user exists
        Users user = findUserById(dto.getUserId());
        
        // Validate data
        validateFileData(dto);
        
        // Process and save profile picture
        updateUserProfilePicture(user, dto.getData(), dto.getContentType(), 
                                dto.getFilename(), dto.getSize());
        
        userRepository.save(user);
        
        log.info("Profile picture uploaded successfully for user: {}", dto.getUserId());
        
        return createResponseDto(user);
    }
    
    public byte[] getProfilePictureData(Integer userId) {
        log.info("Retrieving profile picture data for user: {}", userId);
        
        Users user = findUserById(userId);
        
        if (user.getProfilePictureData() == null) {
            throw new ProfilePictureNotFoundException("Profile picture not found for user: " + userId);
        }
        
        return user.getProfilePictureData();
    }
    
    public ProfilePictureResponseDto getProfilePictureInfo(Integer userId) {
        log.info("Retrieving profile picture info for user: {}", userId);
        
        Users user = findUserById(userId);
        
        return createResponseDto(user);
    }
    
    @Transactional
    public boolean deleteProfilePicture(Integer userId) {
        log.info("Deleting profile picture for user: {}", userId);
        
        Users user = findUserById(userId);
        
        if (user.getProfilePictureData() == null) {
            throw new ProfilePictureNotFoundException("No profile picture found to delete for user: " + userId);
        }
        
        // Clear profile picture data
        clearProfilePictureData(user);
        
        userRepository.save(user);
        
        log.info("Profile picture deleted successfully for user: {}", userId);
        return true;
    }
    
    public String getContentType(Integer userId) {
        Users user = findUserById(userId);
        return user.getProfilePictureContentType();
    }
    
    // Helper methods
    private Users findUserById(Integer userId) {
        return userRepository.findById(userId)
                .orElseThrow(() -> new UserNotFoundException("User not found with id: " + userId));
    }
    
    private void updateUserProfilePicture(Users user, byte[] data, String contentType, 
                                         String filename, Long size) {
        user.setProfilePictureData(data);
        user.setProfilePictureContentType(contentType);
        user.setProfilePictureFilename(filename);
        user.setProfilePictureSize(size);
        user.setProfilePictureUrl("/api/users/" + user.getId() + "/profile-picture");
        user.setUpdateDateTime(LocalDateTime.now());
    }
    
    private void clearProfilePictureData(Users user) {
        user.setProfilePictureData(null);
        user.setProfilePictureContentType(null);
        user.setProfilePictureFilename(null);
        user.setProfilePictureSize(null);
        user.setProfilePictureUrl(null);
        user.setUpdateDateTime(LocalDateTime.now());
    }
    
    private ProfilePictureResponseDto createResponseDto(Users user) {
        boolean hasProfilePicture = user.getProfilePictureData() != null;
        
        return new ProfilePictureResponseDto(
            user.getId(),
            hasProfilePicture ? user.getProfilePictureUrl() : null,
            user.getProfilePictureFilename(),
            user.getProfilePictureContentType(),
            user.getProfilePictureSize(),
            hasProfilePicture
        );
    }
    
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new InvalidFileException("File is required");
        }
        
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new InvalidFileException("File size exceeds maximum limit of 5MB");
        }
        
        String contentType = file.getContentType();
        if (contentType == null || !ALLOWED_CONTENT_TYPES.contains(contentType.toLowerCase())) {
            throw new InvalidFileException("Invalid file type. Only JPEG, PNG, GIF, and WebP files are allowed");
        }
        
        String filename = file.getOriginalFilename();
        if (filename == null || filename.trim().isEmpty()) {
            throw new InvalidFileException("Invalid filename");
        }
        
        // Additional security check for file extension
        if (!isValidFileExtension(filename)) {
            throw new InvalidFileException("Invalid file extension");
        }
    }
    
    private void validateFileData(ProfilePictureUploadDto dto) {
        if (dto.getData() == null || dto.getData().length == 0) {
            throw new InvalidFileException("File data is required");
        }
        
        if (dto.getSize() == null || dto.getSize() > MAX_FILE_SIZE) {
            throw new InvalidFileException("File size exceeds maximum limit of 5MB");
        }
        
        String contentType = dto.getContentType();
        if (contentType == null || !ALLOWED_CONTENT_TYPES.contains(contentType.toLowerCase())) {
            throw new InvalidFileException("Invalid file type. Only JPEG, PNG, GIF, and WebP files are allowed");
        }
        
        if (dto.getFilename() == null || dto.getFilename().trim().isEmpty()) {
            throw new InvalidFileException("Invalid filename");
        }
        
        // Additional security check for file extension
        if (!isValidFileExtension(dto.getFilename())) {
            throw new InvalidFileException("Invalid file extension");
        }
    }
    
    private boolean isValidFileExtension(String filename) {
        String extension = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
        return Arrays.asList("jpg", "jpeg", "png", "gif", "webp").contains(extension);
    }
}