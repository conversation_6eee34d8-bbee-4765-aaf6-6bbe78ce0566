package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.SvgIconDto;
import com.pennypal.fintech.dto.SvgInfoDto;
import com.pennypal.fintech.entity.SvgIcon;
import com.pennypal.fintech.service.SvgIconService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.nio.charset.StandardCharsets;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/api/icons")
@Tag(name = "SVG Icons", description = "APIs for managing SVG icons for financial institutions and stocks")
public class SvgIconController {

    private final SvgIconService svgIconService;

    @Autowired
    public SvgIconController(SvgIconService svgIconService) {
        this.svgIconService = svgIconService;
    }

    // ✅ Returns icons with SVG content as string in DTO
    @GetMapping("/list")
    @Operation(summary = "Get all SVG icons",
               description = "Retrieves all available SVG icons with their content as string")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved all icons",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of SVG icon DTOs")
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<List<SvgIconDto>> getAllIcons() {
        List<SvgIcon> icons = svgIconService.getAllIcons();
        List<SvgIconDto> iconDtos = icons.stream()
                .map(icon -> new SvgIconDto(
                        icon.getId(),
                        icon.getDescription(),
                        icon.getIconName(),
                        icon.getTickerSymbol(),
                        new String(icon.getSvgContent(), StandardCharsets.UTF_8)
                ))
                .collect(Collectors.toList());

        return ResponseEntity.ok(iconDtos);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get SVG icon by ID",
               description = "Retrieves a specific SVG icon by its unique identifier")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved the icon",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "SVG icon DTO")
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Icon not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<SvgIconDto> getIconById(
            @Parameter(description = "Unique identifier of the SVG icon", required = true)
            @PathVariable Integer id) {
        return svgIconService.getIconById(id)
                .map(icon -> ResponseEntity.ok(new SvgIconDto(
                        icon.getId(),
                        icon.getDescription(),
                        icon.getIconName(),
                          icon.getTickerSymbol(),
                        new String(icon.getSvgContent(), StandardCharsets.UTF_8)
                )))
                .orElse(ResponseEntity.notFound().build());
    }

    // New endpoint that returns base64 encoded SVG content as JSON
    @GetMapping("/svg/{iconName}")
    @Operation(summary = "Get SVG icon by name",
               description = "Retrieves a specific SVG icon by its name and returns the SVG content as JSON")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved the icon",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "SVG icon DTO with content")
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Icon not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<?> getIconByName(
            @Parameter(description = "Name of the SVG icon", required = true)
            @PathVariable String iconName) {
        return svgIconService.getIconByName(iconName)
                .map(icon -> {
                    String svgContent = new String(icon.getSvgContent(), StandardCharsets.UTF_8);
                    // Return as JSON with the SVG content
                    return ResponseEntity.ok()
                            .contentType(MediaType.APPLICATION_JSON)
                            .body(new SvgIconDto(
                                    icon.getId(),
                                    icon.getDescription(),
                                    icon.getIconName(),
                                         icon.getTickerSymbol(),
                                    svgContent
                            ));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/url")
    @Operation(summary = "Save SVG icon from URL or content",
               description = "Saves a new SVG icon either by downloading from a URL or from provided SVG content")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "201",
            description = "Successfully saved the icon",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "Saved SVG icon entity")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request or error processing SVG",
            content = @Content(mediaType = "text/plain")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<?> saveIconFromUrl(
            @Parameter(description = "SVG information containing URL or content, name, description, and ticker symbol", required = true)
            @RequestBody SvgInfoDto dto) {
        
        try {
            SvgIcon savedIcon;
            if (dto.getSvgContent() != null && !dto.getSvgContent().isEmpty()) {
                // Save from provided SVG content
                savedIcon = svgIconService.saveIconFromContent(dto.getSvgContent(), dto.getIconName(), dto.getDescription(), dto.getTickerSymbol());
            } else {
                // Download from URL
                savedIcon = svgIconService.saveIconFromUrl(dto.getUrl(), dto.getIconName(), dto.getDescription(), dto.getTickerSymbol());

            }
            
            return ResponseEntity.status(HttpStatus.CREATED).body(savedIcon);
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body("Error processing SVG: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(e.getMessage());
        }
    
    }
}