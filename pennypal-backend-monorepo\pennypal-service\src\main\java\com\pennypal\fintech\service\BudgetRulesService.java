package com.pennypal.fintech.service;

import com.pennypal.fintech.dto.BudgetRuleDto;
import com.pennypal.fintech.dto.TransactionDto;

import com.pennypal.fintech.entity.Budget;
import com.pennypal.fintech.entity.BudgetRules;
import com.pennypal.fintech.entity.Category;
import com.pennypal.fintech.entity.CustomSubCategory;
import com.pennypal.fintech.entity.SubCategory;
import com.pennypal.fintech.entity.Transactions;
import com.pennypal.fintech.entity.Users;

import com.pennypal.fintech.repository.AccountRepository;
import com.pennypal.fintech.repository.BudgetRepository;
import com.pennypal.fintech.repository.BudgetRulesRepository;
import com.pennypal.fintech.repository.CategoryRepository;
import com.pennypal.fintech.repository.CustomSubCategoryRepository;
import com.pennypal.fintech.repository.SubCategoryRepository;
import com.pennypal.fintech.repository.TransactionRepository;
import com.pennypal.fintech.repository.UserRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class BudgetRulesService {

    @Autowired
    private final AccountRepository accountRepository;    
    @Autowired
    private final BudgetRepository budgetRepository;
    @Autowired
    private final BudgetRulesRepository budgetRulesRepository;
    @Autowired
    private final CategoryRepository categoryRepository;
    @Autowired
    private final CustomSubCategoryRepository customSubCategoryRepository;
    @Autowired
    private final SubCategoryRepository subCategoryRepository;
    @Autowired
    private final TransactionRepository transactionRepository;
    @Autowired
    private final UserRepository userRepository;
    @Autowired
    private final BudgetService budgetService;
    @Autowired
    private final TransactionService transactionService;
    @Autowired
    private final CacheManager cacheManager;

    public BudgetRulesService(BudgetRepository budgetRepository,
                              BudgetRulesRepository budgetRulesRepository,
                              CategoryRepository categoryRepository,
                              CustomSubCategoryRepository customSubCategoryRepository,
                              SubCategoryRepository subCategoryRepository,
                              TransactionRepository transactionRepository,
                              UserRepository userRepository,
                              BudgetService budgetService,
                              TransactionService transactionService,
                              AccountRepository accountRepository,
                              CacheManager cacheManager) {
        this.budgetRepository = budgetRepository;
        this.budgetRulesRepository = budgetRulesRepository;
        this.categoryRepository = categoryRepository;
        this.customSubCategoryRepository = customSubCategoryRepository;
        this.subCategoryRepository = subCategoryRepository;
        this.transactionRepository = transactionRepository;
        this.userRepository = userRepository;
        this.budgetService = budgetService;
        this.transactionService = transactionService;
        this.accountRepository = accountRepository;
        this.cacheManager = cacheManager;
    }

    @CacheEvict(value = "budgetRulesCache",
                key = "'getBudgetRulesByUserId' + '_' + #dto.userId",
                allEntries = true)
    @Transactional
    public BudgetRuleDto createBudgetRule(BudgetRuleDto dto) {
        log.info("Inside createBudgetRule in BudgetRulesService");

        if (dto.getUserId() == null) {
            log.error("User ID must be provided");
            throw new RuntimeException("User ID must be provided");
        }
        if (!userRepository.existsById(dto.getUserId())) {
            log.error("User not found");
            throw new RuntimeException("User not found");
        }

        if (dto.getRuleType() == null) {
            log.error("Rule type must be provided");
            throw new RuntimeException("Rule type must be provided");
        }

        if (dto.getFromCategoryId() == null) {
            log.error("From category ID must be provided");
            throw new RuntimeException("From category ID must be provided");
        }
        if (!categoryRepository.existsById(dto.getFromCategoryId())) {
            log.error("From category not found");
            throw new RuntimeException("From category not found");
        }

        if (dto.getFromSubCategoryId() != null && !subCategoryRepository.existsById(dto.getFromSubCategoryId())) {
            log.error("From sub-category not found");
            throw new RuntimeException("From sub-category not found");
        }

        if (dto.getToCategoryId() == null) {
            log.error("To category ID must be provided");
            throw new RuntimeException("To category ID must be provided");
        }
        if (!categoryRepository.existsById(dto.getToCategoryId())) {
            log.error("To category not found");
            throw new RuntimeException("To category not found");
        }

        if (dto.getToCustomSubCategoryId() != null && !customSubCategoryRepository.existsByCustomSubCategoryId(dto.getToCustomSubCategoryId())) {
            log.error("To custom sub-category not found");
            throw new RuntimeException("To custom sub-category not found");
        }

        if (dto.getToSubCategoryId() != null && !subCategoryRepository.existsById(dto.getToSubCategoryId())) {
            log.error("To sub-category not found");
            throw new RuntimeException("To sub-category not found");
        }

        if (dto.getConditionType() == null) {
            log.error("Condition type must be provided");
            throw new RuntimeException("Condition type must be provided");
        }
        
        // To handle both create and edit operations
        BudgetRules rule;
        if (dto.getId() != null) {
            rule = budgetRulesRepository.findById(dto.getId())
                .orElseThrow(() -> new RuntimeException("Budget rule not found"));
            rule.setId(dto.getId());
            log.info("Editing existing budget rule with ID: {}", dto.getId());
        } else {
            rule = new BudgetRules();
            log.info("Creating new budget rule");
        }
        
        rule.setUserId(dto.getUserId());
            
        rule.setRuleType("Custom");
        
        rule.setFromCategoryId(dto.getFromCategoryId());
            
        if (dto.getFromSubCategoryId() != null) {
            rule.setFromSubCategoryId(dto.getFromSubCategoryId());
            String fromSubCatName = subCategoryRepository.findById(dto.getFromSubCategoryId())
                .orElseThrow(() -> new RuntimeException("Subcategory not found"))
                .getSubCategory();
            String fromCatName = categoryRepository.findById(dto.getFromCategoryId())
                .orElseThrow(() -> new RuntimeException("Category not found"))
                .getCategory();
            rule.setFromCatName(fromSubCatName + " - " + fromCatName);
        } else {
            rule.setFromSubCategoryId(null);
            rule.setFromCatName(null);
        }
        
        rule.setToCategoryId(dto.getToCategoryId());
        if (dto.getToCustomSubCategoryId() != null) {
            rule.setToCustomSubCategoryId(dto.getToCustomSubCategoryId());
            String toSubCatName = customSubCategoryRepository.findByCustomSubCategoryId(dto.getToCustomSubCategoryId())
                .getCustomSubCategoryName();
            log.info("Custom sub-category name: {}", toSubCatName);
            String toCatName = categoryRepository.findById(dto.getToCategoryId())
                .orElseThrow(() -> new RuntimeException("Category not found"))
                .getCategory();
            log.info("Category name: {}", toCatName);
            rule.setToCatName(toSubCatName + " - " + toCatName);
        } else {
            rule.setToCustomSubCategoryId(null);
        }
            
        if (dto.getToSubCategoryId() != null) {
            rule.setToSubCategoryId(dto.getToSubCategoryId());
            String toSubCatName = subCategoryRepository.findById(dto.getToSubCategoryId())
                .orElseThrow(() -> new RuntimeException("Subcategory not found"))
                .getSubCategory();
            String toCatName = categoryRepository.findById(dto.getToCategoryId())
                .orElseThrow(() -> new RuntimeException("Category not found"))
                .getCategory();
            rule.setToCatName(toSubCatName + " - " + toCatName);
        } else {
            rule.setToSubCategoryId(null);
            // rule.setToCatName(null);
        }
        
        rule.setConditionType(dto.getConditionType());
        
        // if (dto.getConditionType() == BudgetRules.ConditionType.THRESHOLD || dto.getConditionType() == BudgetRules.ConditionType.PERCENTAGE) {
        //     if (dto.getThresholdAmount() == null) {
        //         throw new RuntimeException("Threshold amount must be provided");
        //     }
        //     rule.setThresholdAmount(dto.getThresholdAmount());
        // }

        // if (dto.getConditionType() == BudgetRules.ConditionType.PERCENTAGE) {
        //     if (dto.getThresholdPercentage() == null) {
        //         throw new RuntimeException("Threshold percentage must be provided");
        //     }
        //     rule.setThresholdPercentage(dto.getThresholdPercentage());
        // }
        
        // if (dto.getConditionType() == BudgetRules.ConditionType.DATE) {
        //     if (dto.getTransferDay() == null) {
        //         throw new RuntimeException("Transfer day must be provided");
        //     }
        //     rule.setTransferDay(dto.getTransferDay());
        // }

        // if (dto.getConditionType() == BudgetRules.ConditionType.MERCHANT) {
        //     if (dto.getMerchantNamePattern() == null) {
        //         throw new RuntimeException("Merchant name pattern must be provided");
        //     }
        //     rule.setMerchantNamePattern(dto.getMerchantNamePattern());

        //     // Handle cascade renaming
        //     if (dto.getCascadeFlag() == true && dto.getRenamedMerchant() != null) {
        //         cascadeRenameExistingTransactions(
        //             dto.getUserId(),
        //             dto.getMerchantNamePattern(),
        //             dto.getRenamedMerchant()
        //         );
        //     }
        // }

        if (dto.getThresholdAmount() != null) {
            rule.setThresholdAmount(dto.getThresholdAmount());
            // Set associated transaction type and comparison operator
            rule.setAmountType(dto.getAmountType());
            rule.setAmountMatch(dto.getAmountMatch());
        } else {
            rule.setThresholdAmount(null);
            rule.setAmountType(null);
            rule.setAmountMatch(null);
        }

        if (dto.getThresholdPercentage() != null) {
            rule.setThresholdPercentage(dto.getThresholdPercentage());
        }
        if (dto.getTransferDay() != null) {
            rule.setTransferDay(dto.getTransferDay());
        }

        if (dto.getMerchantNamePattern() != null) {
            rule.setMerchantNamePattern(dto.getMerchantNamePattern());
            // Set associated regex flag
            rule.setMerchantMatchRegex(dto.getMerchantMatchRegex());
        } else {
            rule.setMerchantNamePattern(null);
            rule.setMerchantMatchRegex(null);
        }

        rule.setIsActive(dto.getIsActive());

        if (dto.getMaxTransferAmount() != null) {
            rule.setMaxTransferAmount(dto.getMaxTransferAmount());
        }
        if (dto.getMinTransferAmount() != null) {
            rule.setMinTransferAmount(dto.getMinTransferAmount());
        }
        if (dto.getMaxTransferPercent() != null) {
            rule.setMaxTransferPercent(dto.getMaxTransferPercent());
        }
        if (dto.getMinTransferPercent() != null) {
            rule.setMinTransferPercent(dto.getMinTransferPercent());
        }
        if (dto.getExecutionFrequency() != null) {
            rule.setExecutionFrequency(dto.getExecutionFrequency());
        }

        if (dto.getRenamedMerchant() != null) {
            rule.setRenamedMerchant(dto.getRenamedMerchant());
        } else {
            rule.setRenamedMerchant(null);
        }

        if (dto.getCascadeFlag() != null) {
            rule.setCascadeFlag(dto.getCascadeFlag());
            cascadeRenameExistingTransactions(dto);
        } else {
            rule.setCascadeFlag(false);
        }

        if (dto.getAccountId() != null) {
            rule.setAccountId(dto.getAccountId());
            String accountName = accountRepository.findById(dto.getAccountId())
                .orElseThrow(() -> new RuntimeException("Account not found"))
                .getAccountName();
            String accountMask = accountRepository.findById(dto.getAccountId())
                .orElseThrow(() -> new RuntimeException("Account not found"))
                .getAccountMask();
            rule.setAccountName(accountName + " - ****" + accountMask);
        } else {
            rule.setAccountId(null);
            rule.setAccountName(null);
        }

        if (dto.getHideTransactionsFlag() != null) {
            rule.setHideTransactionsFlag(dto.getHideTransactionsFlag());
        } else {
            rule.setHideTransactionsFlag(false);
        }

        if (dto.getTags() != null) {
            rule.setTags(dto.getTags());
        } else {
            rule.setTags(null);
        }

        if (dto.getGoal() != null) {
            rule.setGoal(dto.getGoal());
        } else {
            rule.setGoal(null);
        }

        if (rule.getId() == null) {
            rule.setCreatedAt(LocalDateTime.now());
        } else {
            rule.setCreatedAt(rule.getCreatedAt());
            rule.setUpdatedAt(LocalDateTime.now());
        }

        BudgetRules savedRule = budgetRulesRepository.save(rule);
        return convertToDto(savedRule);
    }

    @Cacheable(value = "budgetRulesCache",
               key = "#root.methodName + '_' + #userId",
               unless = "#result == null")
    public List<BudgetRuleDto> getBudgetRulesByUserId(Integer userId) {
        List<BudgetRules> rules = budgetRulesRepository.findByUserId(userId);
        return rules.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    // Process merchant category budget rules; also, updates budget with transaction
    public void processMerchantCategoryRules(TransactionDto transaction) {
        log.info("Processing merchant category rules for transaction: {}", transaction);

        // Fetch merchant pattern budget rules for the user
        List<BudgetRules> merchantRules = budgetRulesRepository.findByUserIdAndConditionTypeAndIsActiveOrderByUpdatedAtDesc(
            transaction.getUserId(),
            BudgetRules.ConditionType.MERCHANT,
            true
        );
        log.info("Fetched merchant rules: {}", merchantRules);

        // If no merchant rules, update budget with transaction as such
        if (merchantRules == null || merchantRules.isEmpty()) {
            budgetService.updateBudgetWithTxn(transaction);
            return;
        }

        // Update budget with transaction before applying any rules
        budgetService.updateBudgetWithTxn(transaction);
        
        for (BudgetRules rule : merchantRules) {
            // Check if the transaction's merchant name matches the rule's pattern
            if (matchesMerchantPattern(transaction, rule)) {
                // Apply the merchant category budget rule if matches
                applyMerchantCategoryRule(transaction, rule);
                break; // Apply only the first matching rule
            }
        }
    }

    // private boolean matchesMerchantPattern(String merchantName, BudgetRules rule) {
    //     return merchantName.toLowerCase()
    //         .contains(rule.getMerchantNamePattern().toLowerCase());
    // }

    private boolean matchesMerchantPattern(TransactionDto transaction, BudgetRules rule) {
        log.info("Checking rule: {}", rule);
        // Check merchant name pattern match
        boolean merchantMatch = true;
        if (rule.getMerchantNamePattern() != null) {
            if (rule.getMerchantMatchRegex() == true) {
                merchantMatch = transaction.getDescription().toLowerCase()
                    .contains(rule.getMerchantNamePattern().toLowerCase());
            } else {
                merchantMatch = transaction.getDescription().toLowerCase()
                    .equals(rule.getMerchantNamePattern().toLowerCase());
            }
        }
        log.info("Merchant match: {}", merchantMatch);
        if (!merchantMatch) return false;
    
        // Check amount type and threshold conditions
        if (rule.getThresholdAmount() != null) {
            BigDecimal txnAmount = BigDecimal.valueOf(Math.abs(transaction.getTransactionAmount()));
            BigDecimal thresholdAmount = rule.getThresholdAmount();
    
            // Check amount type (credit/debit)
            boolean amountTypeMatch = true;
            // ENABLE THIS AFTER CHECKING TRANSACTION DTO
            // if (rule.getAmountType() != null) {
            //     switch (rule.getAmountType().toUpperCase()) {
            //         case "CREDIT":
            //             amountTypeMatch = "credit".equals(transaction.getTransactionType());
            //             break;
            //         case "DEBIT":
            //             amountTypeMatch = "debit".equals(transaction.getTransactionType());
            //             break;
            //         default:
            //             amountTypeMatch = false;
            //     }
            // }
            if (!amountTypeMatch) return false;
    
            // Check amount match condition
            boolean amountMatch = true;
            if (rule.getAmountMatch() != null) {
                switch (rule.getAmountMatch().toUpperCase()) {
                    case "EQUALS":
                        amountMatch = txnAmount.compareTo(thresholdAmount) == 0;
                        break;
                    case "GREATER":
                        amountMatch = txnAmount.compareTo(thresholdAmount) > 0;
                        break;
                    case "LESSER":
                        amountMatch = txnAmount.compareTo(thresholdAmount) < 0;
                        break;
                    default:
                        amountMatch = false;
                }
            }
            log.info("Amount match: {}", amountMatch);
            if (!amountMatch) return false;
        }
    
        // Check from category match
        if (rule.getFromSubCategoryId() != null) {
            SubCategory fromSubCategory = subCategoryRepository.findById(rule.getFromSubCategoryId())
                .orElse(null);
            if (fromSubCategory == null || 
                // !fromSubCategory.getSubCategory().equals(transaction.getCategory())) {
                !rule.getFromSubCategoryId().equals(transaction.getSubCategoryId())) {
                return false;
            }
        }
        log.info("From category match: true");
    
        // Check account match
        if (rule.getAccountId() != null) {
            if (transaction.getAccountId() == null || 
                transaction.getAccountId() != rule.getAccountId()) {
                return false;
            }
        }
        log.info("Account match: true");
    
        return true;
    }

    private void applyMerchantCategoryRule(TransactionDto transaction, BudgetRules rule) {
        try {
            // Get target sub-category
            // SubCategory toSubCategory = subCategoryRepository.findById(rule.getToSubCategoryId())
            //     .orElseThrow(() -> new RuntimeException("Target sub-category not found"));
            
            // Find the transaction entity
            Transactions transactionEntity = transactionRepository.findById(transaction.getId())
                .orElseThrow(() -> new RuntimeException("Transaction not found"));
            
            // Save the transaction's original category for 'from' budget update
            String fromSubCategory = transactionEntity.getCategory();

            // Update the category of the fetched transaction entity
            // transactionEntity.setCategory(toSubCategory.getSubCategory());
            
            // Update the category of the transaction DTO as well
            // transaction.setCategory(toSubCategory.getSubCategory());

            if (rule.getToCustomSubCategoryId() != null) {
                CustomSubCategory customSubCategory = customSubCategoryRepository.findByCustomSubCategoryId(rule.getToCustomSubCategoryId());
                transactionEntity.setCategory(customSubCategory.getCustomSubCategoryName());
                transactionEntity.setSubCategoryId(null);
                transactionEntity.setCustomSubCategoryId(rule.getToCustomSubCategoryId());
                transaction.setCategory(customSubCategory.getCustomSubCategoryName());
                transaction.setSubCategoryId(null);
                transaction.setCustomSubCategoryId(rule.getToCustomSubCategoryId());
            } else {
                SubCategory subCategory = subCategoryRepository.findById(rule.getToSubCategoryId())
                    .orElseThrow(() -> new RuntimeException("SubCategory not found: " + rule.getToSubCategoryId()));
                transactionEntity.setCategory(subCategory.getSubCategory());
                transactionEntity.setSubCategoryId(subCategory.getId());
                transactionEntity.setCustomSubCategoryId(null);
                transaction.setCategory(subCategory.getSubCategory());
                transaction.setSubCategoryId(subCategory.getId());
                transaction.setCustomSubCategoryId(null);
            }

            // Rename merchant if supplied by user
            if (rule.getRenamedMerchant() != null) {
                transactionEntity.setDescription(rule.getRenamedMerchant());
                transaction.setDescription(rule.getRenamedMerchant());
            }

            // Save the updated transaction
            // transactionService.saveTransaction(transactionEntity);
            transactionRepository.save(transactionEntity);
            
            // Update and save the user's rule last execution time
            rule.setLastExecutedAt(LocalDateTime.now());
            budgetRulesRepository.save(rule);

            // Update the budget with the modified transaction
            budgetService.updateBudgetWithTxnWithRule(transaction, rule, fromSubCategory);

        } catch (Exception e) {
            throw new RuntimeException("Failed to apply merchant category rule", e);
        }
    }

    private void cascadeRenameExistingTransactions(BudgetRuleDto dto) {
        
        List<Transactions> matchingTransactions = new ArrayList<>();
        
        // Filter transactions by dto.merchantNamePattern
        if (dto.getMerchantNamePattern() != null) {
            if (dto.getMerchantMatchRegex() == true) {
                matchingTransactions = transactionRepository
                .findByUserIdAndDescriptionLikePattern(
                    dto.getUserId(),
                    "%" + dto.getMerchantNamePattern() + "%"
                );
            } else {
                matchingTransactions = transactionRepository
                .findByUserIdAndDescription(
                    dto.getUserId(),
                    dto.getMerchantNamePattern()
                );
            }
        }

        // Filter transactions by dto.thresholdAmount
        if (dto.getThresholdAmount() != null) {
            matchingTransactions = matchingTransactions.stream()
                .filter(txn -> {
                    BigDecimal txnAmount = BigDecimal.valueOf(Math.abs(txn.getTransactionAmount()));
                    BigDecimal thresholdAmount = dto.getThresholdAmount();
                    
                    // First filter by amount type (credit/debit)
                    boolean amountTypeMatch = true;
                    if (dto.getAmountType() != null) {
                        switch (dto.getAmountType().toUpperCase()) {
                            case "CREDIT":
                                amountTypeMatch = txn.getTransactionType() == "credit";
                                break;
                            case "DEBIT":
                                amountTypeMatch = txn.getTransactionType() == "debit";
                                break;
                            default:
                                amountTypeMatch = false;
                        }
                    }

                    // Then filter by amount match condition
                    boolean amountMatch = true;
                    if (dto.getAmountMatch() != null) {
                        switch (dto.getAmountMatch().toUpperCase()) {
                            case "EQUALS":
                                amountMatch = txnAmount.compareTo(thresholdAmount) == 0;
                                break;
                            case "GREATER":
                                amountMatch = txnAmount.compareTo(thresholdAmount) > 0;
                                break;
                            case "LESSER":
                                amountMatch = txnAmount.compareTo(thresholdAmount) < 0;
                                break;
                            default:
                                amountMatch = false;
                        }
                    }
                    return amountTypeMatch && amountMatch;
                })
                .collect(Collectors.toList());
        }

        // Filter transaction's by dto.fromSubCategory
        if (dto.getFromSubCategoryId() != null) {
            String fromSubCategoryName = subCategoryRepository.findById(dto.getFromSubCategoryId())
                .orElseThrow(() -> new RuntimeException("Sub-category not found"))
                .getSubCategory();
            matchingTransactions = matchingTransactions.stream()
                .filter(txn -> txn.getCategory().equals(fromSubCategoryName))
                .collect(Collectors.toList());
        }

        // Filter transactions by dto.accountId
        if (dto.getAccountId() != null) {
            matchingTransactions = matchingTransactions.stream()
                .filter(txn -> txn.getAccount().getId() == dto.getAccountId())
                .collect(Collectors.toList());
        }
        
        if (!matchingTransactions.isEmpty()) {
            // Group transactions by YearMonth and category
            Map<YearMonth, Map<String, List<Transactions>>> transactionsByMonthAndCategory = 
                matchingTransactions.stream()
                    .collect(Collectors.groupingBy(
                        txn -> YearMonth.from(txn.getTransactionDate()),
                        Collectors.groupingBy(Transactions::getCategory)
                    ));
            
            // Find target subcategory using the rule's toSubCategoryId
            // SubCategory toSubCategory = subCategoryRepository.findById(dto.getToSubCategoryId())
            //     .orElseThrow(() -> new RuntimeException("Sub-category not found"));
            CustomSubCategory toCustomSubCategory;
            SubCategory toSubCategory;
            if (dto.getToCustomSubCategoryId() != null) {
                toCustomSubCategory = customSubCategoryRepository.findByCustomSubCategoryId(dto.getToCustomSubCategoryId());
                toSubCategory = null;
            } else {
                toSubCategory = subCategoryRepository.findById(dto.getToSubCategoryId())
                    .orElseThrow(() -> new RuntimeException("SubCategory not found: " + dto.getToSubCategoryId()));
                toCustomSubCategory = null;
            }
            
            // Process each month's transactions
            for (Map.Entry<YearMonth, Map<String, List<Transactions>>> monthEntry : 
                    transactionsByMonthAndCategory.entrySet()) {
                
                YearMonth month = monthEntry.getKey();
                Map<String, List<Transactions>> categoryGroups = monthEntry.getValue();

                // Process each source category group in each month
                for (Map.Entry<String, List<Transactions>> categoryEntry : categoryGroups.entrySet()) {
                    String sourceCategory = categoryEntry.getKey();
                    List<Transactions> categoryTransactions = categoryEntry.getValue();
                    
                    // Find source subcategory
                    SubCategory sourceSubCategory = subCategoryRepository.findBySubCategory(sourceCategory)
                        .orElseThrow(() -> new RuntimeException("Source sub-category not found: " + sourceCategory));
                    
                    // Calculate total amount for this month's transactions in this category
                    // Here, map function also applies threshold/min/max rules
                    BigDecimal monthlyTotal = categoryTransactions.stream()
                        .map(txn -> {
                            BigDecimal transactionAmount = BigDecimal.valueOf(txn.getTransactionAmount());
                            BigDecimal adjustedAmount = transactionAmount;

                            // Apply max transfer amount cap if specified
                            if (dto.getMaxTransferAmount() != null) {
                                adjustedAmount = adjustedAmount.min(dto.getMaxTransferAmount());
                            }

                            // Apply min transfer amount floor if specified
                            if (dto.getMinTransferAmount() != null) {
                                adjustedAmount = adjustedAmount.max(dto.getMinTransferAmount());
                            }

                            // Apply percentage-based limits if specified
                            if (dto.getMaxTransferPercent() != null) {
                                BigDecimal maxAmount = transactionAmount
                                    .multiply(dto.getMaxTransferPercent())
                                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                                adjustedAmount = adjustedAmount.min(maxAmount);
                            }

                            if (dto.getMinTransferPercent() != null) {
                                BigDecimal minAmount = transactionAmount
                                    .multiply(dto.getMinTransferPercent())
                                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                                adjustedAmount = adjustedAmount.max(minAmount);
                            }

                            return adjustedAmount;
                        })
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    
                    // Update source budget (subtract amounts)
                    Budget sourceBudget = budgetRepository
                        .findLatestBudgetByCategoryIdAndSubCategoryId(
                            dto.getUserId(),
                            sourceSubCategory.getCategory().getId(),
                            sourceSubCategory.getId(),
                            month.atDay(1)
                        ).orElse(null);
                    
                    if (sourceBudget != null) {
                        sourceBudget.setActual(sourceBudget.getActual().subtract(monthlyTotal));
                        sourceBudget.setRemaining(sourceBudget.getAllocated().subtract(sourceBudget.getActual()));
                        budgetRepository.save(sourceBudget);
                    }
                    
                    // Flag to track if the budget is newly created or existing
                    // Optional<Budget> existingBudget = budgetRepository
                    //     .findLatestBudgetByCategoryIdAndSubCategoryId(
                    //         dto.getUserId(),
                    //         dto.getToCategoryId(),
                    //         dto.getToSubCategoryId(),
                    //         month.atDay(1)
                    //     );
                    Optional<Budget> existingBudget;
                    if (dto.getToCustomSubCategoryId() != null) {
                        existingBudget = budgetRepository
                            .findLatestBudgetByCategoryIdAndCustomSubCategoryId(
                                dto.getUserId(),
                                dto.getToCategoryId(),
                                dto.getToCustomSubCategoryId(),
                                month.atDay(1)
                            );
                    } else {
                        existingBudget = budgetRepository
                            .findLatestBudgetByCategoryIdAndSubCategoryId(
                                dto.getUserId(),
                                dto.getToCategoryId(),
                                dto.getToSubCategoryId(),
                                month.atDay(1)
                            );
                    }

                    final boolean isNewBudget = !existingBudget.isPresent();

                    // Update destination budget (add amounts)
                    Budget destBudget = existingBudget
                        .orElseGet(() -> {
                            Budget newBudget = new Budget();
                            Users user = userRepository.findById(dto.getUserId())
                                .orElseThrow(() -> new RuntimeException("User not found: " + dto.getUserId()));
                            Category category = categoryRepository.findById(dto.getToCategoryId())
                                .orElseThrow(() -> new RuntimeException("Category not found: " + dto.getToCategoryId()));
                            // SubCategory subCategory = subCategoryRepository.findById(dto.getToSubCategoryId())
                            //     .orElseThrow(() -> new RuntimeException("SubCategory not found: " + dto.getToSubCategoryId()));
                            if (dto.getToCustomSubCategoryId() != null) {
                                newBudget.setCustomSubCategoryId(toCustomSubCategory.getCustomSubCategoryId());
                                newBudget.setSubCategory(null);
                            } else {
                                newBudget.setSubCategory(toSubCategory);
                                newBudget.setCustomSubCategoryId(null);
                            }
                            
                            newBudget.setUser(user);
                            newBudget.setCategory(category);
                            // newBudget.setSubCategory(subCategory);
                            newBudget.setDate(month.atDay(1));
                            newBudget.setAllocated(BigDecimal.ZERO);
                            newBudget.setActual(BigDecimal.ZERO);
                            newBudget.setRemaining(BigDecimal.ZERO);
                            return newBudget;
                        });
                    
                    // Update destination budget amounts
                    destBudget.setActual(destBudget.getActual().add(monthlyTotal));
                    destBudget.setRemaining(destBudget.getAllocated().subtract(destBudget.getActual()));
                    budgetRepository.save(destBudget);

                    // Update transactions
                    // for (Transactions transaction : categoryTransactions) {
                    //     transaction.setCategory(toSubCategory.getSubCategory());
                    //     if (dto.getRenamedMerchant() != null) {
                    //         transaction.setDescription(dto.getRenamedMerchant());
                    //     }
                    // }
                    if (dto.getToCustomSubCategoryId() != null) {
                        for (Transactions transaction : categoryTransactions) {
                            transaction.setCategory(toCustomSubCategory.getCustomSubCategoryName());
                            transaction.setSubCategoryId(null);
                            transaction.setCustomSubCategoryId(dto.getToCustomSubCategoryId());
                            if (dto.getRenamedMerchant() != null) {
                                transaction.setDescription(dto.getRenamedMerchant());
                            }
                        }
                    } else {
                        for (Transactions transaction : categoryTransactions) {
                            transaction.setCategory(toSubCategory.getSubCategory());
                            transaction.setSubCategoryId(toSubCategory.getId());
                            transaction.setCustomSubCategoryId(null);
                            if (dto.getRenamedMerchant() != null) {
                                transaction.setDescription(dto.getRenamedMerchant());
                            }
                        }
                    }

                    TransactionDto transactionDto = convertToTransactionDto(
                            categoryTransactions.get(0));

                    // Check notification rules for new budget
                    if (isNewBudget) {
                        budgetService.checkNewBudgetNotificationRules(
                            transactionDto,
                            destBudget
                        );
                    }
                    
                    // Check notification rules
                    if (destBudget.getAllocated().compareTo(BigDecimal.ZERO) > 0) {
                        String toSubCategoryName;
                        if (dto.getToCustomSubCategoryId() != null) {
                            toSubCategoryName = toCustomSubCategory.getCustomSubCategoryName();
                        } else {
                            toSubCategoryName = toSubCategory.getSubCategory();
                        }
                        budgetService.checkNotificationRules(
                            transactionDto,
                            destBudget,
                            toSubCategoryName
                        );
                    }
                }
            }
            
            // Batch save all updated transactions
            transactionRepository.saveAll(matchingTransactions);
        }
    }

    public void deleteBudgetRule(Integer ruleId) {
        if (!budgetRulesRepository.existsById(ruleId)) {
            throw new RuntimeException("Budget rule not found");
        }

        // Get userId
        BudgetRules rule = budgetRulesRepository.findById(ruleId).orElse(null);
        Integer userId = rule.getUserId();

        // Clear cache
        clearCacheByPatterns("budgetRulesCache", "getBudgetRulesByUserId_" + userId);
        
        // Delete rule
        budgetRulesRepository.deleteById(ruleId);
    }

    private BudgetRuleDto convertToDto(BudgetRules rule) {
        BudgetRuleDto dto = new BudgetRuleDto();
        dto.setId(rule.getId());
        dto.setUserId(rule.getUserId());
        dto.setRuleType(rule.getRuleType());
        dto.setFromCategoryId(rule.getFromCategoryId());
        dto.setFromSubCategoryId(rule.getFromSubCategoryId() != null ? 
            rule.getFromSubCategoryId() : null);
        dto.setToCategoryId(rule.getToCategoryId());
        dto.setToSubCategoryId(rule.getToSubCategoryId() != null ? 
            rule.getToSubCategoryId() : null);
        dto.setToCustomSubCategoryId(rule.getToCustomSubCategoryId() != null ? 
            rule.getToCustomSubCategoryId() : null);
        dto.setConditionType(rule.getConditionType());
        dto.setThresholdAmount(rule.getThresholdAmount() != null ?
            rule.getThresholdAmount() : null);
        dto.setThresholdPercentage(rule.getThresholdPercentage() != null ?
            rule.getThresholdPercentage() : null);
        dto.setTransferDay(rule.getTransferDay() != null ?
            rule.getTransferDay() : null);
        dto.setMerchantNamePattern(rule.getMerchantNamePattern() != null ?
            rule.getMerchantNamePattern() : null);
        dto.setIsActive(rule.getIsActive());
        dto.setMaxTransferAmount(rule.getMaxTransferAmount() != null ?
            rule.getMaxTransferAmount() : null);
        dto.setMinTransferAmount(rule.getMinTransferAmount() != null ?
            rule.getMinTransferAmount() : null);
        dto.setMaxTransferPercent(rule.getMaxTransferPercent() != null ?
            rule.getMaxTransferPercent() : null);
        dto.setMinTransferPercent(rule.getMinTransferPercent() != null ?
            rule.getMinTransferPercent() : null);
        dto.setExecutionFrequency(rule.getExecutionFrequency() != null ?
            rule.getExecutionFrequency() : null);
        dto.setRenamedMerchant(rule.getRenamedMerchant() != null ?
            rule.getRenamedMerchant() : null);
        dto.setCascadeFlag(rule.getCascadeFlag());
        dto.setToSubCategoryName(" ");
        dto.setMerchantMatchRegex(rule.getMerchantMatchRegex());
        dto.setAmountType(rule.getAmountType());
        dto.setAmountMatch(rule.getAmountMatch());
        dto.setAccountId(rule.getAccountId());
        dto.setHideTransactionsFlag(rule.getHideTransactionsFlag());
        dto.setTags(rule.getTags());
        dto.setGoal(rule.getGoal());
        dto.setFromCatName(rule.getFromCatName());
        dto.setToCatName(rule.getToCatName());
        dto.setAccountName(rule.getAccountName());
        return dto;
    }

    private TransactionDto convertToTransactionDto(Transactions transaction) {
        TransactionDto dto = new TransactionDto();

        dto.setId(transaction.getId());
        dto.setCategory(transaction.getCategory());
        dto.setDescription(transaction.getDescription());
        dto.setTransactionAmount(transaction.getTransactionAmount().doubleValue());
        dto.setTransactionDate(transaction.getTransactionDate());
        dto.setAccountId(transaction.getAccount().getId());
        dto.setUserId(transaction.getUser().getId());
        dto.setSubCategoryId(transaction.getSubCategoryId());
        
        return dto;
    }

    public void clearCacheByPatterns(String cacheName, String pattern) {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
            if (cache != null && cache instanceof CaffeineCache caffeineCache) {
                com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache =
                    (com.github.benmanes.caffeine.cache.Cache<Object, Object>) caffeineCache.getNativeCache();
    
                // Remove all cache entries that match the provided pattern
                nativeCache.asMap().keySet().removeIf(key -> {
                    if (key instanceof String keyStr) {
                        return keyStr.startsWith(pattern);
                    }
                    return false;
                });
    
                log.info("Cleared cache entries matching pattern: {} from cache: {}",
                    pattern, cacheName);
            } else {
                log.warn("Cache '{}' not found or not a CaffeineCache instance", cacheName);
            }
        } catch (Exception e) {
            log.error("Error clearing cache '{}' with pattern: {}",
                cacheName, pattern, e);
        }
    }
}