package com.pennypal.fintech.dto;



// Make sure your SvgIconDto includes the tickerSymbol field
public class SvgIconDto {
    private Integer id;
    private String description;
    private String iconName;
    private String tickerSymbol; // Make sure this field exists
    private String svgContent;

    // Constructors
    public SvgIconDto() {}

    public SvgIconDto(Integer id, String description, String iconName, String tickerSymbol, String svgContent) {
        this.id = id;
        this.description = description;
        this.iconName = iconName;
        this.tickerSymbol = tickerSymbol;
        this.svgContent = svgContent;
    }

    // Getters and setters
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getIconName() { return iconName; }
    public void setIconName(String iconName) { this.iconName = iconName; }

    public String getTickerSymbol() { return tickerSymbol; }
    public void setTickerSymbol(String tickerSymbol) { this.tickerSymbol = tickerSymbol; }

    public String getSvgContent() { return svgContent; }
    public void setSvgContent(String svgContent) { this.svgContent = svgContent; }
}