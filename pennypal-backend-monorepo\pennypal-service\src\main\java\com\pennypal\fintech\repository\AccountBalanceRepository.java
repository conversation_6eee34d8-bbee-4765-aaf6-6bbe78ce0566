package com.pennypal.fintech.repository;

import com.pennypal.fintech.dto.AccountBalanceDto;
import com.pennypal.fintech.dto.AccountBalanceSummaryDto;
import com.pennypal.fintech.entity.AccountBalance;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.time.LocalDate;

import org.antlr.v4.runtime.atn.SemanticContext.AND;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

@Repository
public interface AccountBalanceRepository extends JpaRepository<AccountBalance, Integer> {
    
    @Query("SELECT ab FROM AccountBalance ab WHERE ab.account.id IN :accountIds AND ab.timestamp <= :endDate AND ab.timestamp >= :startDate ORDER BY ab.timestamp DESC LIMIT 1")
    Optional<AccountBalance> findLatestBalanceBeforeDateOptional(List<Integer> accountIds, LocalDateTime endDate, LocalDateTime startDate);
            
    @Query("SELECT ab FROM AccountBalance ab WHERE ab.account.id IN :accountIds AND ab.timestamp BETWEEN :startDate AND :endDate ORDER BY ab.timestamp DESC LIMIT 1")
    Optional<AccountBalance> findLatestBalanceForAccountInPeriod(List<Integer> accountIds, LocalDateTime startDate, LocalDateTime endDate);
    
    @Query(value = "SELECT " +
    "    a.user_id AS userId, " +
    "    a.account_type AS accountType, " +
    "    MONTHNAME(ab.timestamp) AS month, " +
    "    COUNT(DISTINCT a.id) AS numberOfAccounts, " +
    "    SUM(ab.balance) AS totalBalance " +
    "FROM " +
    "    account a " +
    "INNER JOIN " +
    "    account_balances ab ON a.id = ab.account_id " +
    "WHERE " +
    "    a.account_type IN ('credit', 'loan', 'investment', 'depository') " +
    "    AND a.user_id = :userId " +
    "    AND ab.timestamp = ( " +
    "        SELECT MAX(ab2.timestamp) " +
    "        FROM account_balances ab2 " +
    "        WHERE ab2.account_id = ab.account_id " +
    "        AND MONTH(ab2.timestamp) = MONTH(ab.timestamp) " +
    "    ) " +
    "GROUP BY " +
    "    a.user_id, " +
    "    a.account_type, " +
    "    MONTH(ab.timestamp), " +
    "    YEAR(ab.timestamp) " +
    "ORDER BY " +
    "    a.user_id, " +
    "    a.account_type, " +
    "    MONTH(ab.timestamp)", nativeQuery = true)
List<AccountBalanceDto> findAccountBalanceSummaryByUser(@Param("userId") Integer userId);

   @Query(value = """
           WITH FilteredData AS (
            -- Get relevant balances from the last x months
            SELECT ab.balance, ab.account_id, ab.user_id, ab.timestamp, a.account_type
            FROM account_balances ab
            JOIN accounts a ON ab.account_id = a.id
            WHERE ab.timestamp >= DATE_SUB(CURDATE(), INTERVAL :x MONTH) AND
                  ab.user_id = :userId
            ),
            GroupedData AS (
                -- Assign each transaction to a y-day group providing anchor point
                SELECT fd.user_id, fd.account_type, fd.account_id, fd.timestamp, fd.balance,
                    FLOOR(DATEDIFF(fd.timestamp, DATE_SUB(CURDATE(), INTERVAL :x MONTH)) / :y)
                       AS group_number
                FROM FilteredData fd
            ),
            LatestBalancePerAccount AS (
                -- Get the latest balance for each account_id in each group
                SELECT gd.user_id, gd.account_type, gd.account_id, gd.group_number,
                       MAX(gd.timestamp) AS latest_timestamp_in_group
                FROM GroupedData gd
                GROUP BY gd.user_id, gd.account_type, gd.account_id, gd.group_number
            ),
            LatestBalances AS (
                -- Fetch the actual latest balance values for each account_id
                SELECT lbpa.user_id, lbpa.account_type, lbpa.account_id, lbpa.group_number,
                       -- lbpa.latest_timestamp_in_group AS group_end_date, gd.balance
                       gd.balance
                FROM LatestBalancePerAccount lbpa
                JOIN GroupedData gd 
                  ON lbpa.user_id = gd.user_id AND
                     lbpa.account_type = gd.account_type AND
                     lbpa.account_id = gd.account_id AND
                     lbpa.latest_timestamp_in_group = gd.timestamp
            ),
            FinalAggregatedBalance AS (
                -- Sum balances for the same user_id and account_type within the same group
                SELECT user_id, account_type, group_number,
                       SUM(balance) AS aggregated_balance
                FROM LatestBalances
                GROUP BY user_id, account_type, group_number
            )
            SELECT user_id, account_type,
                   -- DATE(MAX(group_end_date)) AS group_end_date,
                   LEAST(
                    DATE(DATE_ADD(DATE_SUB(CURDATE(), INTERVAL :x MONTH), INTERVAL (group_number + 1) * :y - 1 DAY)),
                    CURDATE()
                    ) AS group_end_date,
                   SUM(aggregated_balance) AS final_balance
            FROM FinalAggregatedBalance
            GROUP BY user_id, account_type, group_number
            ORDER BY group_end_date, account_type;
           """, nativeQuery = true)
    List<Object[]> getAggregatedBalances(Integer userId, Integer x, Integer y);

    @Query(value = """
           WITH FilteredData AS (
            -- Get relevant balances from the last x months
            SELECT ab.balance, ab.account_id, ab.user_id, ab.timestamp, a.account_type
            FROM account_balances ab
            JOIN accounts a ON ab.account_id = a.id
            WHERE ab.timestamp >= DATE_SUB(CURDATE(), INTERVAL :x MONTH) AND
                  ab.user_id = :userId AND
                  a.account_type = :z
            ),
            GroupedData AS (
                -- Assign each transaction to a y-day group providing anchor point
                SELECT fd.user_id, fd.account_type, fd.account_id, fd.timestamp, fd.balance,
                    FLOOR(DATEDIFF(fd.timestamp, DATE_SUB(CURDATE(), INTERVAL :x MONTH)) / :y)
                       AS group_number
                FROM FilteredData fd
            ),
            LatestBalancePerAccount AS (
                -- Get the latest balance for each account_id in each group
                SELECT gd.user_id, gd.account_type, gd.account_id, gd.group_number,
                       MAX(gd.timestamp) AS latest_timestamp_in_group
                FROM GroupedData gd
                GROUP BY gd.user_id, gd.account_type, gd.account_id, gd.group_number
            ),
            LatestBalances AS (
                -- Fetch the actual latest balance values for each account_id
                SELECT lbpa.user_id, lbpa.account_type, lbpa.account_id, lbpa.group_number,
                       -- lbpa.latest_timestamp_in_group AS group_end_date, gd.balance
                       gd.balance
                FROM LatestBalancePerAccount lbpa
                JOIN GroupedData gd 
                  ON lbpa.user_id = gd.user_id AND
                     lbpa.account_type = gd.account_type AND
                     lbpa.account_id = gd.account_id AND
                     lbpa.latest_timestamp_in_group = gd.timestamp
            ),
            FinalAggregatedBalance AS (
                -- Sum balances for the same user_id and account_type within the same group
                SELECT user_id, account_type, group_number,
                       SUM(balance) AS aggregated_balance
                FROM LatestBalances
                GROUP BY user_id, account_type, group_number
            )
            SELECT user_id, account_type,
                   -- DATE(MAX(group_end_date)) AS group_end_date,
                   LEAST(
                    DATE(DATE_ADD(DATE_SUB(CURDATE(), INTERVAL :x MONTH), INTERVAL (group_number + 1) * :y - 1 DAY)),
                    CURDATE()
                    ) AS group_end_date,
                   SUM(aggregated_balance) AS final_balance
            FROM FinalAggregatedBalance
            GROUP BY user_id, account_type, group_number
            ORDER BY group_end_date, account_type;
           """, nativeQuery = true)
    List<Object[]> getAggregatedBalancesByAccountType(Integer userId, Integer x, Integer y, String z);


    @Query(value = """
       WITH CurrentLastDayBalances AS (
           SELECT 
               a.account_type, ab.balance, ab.timestamp,
               ROW_NUMBER() OVER (PARTITION BY a.id ORDER BY ab.timestamp DESC) as rn
           FROM account_balances ab
           JOIN accounts a ON ab.account_id = a.id
           WHERE ab.user_id = :userId AND
                 YEAR(ab.timestamp) = :year AND
                 MONTH(ab.timestamp) = :month
       ),
       CurrentMonthTotals AS (
           SELECT 
               account_type, SUM(balance) as current_balance
           FROM CurrentLastDayBalances
           WHERE rn = 1
           GROUP BY account_type
       ),
       PreviousLastDayBalances AS (
           SELECT 
               a.account_type, ab.balance, ab.timestamp,
               ROW_NUMBER() OVER (PARTITION BY a.id ORDER BY ab.timestamp DESC) as rn
           FROM account_balances ab
           JOIN accounts a ON ab.account_id = a.id
           WHERE ab.user_id = :userId
           AND ((MONTH(ab.timestamp) = :month - 1 AND YEAR(ab.timestamp) = :year) OR
                (MONTH(ab.timestamp) = 12 AND YEAR(ab.timestamp) = :year - 1 AND :month = 1))
       ),
       PreviousMonthTotals AS (
           SELECT 
               account_type, SUM(balance) as previous_balance
           FROM PreviousLastDayBalances
           WHERE rn = 1
           GROUP BY account_type
       )
       SELECT 
           c.account_type, c.current_balance, p.previous_balance,
           (c.current_balance - p.previous_balance) as delta_amount,
           CASE 
               WHEN p.previous_balance = 0 THEN 100
               ELSE ((c.current_balance - p.previous_balance) / p.previous_balance) * 100 
           END as delta_percentage
       FROM CurrentMonthTotals c
       LEFT JOIN PreviousMonthTotals p ON c.account_type = p.account_type
       """, nativeQuery = true)
    List<Object[]> getAccountBalanceMonthlyDeltas(Integer userId, Integer year, Integer month);

    @Query(value = """
        WITH RECURSIVE GroupNumbers AS (
            -- Generate exactly x groups (0 to x-1)
            SELECT 0 as group_number
            UNION ALL
            SELECT group_number + 1
            FROM GroupNumbers
            WHERE group_number < :x - 1
        ),
        UserAccounts AS (
            -- Get all accounts for the user
            SELECT DISTINCT a.id as account_id, a.account_name, a.account_category,
                   a.account_type, a.account_subtype, a.account_mask, a.institution_id
            FROM accounts a
            WHERE a.user_id = :userId
        ),
        AccountGroups AS (
            -- Cross join to ensure every account has every group
            SELECT ua.account_id, ua.account_name, ua.account_category, ua.account_type,
                   ua.account_subtype, ua.account_mask, ua.institution_id,
                   gn.group_number,
                   DATE(DATE_SUB(CURDATE(), INTERVAL gn.group_number * :y DAY)) as group_end_date,
                   DATE(DATE_SUB(CURDATE(), INTERVAL (gn.group_number + 1) * :y DAY)) as group_start_date
            FROM UserAccounts ua
            CROSS JOIN GroupNumbers gn
        ),
        FilteredData AS (
            -- Get relevant balances from the last x months
            SELECT ab.balance, ab.account_id, ab.user_id, ab.timestamp
            FROM account_balances ab
            JOIN accounts a ON ab.account_id = a.id
            WHERE ab.timestamp >= DATE_SUB(CURDATE(), INTERVAL :x MONTH) AND
                ab.user_id = :userId
        ),
        GroupedData AS (
            -- Assign each balance to the appropriate group
            SELECT fd.user_id, fd.account_id, fd.timestamp, fd.balance,
                ag.group_number, ag.account_name, ag.account_category, ag.account_type,
                ag.account_subtype, ag.account_mask, ag.institution_id, ag.group_end_date
            FROM FilteredData fd
            JOIN AccountGroups ag ON fd.account_id = ag.account_id
            WHERE fd.timestamp <= ag.group_end_date
              AND fd.timestamp > ag.group_start_date
        ),
        LatestBalancePerAccount AS (
            -- Get the latest balance for each account_id in each group
            SELECT gd.user_id, gd.account_id, gd.group_number,
                MAX(gd.timestamp) AS latest_timestamp_in_group,
                MAX(gd.account_name) AS account_name,
                MAX(gd.account_category) AS account_category,
                MAX(gd.account_type) AS account_type,
                MAX(gd.account_subtype) AS account_subtype,
                MAX(gd.account_mask) AS account_mask,
                MAX(gd.institution_id) AS institution_id,
                MAX(gd.group_end_date) AS group_end_date
            FROM GroupedData gd
            GROUP BY gd.user_id, gd.account_id, gd.group_number
        ),
        LatestBalances AS (
            -- Fetch the actual latest balance values for each account_id in each group
            SELECT lbpa.user_id, lbpa.account_id, lbpa.group_number,
                COALESCE(gd.balance, 0) as balance,
                lbpa.account_name, lbpa.account_category, lbpa.account_type,
                lbpa.account_subtype, lbpa.account_mask, lbpa.institution_id,
                lbpa.group_end_date
            FROM LatestBalancePerAccount lbpa
            LEFT JOIN GroupedData gd
            ON lbpa.user_id = gd.user_id AND
                lbpa.account_id = gd.account_id AND
                lbpa.group_number = gd.group_number AND
                lbpa.latest_timestamp_in_group = gd.timestamp
        ),
        FinalResult AS (
            -- Ensure we have entries for all account-group combinations
            SELECT ag.account_id, :userId as user_id, ag.group_number,
                COALESCE(lb.balance, 0) AS aggregated_balance,
                ag.account_name, ag.account_category, ag.account_type,
                ag.account_subtype, ag.account_mask, ag.institution_id,
                ag.group_end_date
            FROM AccountGroups ag
            LEFT JOIN LatestBalances lb
            ON ag.account_id = lb.account_id AND ag.group_number = lb.group_number
        )
        SELECT user_id, account_id, group_end_date, aggregated_balance as final_balance,
            account_name, account_category, account_type,
            account_subtype, account_mask, institution_id
        FROM FinalResult
        ORDER BY account_id, group_end_date ASC;
    """, nativeQuery = true)
    List<Object[]> getAggregatedBalancesGroupedByAccountId(Integer userId, Integer x, Integer y);

    @Query(value = """
        WITH CurrentLastDayBalances AS (
            SELECT 
                a.id AS account_id, 
                a.account_name, a.account_category, a.account_type, 
                a.account_subtype, a.account_mask, a.institution_id,
                ab.balance, ab.timestamp,
                ROW_NUMBER() OVER (PARTITION BY a.id ORDER BY ab.timestamp DESC) as rn
            FROM account_balances ab
            JOIN accounts a ON ab.account_id = a.id
            WHERE ab.user_id = :userId AND
                YEAR(ab.timestamp) = :year AND
                MONTH(ab.timestamp) = :month
        ),
        CurrentMonthTotals AS (
            SELECT 
                account_id, 
                account_name, account_category, account_type, 
                account_subtype, account_mask, institution_id,
                balance as current_balance
            FROM CurrentLastDayBalances
            WHERE rn = 1
        ),
        PreviousLastDayBalances AS (
            SELECT 
                a.id AS account_id, 
                a.account_name, a.account_category, a.account_type, 
                a.account_subtype, a.account_mask, a.institution_id,
                ab.balance, ab.timestamp,
                ROW_NUMBER() OVER (PARTITION BY a.id ORDER BY ab.timestamp DESC) as rn
            FROM account_balances ab
            JOIN accounts a ON ab.account_id = a.id
            WHERE ab.user_id = :userId
            AND ((MONTH(ab.timestamp) = :month - 1 AND YEAR(ab.timestamp) = :year) OR
                (MONTH(ab.timestamp) = 12 AND YEAR(ab.timestamp) = :year - 1 AND :month = 1))
        ),
        PreviousMonthTotals AS (
            SELECT 
                account_id,
                balance as previous_balance
            FROM PreviousLastDayBalances
            WHERE rn = 1
        )
        SELECT 
            c.account_id,
            c.account_name, 
            c.account_category, 
            c.account_type, 
            c.account_subtype, 
            c.account_mask, 
            c.institution_id,
            c.current_balance, 
            p.previous_balance,
            (c.current_balance - p.previous_balance) as delta_amount,
            CASE 
                WHEN p.previous_balance = 0 THEN 100
                ELSE ((c.current_balance - p.previous_balance) / p.previous_balance) * 100 
            END as delta_percentage
        FROM CurrentMonthTotals c
        LEFT JOIN PreviousMonthTotals p ON c.account_id = p.account_id;
    """, nativeQuery = true)
    List<Object[]> getAccountBalanceMontltasGroupedByAccountId(Integer userId, Integer year, Integer month);
    // Add this new query method to your AccountBalanceRepository interface
@Query(value = """
    WITH AllUserAccounts AS (
        SELECT DISTINCT 
            a.id AS account_id,
            a.account_name, 
            a.account_category, 
            a.account_type, 
            a.account_subtype, 
            a.account_mask, 
            a.institution_id
        FROM accounts a
        WHERE a.user_id = :userId
    ),
    CurrentPeriodBalances AS (
        SELECT 
            a.id AS account_id,
            AVG(ab.balance) as avg_balance
        FROM account_balances ab
        JOIN accounts a ON ab.account_id = a.id
        WHERE ab.user_id = :userId 
          AND DATE(ab.timestamp) BETWEEN :currentPeriodStart AND :currentPeriodEnd
        GROUP BY a.id
    ),
    PastPeriodBalances AS (
        SELECT 
            a.id AS account_id,
            AVG(ab.balance) as avg_balance
        FROM account_balances ab
        JOIN accounts a ON ab.account_id = a.id
        WHERE ab.user_id = :userId
          AND DATE(ab.timestamp) BETWEEN :pastPeriodStart AND :pastPeriodEnd
        GROUP BY a.id
    ),
    ActiveAccountsLast60Days AS (
        SELECT DISTINCT ab.account_id
        FROM account_balances ab
        WHERE ab.user_id = :userId
          AND DATE(ab.timestamp) BETWEEN :pastPeriodStart AND :currentPeriodEnd
    )
    SELECT 
        ua.account_id,
        ua.account_name, 
        ua.account_category, 
        ua.account_type, 
        ua.account_subtype, 
        ua.account_mask, 
        ua.institution_id,
        COALESCE(c.avg_balance, 0) as current_period_balance, 
        COALESCE(p.avg_balance, 0) as past_period_balance,
        (COALESCE(c.avg_balance, 0) - COALESCE(p.avg_balance, 0)) as delta_amount,
        CASE 
            WHEN COALESCE(p.avg_balance, 0) = 0 THEN 
                CASE WHEN COALESCE(c.avg_balance, 0) > 0 THEN 100.0 ELSE 0.0 END
            ELSE ((COALESCE(c.avg_balance, 0) - p.avg_balance) / ABS(p.avg_balance)) * 100.0
        END as delta_percentage
    FROM AllUserAccounts ua
    INNER JOIN ActiveAccountsLast60Days active ON ua.account_id = active.account_id
    LEFT JOIN CurrentPeriodBalances c ON ua.account_id = c.account_id
    LEFT JOIN PastPeriodBalances p ON ua.account_id = p.account_id
    ORDER BY ua.account_id;
""", nativeQuery = true)
List<Object[]> getAccountBalancePeriodDeltaWithDateRange(
    @Param("userId") Integer userId, 
    @Param("currentPeriodStart") LocalDate currentPeriodStart,
    @Param("currentPeriodEnd") LocalDate currentPeriodEnd,
    @Param("pastPeriodStart") LocalDate pastPeriodStart,
    @Param("pastPeriodEnd") LocalDate pastPeriodEnd
);


@Query(value = """
    WITH CurrentQuarterBalances AS (
        SELECT 
            a.id AS account_id, 
            a.account_name, a.account_category, a.account_type, 
            a.account_subtype, a.account_mask, a.institution_id,
            AVG(ab.balance) as avg_balance,
            SUM(ab.balance) as total_balance,
            COUNT(ab.balance) as balance_count
        FROM account_balances ab
        JOIN accounts a ON ab.account_id = a.id
        WHERE ab.user_id = :userId 
        AND DATE(ab.timestamp) >= :currentQuarterStart
        AND DATE(ab.timestamp) <= :currentQuarterEnd
        GROUP BY a.id, a.account_name, a.account_category, a.account_type, 
                 a.account_subtype, a.account_mask, a.institution_id
    ),
    PastQuarterBalances AS (
        SELECT 
            a.id AS account_id, 
            AVG(ab.balance) as avg_balance,
            SUM(ab.balance) as total_balance,
            COUNT(ab.balance) as balance_count
        FROM account_balances ab
        JOIN accounts a ON ab.account_id = a.id
        WHERE ab.user_id = :userId
        AND DATE(ab.timestamp) >= :pastQuarterStart
        AND DATE(ab.timestamp) <= :pastQuarterEnd
        GROUP BY a.id
    )
    SELECT 
        c.account_id,
        c.account_name, 
        c.account_category, 
        c.account_type, 
        c.account_subtype, 
        c.account_mask, 
        c.institution_id,
        c.avg_balance as current_quarter_balance, 
        COALESCE(p.avg_balance, 0) as past_quarter_balance,
        (c.avg_balance - COALESCE(p.avg_balance, 0)) as delta_amount,
        CASE 
            WHEN COALESCE(p.avg_balance, 0) = 0 THEN 
                CASE WHEN c.avg_balance > 0 THEN 100.0 ELSE 0.0 END
            ELSE ((c.avg_balance - p.avg_balance) / ABS(p.avg_balance)) * 100.0
        END as delta_percentage
    FROM CurrentQuarterBalances c
    LEFT JOIN PastQuarterBalances p ON c.account_id = p.account_id
    ORDER BY c.account_id;
""", nativeQuery = true)
List<Object[]> getAccountBalanceQuarterlyDeltaWithDateRange(
    @Param("userId") Integer userId, 
    @Param("currentQuarterStart") LocalDate currentQuarterStart,
    @Param("currentQuarterEnd") LocalDate currentQuarterEnd,
    @Param("pastQuarterStart") LocalDate pastQuarterStart,
    @Param("pastQuarterEnd") LocalDate pastQuarterEnd
);
// FIXED REPOSITORY QUERIES WITH PROPER DATE LOGIC
   @Query(value = """
        WITH CurrentBalances AS (
            SELECT 
                a.id AS account_id, 
                a.account_name, a.account_category, a.account_type, 
                a.account_subtype, a.account_mask, a.institution_id,
                ab.balance, ab.timestamp,
                ROW_NUMBER() OVER (PARTITION BY a.id ORDER BY ab.timestamp DESC) as rn
            FROM account_balances ab
            JOIN accounts a ON ab.account_id = a.id
            WHERE ab.user_id = :userId 
            AND ab.timestamp >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        ),
        CurrentTotals AS (
            SELECT 
                account_id, 
                account_name, account_category, account_type, 
                account_subtype, account_mask, institution_id,
                balance as current_balance
            FROM CurrentBalances
            WHERE rn = 1
        ),
        PastBalances AS (
            SELECT 
                a.id AS account_id, 
                ab.balance, ab.timestamp,
                ROW_NUMBER() OVER (PARTITION BY a.id ORDER BY ab.timestamp DESC) as rn
            FROM account_balances ab
            JOIN accounts a ON ab.account_id = a.id
            WHERE ab.user_id = :userId
            AND ab.timestamp <= DATE_SUB(CURDATE(), INTERVAL :months MONTH)
            AND ab.timestamp >= DATE_SUB(DATE_SUB(CURDATE(), INTERVAL :months MONTH), INTERVAL 7 DAY)
        ),
        PastTotals AS (
            SELECT 
                account_id,
                balance as past_balance
            FROM PastBalances
            WHERE rn = 1
        )
        SELECT 
            c.account_id,
            c.account_name, 
            c.account_category, 
            c.account_type, 
            c.account_subtype, 
            c.account_mask, 
            c.institution_id,
            c.current_balance, 
            COALESCE(p.past_balance, 0) as past_balance,
            (c.current_balance - COALESCE(p.past_balance, 0)) as delta_amount,
            CASE 
                WHEN COALESCE(p.past_balance, 0) = 0 THEN 
                    CASE WHEN c.current_balance > 0 THEN 100 ELSE 0 END
                ELSE ((c.current_balance - p.past_balance) / p.past_balance) * 100 
            END as delta_percentage
        FROM CurrentTotals c
        LEFT JOIN PastTotals p ON c.account_id = p.account_id
        ORDER BY c.account_id;
    """, nativeQuery = true)
    List<Object[]> getAccountBalanceGroupedByTimeperiod(Integer userId, Integer months);

}

