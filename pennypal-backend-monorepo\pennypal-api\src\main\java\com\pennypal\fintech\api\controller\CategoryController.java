package com.pennypal.fintech.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.pennypal.fintech.dto.CategoryDto;
import com.pennypal.fintech.dto.CategoryMappingDto;
import com.pennypal.fintech.entity.Category;
import com.pennypal.fintech.entity.SubCategory;
import com.pennypal.fintech.service.CategoryService;

import java.util.List;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/api/v1/category")
@Tag(name = "Category Management", description = "APIs for managing transaction categories and subcategories")
public class CategoryController {

    @Autowired
    private final CategoryService categoryService;

 
    public CategoryController(CategoryService categoryService) {
        this.categoryService = categoryService;
    }
   
    /**
     * Get category by ID
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get category by ID",
               description = "Retrieves a specific category by its unique identifier")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved the category",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = CategoryDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Category not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<CategoryDto> getCategoryById(
            @Parameter(description = "Unique identifier of the category", required = true)
            @PathVariable Integer id) {
        try {
            Category category = categoryService.getCategoryById(id);
            CategoryDto dto = convertToDto(category);
            return ResponseEntity.ok(dto);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    /**
     * Get all categories
     */
    @GetMapping("/all")
    @Operation(summary = "Get all categories",
               description = "Retrieves all available budget/transaction categories")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved all categories",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of category DTOs")
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<List<CategoryDto>> getAllCategories() {
        List<Category> categories = categoryService.getAllCategories();
        List<CategoryDto> dtos = categories.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
        return ResponseEntity.ok(dtos);
    }

    @Operation(summary = "Update category icon",
               description = "Updates the icon for a specific category")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully updated category icon"),
        @ApiResponse(responseCode = "404", description = "Category not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PutMapping("/{id}/icon")
    public ResponseEntity<Void> updateCategoryIcon(
        @Parameter(description = "Unique identifier of the category", required = true)
        @PathVariable Integer id,
        @Parameter(description = "Icon key to update", required = true)
        @RequestParam String iconKey) {
        try {
            categoryService.updateCategoryIconKey(id, iconKey);
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    // Helper method to map Category to CategoryDto
    private CategoryDto convertToDto(Category category) {
        CategoryDto dto = new CategoryDto();
        dto.setId(category.getId());
        dto.setCategory(category.getCategory());
        dto.setCategoryIconKey(category.getCategoryIconKey());
        // Optionally map subcategories if needed:
        // dto.setSubCategories( ... );
        return dto;
    }

    @Operation(summary = "Get all subcategories by category",
               description = "Retrieves all subcategories for a specific category")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved subcategories"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/byCategory/{category}")
    public ResponseEntity<List<SubCategory>> getAllCategories(
        @Parameter(description = "Name of the category", required = true)
        @PathVariable String category) {
        System.out.println("category is "+category);
        List<SubCategory> categories = categoryService.getAllCategoriesByCategory(category);
        return ResponseEntity.ok(categories);
    }

    @Operation(summary = "Sync Plaid PFC categories",
               description = "Syncs Plaid PFC categories with the application's database")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully synced categories"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/sync/plaid-pfc")
    public String syncPlaidPFC() {
        try {
            categoryService.processAndStorePlaidPFC();
            return "Categories synced successfully!";
        } catch (Exception e) {
            return "Error syncing categories: " + e.getMessage();
        }
    }

    @Operation(summary = "Get all Plaid PFC mappings",
               description = "Retrieves all mappings between Plaid PFC categories and the application's categories")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved mappings"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/get/plaid-pfc")
    public List<CategoryMappingDto> getPlaidPFC() {
        return categoryService.getAllMappings();
    }
}