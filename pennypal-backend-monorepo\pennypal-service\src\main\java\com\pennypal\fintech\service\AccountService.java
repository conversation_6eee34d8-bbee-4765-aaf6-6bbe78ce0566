package com.pennypal.fintech.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.JsonNode;

import com.pennypal.fintech.dto.AccountBalanceDto;
import com.pennypal.fintech.entity.AccountBalance;
import com.pennypal.fintech.entity.Accounts;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.repository.AccountBalanceRepository;
import com.pennypal.fintech.repository.AccountRepository;
import com.pennypal.fintech.repository.UserRepository;

import com.plaid.client.model.AccountBase;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class AccountService {

    @Autowired
    private AccountRepository accountRepository;
    
    @Autowired
    private AccountBalanceRepository accountBalanceRepository;    
   
    @Autowired
    private TokenService tokenService;

    @Autowired
    private UserRepository userRepository;

    public AccountService(AccountRepository accountRepository) {
        this.accountRepository = accountRepository;
    }

    @Cacheable(value = "accountBalanceCache",
               key = "#root.methodName + '_' + #userId",
               unless = "#result == null")
    @Transactional(readOnly = true)
    public List<AccountBalanceDto> getAccountBalanceSummary(Integer userId) {
        return accountBalanceRepository.findAccountBalanceSummaryByUser(userId);
    }
   
    public List<Accounts> getAllAccounts() {
        return accountRepository.findAll();
    }

    public List<AccountBase> fetchAccountBalance(String accessToken){
        return tokenService.getAccountBalance(accessToken);
    }

    public void updateAccount(){
     
       // accountRepository.save(null);
    }

    @Transactional
    public List<Accounts> saveFinicityAccounts(JsonNode accountsResponse, Integer userId) {
        log.info("Inside saveFinicityAccounts in AccountService for user ID: {}", userId);
        List<Accounts> savedAccounts = new ArrayList<>();

        Users user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));

        if (accountsResponse == null) {
            log.warn("Received null accountsResponse");
            return savedAccounts;
        }

        if (!accountsResponse.has("account")) {
            log.warn("No 'account' field in response: {}", accountsResponse);
            return savedAccounts;
        }
        
        if (accountsResponse != null && accountsResponse.has("account")) {
            JsonNode accountsArray = accountsResponse.get("account");
            log.debug("Processing {} accounts", accountsArray.size());

            if (!accountsArray.isArray()) {
                log.warn("'account' field is not an array: {}", accountsArray);
                return savedAccounts;
            }
            
            if (accountsArray.isArray()) {
                for (JsonNode accountNode : accountsArray) {
                    try {
                        log.info("Account node: " + accountNode);

                        if (!accountNode.has("id")) {
                            log.warn("Account missing ID field, skipping: {}", accountNode);
                            continue;
                        }
                        Accounts account = mapFinicityAccountToEntity(accountNode, userId);
                        log.info("Mapped account: " + account);
                        Accounts savedAccount = saveOrUpdateAccount(account);
                        log.info("Saved account: " + savedAccount);
                        savedAccounts.add(savedAccount);

                        // Add new entry to account_balances
                        try {
                            AccountBalance accountBalance = new AccountBalance();
                            accountBalance.setAccount(savedAccount);
                            accountBalance.setUser(savedAccount.getUser());
                            accountBalance.setBalance(savedAccount.getBalance());
                            accountBalance.setCurrencyCode(savedAccount.getCurrencyType());
                            accountBalance.setTimestamp(LocalDateTime.now());
                            accountBalanceRepository.save(accountBalance);
                            log.debug("Saved balance history for account ID: {}", savedAccount.getId());
                        } catch (Exception e) {
                            log.error("Error saving balance history: {}", e.getMessage(), e);
                        }
                    } catch (Exception e) {
                        log.error("Error processing account: {}", e.getMessage(), e);
                    }
                }
            }
        }
        
        return savedAccounts;
    }
    
    private Accounts mapFinicityAccountToEntity(JsonNode accountNode, Integer userId) {
        String finicityAccountId = accountNode.get("id").asText();
        log.info("Finicity account ID: " + finicityAccountId);
        
        // Check if account already exists
        Optional<Accounts> existingAccount = accountRepository.findByPlaidUniqueNo(finicityAccountId);
        log.info("Existing account: " + existingAccount);
        Accounts account = existingAccount.orElse(new Accounts());
        log.info("Account after orElse: " + account);

        if (!existingAccount.isPresent()) {
            Optional<Users> userOptional = userRepository.findById(userId);
            if (userOptional.isPresent()) {
                account.setUser(userOptional.get());
            } else {
                throw new RuntimeException("User not found with ID: " + userId);
            }
            account.setPlaidUniqueNo(finicityAccountId);
            account.setAuthPartner("finicity");
        }
        
        // Map Finicity fields to account entity
        if (accountNode.has("balance")) {
            account.setBalance(accountNode.get("balance").asDouble());
        }

        if (accountNode.has("accountNumberDisplay")) {
            account.setAccountMask(accountNode.get("accountNumberDisplay").asText());
        }
        
        if (accountNode.has("name")) {
            account.setAccountName(accountNode.get("name").asText());
        }
        
        if (accountNode.has("type")) {
            account.setAccountCategory(accountNode.get("type").asText());
            // Map account type to category
            account.setAccountType(mapAccountTypeToCategory(accountNode.get("type").asText()));
        }
        
        if (accountNode.has("currency")) {
            account.setCurrencyType(accountNode.get("currency").asText());
        }
        
        if (accountNode.has("institutionId")) {
            account.setInstitutionId(accountNode.get("institutionId").asText());
        }
        
        if (accountNode.has("institutionLoginId")) {
            account.setItemId(accountNode.get("institutionLoginId").asText());
        }
        
        account.setAuthenticationRequired(true);

        account.setLastSyncTime(LocalDateTime.now());
        account.setUpdateDatetime(LocalDateTime.now());

        if (accountNode.has("oldestTransactionDate")) {
            account.setOldestTxnDate(accountNode.get("oldestTransactionDate").asLong());
        } else {
            account.setOldestTxnDate(
                Instant.now().minus(1800, ChronoUnit.DAYS).getEpochSecond());
        }
        
        return account;
    }
    
    private String mapAccountTypeToCategory(String accountType) {
        log.info("Mapping account type to category: " + accountType);
        switch (accountType.toLowerCase()) {
            case "checking":
            case "savings":
            case "moneymarket":
                return "depository";
            case "creditcard":
                return "credit";
            case "investment":
            case "investmenttaxdeferred":
            case "roth":
            case "ira":
                return "investment";
            case "loan":
            case "mortgage":
                return "loan";
            default:
                return "other";
        }
    }
    
    private Accounts saveOrUpdateAccount(Accounts account) {
        // If it's an existing account, preserve the insert datetime
        log.info("Saving or updating account: " + account);
        log.info("Account ID: " + account.getId());
        if (account.getId() > 0) {
            Accounts existingAccount = accountRepository.findById(account.getId()).orElse(null);
            if (existingAccount != null) {
                account.setInsertDatetime(existingAccount.getInsertDatetime());
            }
        } else {
            account.setInsertDatetime(LocalDateTime.now());
        }
        
        return accountRepository.save(account);
    }
    
}
