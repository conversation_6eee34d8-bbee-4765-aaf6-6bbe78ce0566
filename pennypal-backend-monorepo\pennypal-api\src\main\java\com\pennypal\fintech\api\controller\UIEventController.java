package com.pennypal.fintech.api.controller; 

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pennypal.fintech.dto.UIEventDto;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/v1/log")
@Slf4j
@Tag(name = "UI Event Logging", description = "APIs for logging user interface events and analytics")
public class UIEventController {

    @Autowired
    private ObjectMapper objectMapper;

    @PostMapping("/UIEvent")
    @Operation(summary = "Log single UI event",
               description = "Logs a single user interface event for analytics and monitoring")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Event logged successfully",
            content = @Content(mediaType = "text/plain")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid event data",
            content = @Content(mediaType = "text/plain")
        )
    })
    public String logEvent(
            @Parameter(description = "UI event data to log", required = true)
            @RequestBody UIEventDto eventDTO) {
        log.info("Received UI event: " + eventDTO);
        try {
            String metadataJson = objectMapper.writeValueAsString(eventDTO.getMetadata());
            log.info("userId: " + eventDTO.getUserId() +
                     ", userSub: " + eventDTO.getUserSub() +
                     ", componentName: " + eventDTO.getComponentName() +
                     ", eventType: " + eventDTO.getEventType() +
                     ", metadata: " + metadataJson);
            return "Event logged successfully";
        } catch (Exception e) {
            return "Error: " + e.getMessage();
        }
    }

    @PostMapping("/UIEvents")
    @Operation(summary = "Log multiple UI events",
               description = "Logs multiple user interface events in batch for analytics and monitoring")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Events logged successfully",
            content = @Content(mediaType = "text/plain")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid event data",
            content = @Content(mediaType = "text/plain")
        )
    })
    public String logEvents(
            @Parameter(description = "List of UI event data to log", required = true)
            @RequestBody List<UIEventDto> eventDTOs) {
        log.info("Received UI events: " + eventDTOs);
        try {
            for (UIEventDto eventDTO : eventDTOs) {
                log.info("Received UI event: " + eventDTO);
                String metadataJson = objectMapper.writeValueAsString(eventDTO.getMetadata());
                log.info("userId: " + eventDTO.getUserId() +
                        ", userSub: " + eventDTO.getUserSub() +
                        ", componentName: " + eventDTO.getComponentName() +
                        ", eventType: " + eventDTO.getEventType() +
                        ", metadata: " + metadataJson);
            }
            return "Event logged successfully";
        } catch (Exception e) {
            return "Error: " + e.getMessage();
        }
    }
}