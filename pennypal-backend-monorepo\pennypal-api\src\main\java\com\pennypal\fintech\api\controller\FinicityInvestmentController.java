package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.service.FinicityInvestmentService;

import java.time.LocalDateTime;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/api/v1/finicity/investment")
@Slf4j
@Tag(name = "Finicity Investment", description = "APIs for Finicity investment data and portfolio management")
public class FinicityInvestmentController {

    @Autowired
    private FinicityInvestmentService finicityInvestmentService;
    
    @GetMapping("/debug/all-accounts/{userId}")
    @Operation(summary = "Debug - Get all accounts",
               description = "Debug endpoint to retrieve all accounts for a user from Finicity")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved all accounts",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<?> debugGetAllAccounts(
            @Parameter(description = "ID of the user to get accounts for", required = true)
            @PathVariable Integer userId) {
        return finicityInvestmentService.debugGetAllAccounts(userId);
    }

    /**
     * Get investment accounts for a user
     */
    @GetMapping("/accounts/{userId}")
    @Operation(summary = "Get investment accounts",
               description = "Retrieves all investment accounts for a specific user from Finicity")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved investment accounts",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Failed to get investment accounts",
            content = @Content(mediaType = "application/json")
        )
    })
    public ResponseEntity<?> getInvestmentAccounts(
            @Parameter(description = "ID of the user to get investment accounts for", required = true)
            @PathVariable Integer userId) {
        try {
            return finicityInvestmentService.getInvestmentAccounts(userId);
        } catch (Exception e) {
            log.error("Controller error getting investment accounts for user {}: {}", userId, e.getMessage());
            return ResponseEntity.status(500).body(Map.of(
                "error", "Failed to get investment accounts",
                "message", e.getMessage(),
                "userId", userId
            ));
        }
    }

    /**
     * Get holdings for a specific investment account
     */
    @Operation(summary = "Get holdings for a specific investment account",
               description = "Retrieves all holdings for a specific investment account from Finicity")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved account holdings",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Failed to get account holdings",
            content = @Content(mediaType = "application/json")
        )
    })
    @GetMapping("/accounts/{userId}/holdings/{accountId}")
    public ResponseEntity<?> getAccountHoldings(
            @Parameter(description = "ID of the user to get holdings for", required = true)
            @PathVariable Integer userId,
            @Parameter(description = "ID of the account to get holdings for", required = true)
            @PathVariable String accountId) {
        return finicityInvestmentService.getAccountHoldings(userId, accountId);
    }

    /**
     * Sync all investment holdings for a user
     */
    @Operation(summary = "Sync all investment holdings for a user",
               description = "Syncs all investment holdings for a specific user from Finicity")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully synced investment holdings",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Failed to sync investment holdings",
            content = @Content(mediaType = "application/json")
        )
    })
    @PostMapping("/sync-holdings/{userId}")
    public ResponseEntity<?> syncInvestmentHoldings(
        @Parameter(description = "ID of the user to sync holdings for", required = true)
        @PathVariable Integer userId) {
        return finicityInvestmentService.syncInvestmentHoldings(userId);
    }

    /**
     * Get investment transactions for an account
     */
    @Operation(summary = "Get investment transactions for an account",
               description = "Retrieves all investment transactions for a specific account from Finicity")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved investment transactions",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Failed to get investment transactions",
            content = @Content(mediaType = "application/json")
        )
    })
    @GetMapping("/accounts/{userId}/transactions/{accountId}")
    public ResponseEntity<?> getInvestmentTransactions(
            @Parameter(description = "ID of the user to get transactions for", required = true)
            @PathVariable Integer userId,
            @Parameter(description = "ID of the account to get transactions for", required = true)
            @PathVariable String accountId) {
        return finicityInvestmentService.getInvestmentTransactions(userId, accountId);
    }

    /**
     * Get user's investment portfolio summary
     */
    @Operation(summary = "Get user's investment portfolio summary",
               description = "Retrieves the investment portfolio summary for a specific user from Finicity")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved investment portfolio summary",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Failed to get investment portfolio summary",
            content = @Content(mediaType = "application/json")
        )
    })
    @GetMapping("/portfolio/{userId}")
    public ResponseEntity<?> getInvestmentPortfolio(
        @Parameter(description = "ID of the user to get portfolio summary for", required = true)
        @PathVariable Integer userId) {
        return finicityInvestmentService.getInvestmentPortfolio(userId);
    }

    /**
     * Get all investment holdings for a user
     * This endpoint fetches all investment accounts and their holdings
     */
    @Operation(summary = "Get all investment holdings for a user",
               description = "Retrieves all investment holdings for a specific user from Finicity")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved all investment holdings",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Failed to get all investment holdings",
            content = @Content(mediaType = "application/json")
        )
    })
    @GetMapping("/holdings/{userId}")
    public ResponseEntity<?> getAllUserHoldings(
        @Parameter(description = "ID of the user to get all holdings for", required = true)
        @PathVariable Integer userId) {
        log.info("Controller: Getting all investment holdings for userId: {}", userId);
        
        // Validate userId
        if (userId == null || userId <= 0) {
            log.error("Invalid userId provided: {}", userId);
            return ResponseEntity.badRequest().body(Map.of(
                "error", "Invalid user ID",
                "message", "User ID must be a positive integer",
                "userId", userId
            ));
        }
        
        try {
            long startTime = System.currentTimeMillis();
            ResponseEntity<?> response = finicityInvestmentService.getAllUserHoldings(userId);
            long endTime = System.currentTimeMillis();
            
            log.info("Controller: Successfully processed holdings request for user {} in {}ms", 
                userId, (endTime - startTime));
            
            return response;
            
        } catch (IllegalArgumentException e) {
            log.error("Controller: Invalid argument for user {}: {}", userId, e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "error", "Invalid request parameters",
                "message", e.getMessage(),
                "userId", userId
            ));
            
        } catch (SecurityException e) {
            log.error("Controller: Security error for user {}: {}", userId, e.getMessage());
            return ResponseEntity.status(403).body(Map.of(
                "error", "Access denied",
                "message", "You are not authorized to access this resource",
                "userId", userId
            ));
            
        } catch (Exception e) {
            log.error("Controller: Unexpected error getting all holdings for user {}: {}", 
                userId, e.getMessage(), e);
            return ResponseEntity.status(500).body(Map.of(
                "error", "Internal server error",
                "message", "An unexpected error occurred while retrieving investment holdings",
                "userId", userId,
                "timestamp", LocalDateTime.now(),
                "requestId", java.util.UUID.randomUUID().toString()
            ));
        }
    }

    // Add to your FinicityInvestmentController
    @GetMapping("/debug/holdings/{userId}/{accountId}")
    public ResponseEntity<?> debugGetAccountHoldings(
            @PathVariable Integer userId,
            @PathVariable String accountId) {
        return finicityInvestmentService.debugGetAccountHoldings(userId, accountId);
    }
}