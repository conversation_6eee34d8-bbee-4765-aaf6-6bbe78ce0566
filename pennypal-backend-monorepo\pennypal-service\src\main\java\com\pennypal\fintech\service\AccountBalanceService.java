package com.pennypal.fintech.service;

import java.beans.Transient;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.time.temporal.ChronoUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;
import java.util.Arrays;
import java.util.Set;
import com.pennypal.fintech.dto.AccountBalanceDeltaDto;
import com.pennypal.fintech.dto.AccountBalanceSummary;
import com.pennypal.fintech.dto.AccountBalanceSummaryDto;
import com.pennypal.fintech.repository.AccountBalanceDao;
import com.pennypal.fintech.repository.AccountRepository;
import com.pennypal.fintech.repository.AccountBalanceRepository;
import com.pennypal.fintech.repository.BudgetRepository;
import com.pennypal.fintech.repository.UserRepository;
import java.util.Objects;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
public class AccountBalanceService {
 private static final Logger log = LoggerFactory.getLogger(AccountBalanceService.class);
    @Autowired
    private AccountBalanceRepository accountBalanceRepository;

    @Autowired
    private AccountBalanceDao accountBalanceDao;

    @Autowired
    private BudgetRepository budgetRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private CacheManager cacheManager;

    /* 
    public List<AccountBalanceSummaryDto> getAccountBalanceSummary() {
        return accountBalanceRepository.getAccountBalanceSummary();
    }*/
    
     /**
     * Gets account balances organized by account type 
     * 
     * @param userId The user ID
     * @return Map of account type to list of monthly balances
     */
    @Cacheable(value = "accountBalanceCache",
               key = "#root.methodName + '_' + #userId",
               unless = "#result == null")
    public Map<String, List<AccountBalanceSummary>> getBalancesGroupedByAccountType(Long userId) {
        List<AccountBalanceSummary> balances = accountBalanceDao.getMonthlyAccountBalancesByType(userId);
        
        // Group the results by account type
        return balances.stream()
                .collect(Collectors.groupingBy(AccountBalanceSummary::getAccountType));
    }

    @Cacheable(value = "accountBalanceCache", 
               key = "#root.methodName + '_' + #userId + '_' + #x + '_' + #y",
               unless = "#result == null")
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getAggregatedBalances(Integer userId, Integer x, Integer y) {
        if (!userRepository.existsById(userId)) {
            throw new RuntimeException("User not found");
        }

        if (y > x * 30) {
            throw new IllegalArgumentException("Interval cannot be larger than the total duration period");
        }

        List<Object[]> results = accountBalanceRepository.getAggregatedBalances(userId, x, y);

        List<Map<String, Object>> formattedResults = new ArrayList<>();
        for (Object[] row : results) {
            Map<String, Object> map = new HashMap<>();
            map.put("user_id", row[0]);
            map.put("account_type", row[1]);
            map.put("group_end_date", row[2]);
            map.put("aggregated_balance", row[3]);
            formattedResults.add(map);
        }
        return formattedResults;
    }

    @Cacheable(value = "accountBalanceCache", 
               key = "#root.methodName + '_' + #userId + '_' + #x + '_' + #y + '_' + #z",
               unless = "#result == null")
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getAggregatedBalancesByAccountType(Integer userId, Integer x, Integer y, String z) {
        if (!userRepository.existsById(userId)) {
            throw new RuntimeException("User not found");
        }

        if (y > x * 30) {
            throw new IllegalArgumentException("Interval cannot be larger than the total duration period");
        }

        if (!"credit".equals(z) && !"depository".equals(z) && !"loan".equals(z) && !"investment".equals(z)) {
            throw new IllegalArgumentException("Invalid account type. Must be one of: credit, depository, loan, and investment");
        }

        List<Object[]> results = accountBalanceRepository.getAggregatedBalancesByAccountType(userId, x, y, z);

        List<Map<String, Object>> formattedResults = new ArrayList<>();
        for (Object[] row : results) {
            Map<String, Object> map = new HashMap<>();
            map.put("user_id", row[0]);
            map.put("account_type", row[1]);
            map.put("group_end_date", row[2]);
            map.put("aggregated_balance", row[3]);
            formattedResults.add(map);
        }
        return formattedResults;
    }

    @Cacheable(value = "accountBalanceCache", 
               key = "#root.methodName + '_' + #userId + '_' + #year + '_' + #month",
               unless = "#result == null")
    @Transactional(readOnly = true)
    public List<AccountBalanceDeltaDto> getAccountBalanceMonthlyDeltas(Integer userId, Integer year, Integer month) {
        if (!userRepository.existsById(userId)) {
            throw new RuntimeException("User not found");
        }

        LocalDate now = LocalDate.now();
        LocalDate requestedDate = LocalDate.of(year, month, 1);

        if (requestedDate.isAfter(now)) {
            throw new IllegalArgumentException("Cannot calculate deltas for future dates");
        }

        List<Object[]> results1 = accountBalanceRepository.getAccountBalanceMonthlyDeltas(userId, year, month);
        List<Object[]> results2 = budgetRepository.getBudgetExpenseDeltas(userId, year, month);
        List<Object[]> results3 = budgetRepository.getBudgetIncomeDeltas(userId, year, month);
        
        List<Object[]> results = new ArrayList<>();
        results.addAll(results1);
        results.addAll(results2);
        results.addAll(results3);
        
        return results.stream().map(row -> {
            AccountBalanceDeltaDto dto = new AccountBalanceDeltaDto();
            dto.setAccountType((String) row[0]);
            dto.setCurrentBalance(row[1] != null ? ((Number) row[1]).doubleValue() : 0.0);
            dto.setPreviousBalance(row[2] != null ? ((Number) row[2]).doubleValue() : 0.0);
            dto.setDeltaAmount(row[3] != null ? ((Number) row[3]).doubleValue() : 0.0);
            dto.setDeltaPercentage(row[4] != null ? ((Number) row[4]).doubleValue() : 0.0);
            return dto;
            }).collect(Collectors.toList());
    }

    @Cacheable(value = "accountBalanceCache", 
               key = "#root.methodName + '_' + #userId + '_' + #duration + '_' + #interval",
               unless = "#result == null || #result.isEmpty()")
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getAggregatedBalancesGroupedByAccountId(
            Integer userId, Integer duration, Integer interval) {
        
        if (!userRepository.existsById(userId)) {
            throw new RuntimeException("User not found");
        }

        if (interval > duration * 30) {
            throw new IllegalArgumentException("Interval cannot be larger than the total duration period");
        }

        List<Object[]> results = accountBalanceRepository
            .getAggregatedBalancesGroupedByAccountId(userId, duration, interval);
            
        return results.stream().map(row -> {
            Map<String, Object> map = new HashMap<>();
            map.put("userId", row[0]);
            map.put("accountId", row[1]);
            map.put("groupEndDate", row[2]);
            map.put("aggregatedBalance", row[3]);
            map.put("accountName", row[4]);
            map.put("accountCategory", row[5]);
            map.put("accountType", row[6]); // <-- fixed parenthesis
            map.put("accountSubtype", row[7]);
            map.put("accountMask", row[8]);
            map.put("institutionId", row[9]);
            return map;
        }).collect(Collectors.toList());
    }

    @Cacheable(value = "accountBalanceCache", 
               key = "#root.methodName + '_' + #userId + '_' + #year + '_' + #month",
               unless = "#result == null || #result.isEmpty()")
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getAccountBalanceMonthlyDeltasGroupedByAccountId(
            Integer userId, Integer year, Integer month) {
        
        if (!userRepository.existsById(userId)) {
            throw new RuntimeException("User not found");
        }

        LocalDate now = LocalDate.now();
        LocalDate requestedDate = LocalDate.of(year, month, 1);

        if (requestedDate.isAfter(now)) {
            throw new IllegalArgumentException("Cannot calculate deltas for future dates");
        }

        List<Object[]> results = accountBalanceRepository
            .getAccountBalanceMontltasGroupedByAccountId(userId, year, month);
            
        return results.stream().map(row -> {
            Map<String, Object> map = new HashMap<>();
            map.put("accountId", row[0]);
            map.put("accountName", row[1]);
            map.put("accountCategory", row[2]);
            map.put("accountType", row[3]);
            map.put("accountSubtype", row[4]);
            map.put("accountMask", row[5]);
            map.put("institutionId", row[6]);
            map.put("currentBalance", row[7]);
            map.put("previousBalance", row[8]);
            map.put("deltaAmount", row[9]);
            map.put("deltaPercentage", row[10]);
            return map;
        }).collect(Collectors.toList());
    }

    @Cacheable(value = "accountBalanceCache", 
               key = "#root.methodName + '_' + #userId",
               unless = "#result == null || #result.isEmpty()")
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getUniqueAccountIds(int userId) {
        if (!userRepository.existsById(userId)) {
            throw new RuntimeException("User not found");
        }
        
        return accountRepository.findUniqueAccountIds(userId);
    }
// Add these new methods to your AccountBalanceService class
// Add these new methods to your AccountBalanceService class
// Updated Service methods with correct date calculations
   /**
     * Get account balance period delta for specified months
     * Used by 3-month, 6-month, and 1-year delta endpoints
     */
//  public List<Map<String, Object>> getAccountBalancePeriodDelta(Integer userId, Integer months) {
//     if (userId == null || userId <= 0) {
//         throw new RuntimeException("User not found");
//     }

//     LocalDate currentDate = LocalDate.now();
//     LocalDate currentPeriodStart = currentDate.minusMonths(months).plusDays(1);
//     LocalDate pastPeriodEnd = currentDate.minusMonths(months);
//     LocalDate pastPeriodStart = pastPeriodEnd.minusMonths(months).plusDays(1);

//     List<Object[]> results = accountBalanceRepository.getAccountBalancePeriodDeltaWithDateRange(
//         userId, currentPeriodStart, currentDate, pastPeriodStart, pastPeriodEnd
//     );

//     return convertPeriodDeltaResults(results, currentPeriodStart, currentDate, pastPeriodStart, pastPeriodEnd);
// }
// Updated service method for 1-month delta (30 days)

    // Corrected method for Year to Date delta
    @Cacheable(value = "accountBalanceCache", 
               key = "#root.methodName + '_' + #userId + '_' + #ytdStart + '_' + #ytdEnd + '_' + #pastPeriodStart + '_' + #pastPeriodEnd",
               unless = "#result == null || #result.isEmpty()")
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getAccountBalanceYTDDelta(Integer userId, 
            LocalDate ytdStart, LocalDate ytdEnd, LocalDate pastPeriodStart, LocalDate pastPeriodEnd) {
        if (userId == null || userId <= 0) {
            throw new RuntimeException("User not found");
        }

        List<Object[]> results = accountBalanceRepository.getAccountBalancePeriodDeltaWithDateRange(
            userId, ytdStart, ytdEnd, pastPeriodStart, pastPeriodEnd
        );

        return convertPeriodDeltaResults(results, ytdStart, ytdEnd, pastPeriodStart, pastPeriodEnd);
    }

    /**
     * Get quarterly account balance deltas with corrected rolling comparison logic
     * Current date: 2025-06-02 (Q2)
     * Q2 2025 vs Q1 2025, Q1 2025 vs Q4 2024, Q4 2024 vs Q3 2024, Q3 2024 vs Q2 2024
     */
    @Cacheable(value = "accountBalanceCache", 
               key = "#root.methodName + '_' + #userId",
               unless = "#result == null || #result.isEmpty()")
    @Transactional(readOnly = true)
    public Map<String, List<Map<String, Object>>> getAccountBalanceQuarterlyRollingDeltasGroupedByAccountId(Integer userId) {
        if (userId == null || userId <= 0) {
            throw new RuntimeException("User not found");
        }

        LocalDate currentDate = LocalDate.now(); // 2025-06-02
        int currentYear = currentDate.getYear(); // 2025
        
        Map<String, List<Map<String, Object>>> quarterlyResults = new HashMap<>();
        
        // Define all quarters with corrected dates
        Map<String, QuarterInfo> quarters = new HashMap<>();
        
        // Q1 2025: Jan 1 to Mar 31, 2025
        quarters.put("Q1", new QuarterInfo(
            LocalDate.of(currentYear, 1, 1), 
            LocalDate.of(currentYear, 3, 31)
        ));
        
        // Q2 2025: Apr 1 to Jun 30, 2025
        quarters.put("Q2", new QuarterInfo(
            LocalDate.of(currentYear, 4, 1), 
            LocalDate.of(currentYear, 6, 30)
        ));
        
        // Q3 2024: Jul 1 to Sep 30, 2024
        quarters.put("Q3", new QuarterInfo(
            LocalDate.of(currentYear - 1, 7, 1), 
            LocalDate.of(currentYear - 1, 9, 30)
        ));
        
        // Q4 2024: Oct 1 to Dec 31, 2024
        quarters.put("Q4", new QuarterInfo(
            LocalDate.of(currentYear - 1, 10, 1), 
            LocalDate.of(currentYear - 1, 12, 31)
        ));

        // Process each quarter with corrected rolling comparison logic
        for (String quarter : Arrays.asList("Q1", "Q2", "Q3", "Q4")) {
            QuarterInfo currentQuarterInfo = quarters.get(quarter);
            QuarterInfo pastQuarterInfo = getPreviousQuarter(quarter, quarters);
            
            if (pastQuarterInfo != null) {
                List<Object[]> results = accountBalanceRepository.getAccountBalanceQuarterlyDeltaWithDateRange(
                    userId, 
                    currentQuarterInfo.start, currentQuarterInfo.end,
                    pastQuarterInfo.start, pastQuarterInfo.end
                );

                List<Map<String, Object>> quarterData = convertQuarterlyRollingDeltaResults(
                    results, quarter, currentQuarterInfo, pastQuarterInfo
                );
                quarterlyResults.put(quarter, quarterData);
            }
        }

        return quarterlyResults;
    }

    // Corrected helper method to get previous quarter for rolling comparison
    private QuarterInfo getPreviousQuarter(String currentQuarter, Map<String, QuarterInfo> quarters) {
        switch (currentQuarter) {
            case "Q1":
                return quarters.get("Q4"); // Q1 2025 vs Q4 2024
            case "Q2":
                return quarters.get("Q1"); // Q2 2025 vs Q1 2025
            case "Q3":
                // Q3 2024 vs Q2 2024 (need to create Q2 2024)
                return new QuarterInfo(
                    LocalDate.of(2024, 4, 1), 
                    LocalDate.of(2024, 6, 30)
                );
            case "Q4":
                return quarters.get("Q3"); // Q4 2024 vs Q3 2024
            default:
                return null;
        }
    }

    // Helper class for quarter information
    private static class QuarterInfo {
        LocalDate start;
        LocalDate end;
        
        QuarterInfo(LocalDate start, LocalDate end) {
            this.start = start;
            this.end = end;
        }
    }

    // Corrected converter for rolling quarterly deltas
    private List<Map<String, Object>> convertQuarterlyRollingDeltaResults(List<Object[]> results, 
            String quarter, QuarterInfo currentQuarterInfo, QuarterInfo pastQuarterInfo) {
        
        String currentPeriodDate = currentQuarterInfo.start.toString() + " to " + currentQuarterInfo.end.toString();
        String pastPeriodDate = pastQuarterInfo.start.toString() + " to " + pastQuarterInfo.end.toString();
        
        return results.stream().map(row -> {
            Map<String, Object> account = new HashMap<>();
            account.put("quarter", quarter);
            account.put("accountId", row[0]);
            account.put("accountName", row[1]);
            account.put("accountCategory", row[2]);
            account.put("accountType", row[3]);
            account.put("accountSubtype", row[4]);
            account.put("accountMask", row[5]);
            account.put("institutionId", row[6]);
            account.put("currentBalance", row[7]);
            account.put("pastBalance", row[8]);
            account.put("deltaAmount", row[9]);
            account.put("deltaPercentage", ((Double) row[10]));
            account.put("currentPeriodDate", currentPeriodDate);
            account.put("pastPeriodDate", pastPeriodDate);
            account.put("comparisonType", getQuarterComparisonType(quarter));
            return account;
        }).collect(Collectors.toList());
    }

    // Corrected helper method to describe the comparison type
    private String getQuarterComparisonType(String quarter) {
        switch (quarter) {
            case "Q1":
                return "Q1 2025 vs Q4 2024";
            case "Q2":
                return "Q2 2025 vs Q1 2025";
            case "Q3":
                return "Q3 2024 vs Q2 2024";
            case "Q4":
                return "Q4 2024 vs Q3 2024";
            default:
                return "Unknown comparison";
        }
    }

    // Keep existing helper methods with minor updates for date calculations
    private List<Map<String, Object>> convertPeriodDeltaResults(List<Object[]> results, 
            LocalDate currentPeriodStart, LocalDate currentPeriodEnd, 
            LocalDate pastPeriodStart, LocalDate pastPeriodEnd) {
        
        String currentPeriodDate = currentPeriodStart.toString() + " to " + currentPeriodEnd.toString();
        String pastPeriodDate = pastPeriodStart.toString() + " to " + pastPeriodEnd.toString();
        
        return results.stream().map(row -> {
            Map<String, Object> account = new HashMap<>();
            account.put("accountId", row[0]);
            account.put("accountName", row[1]);
            account.put("accountCategory", row[2]);
            account.put("accountType", row[3]);
            account.put("accountSubtype", row[4]);
            account.put("accountMask", row[5]);
            account.put("institutionId", row[6]);
            account.put("currentBalance", row[7]);
            account.put("pastBalance", row[8]);
            account.put("deltaAmount", row[9]);
            account.put("deltaPercentage", ((Double) row[10]));
            account.put("currentPeriodDate", currentPeriodDate);
            account.put("pastPeriodDate", pastPeriodDate);
            return account;
        }).collect(Collectors.toList());
    }

    /**
     * Get account balance deltas by account type for specified time period
     */
    @Cacheable(value = "accountBalanceCache", 
               key = "#root.methodName + '_' + #userId + '_' + #months",
               unless = "#result == null || #result.isEmpty()")
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getAccountBalanceByAccountType(Integer userId, Integer months) {
        if (userId == null || userId <= 0) {
            throw new RuntimeException("User not found");
        }

        List<Object[]> results = accountBalanceRepository.getAccountBalanceGroupedByTimeperiod(
            userId, months
        );

        return convertAccountTypeResults(results);
    }

    private List<Map<String, Object>> convertAccountTypeResults(List<Object[]> results) {
        return results.stream().map(row -> {
            Map<String, Object> account = new HashMap<>();
            account.put("accountId", row[0]);
            account.put("accountName", row[1]);
            account.put("accountCategory", row[2]);
            account.put("accountType", row[3]);
            account.put("accountSubtype", row[4]);
            account.put("accountMask", row[5]);
            account.put("institutionId", row[6]);
            account.put("currentBalance", row[7]); // <-- fixed parenthesis
            account.put("pastBalance", row[8]);
            account.put("deltaAmount", row[9]);
            account.put("deltaPercentage", ((Double) row[10])); // Return as number, not string
            return account;
        }).collect(Collectors.toList());
    }

    /**
     * Get YTD account balance deltas aggregated by account type
     */
    @Cacheable(value = "accountBalanceCache", 
                key = "#root.methodName + '_' + #userId",
                unless = "#result == null || #result.isEmpty()")
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getAccountBalanceYTDDeltaByAccountType(Integer userId) {
        if (userId == null || userId <= 0) {
            throw new RuntimeException("User not found");
        }

        LocalDate currentDate = LocalDate.now();
        LocalDate ytdStart = LocalDate.of(currentDate.getYear(), 1, 1);
        int ytdDays = (int) ChronoUnit.DAYS.between(ytdStart, currentDate) + 1;
        
        LocalDate pastPeriodEnd = ytdStart.minusDays(1);
        LocalDate pastPeriodStart = pastPeriodEnd.minusDays(ytdDays - 1);
        
        // Get individual account deltas first
        List<Map<String, Object>> accountDeltas = getAccountBalanceYTDDelta(userId, ytdStart, currentDate, pastPeriodStart, pastPeriodEnd);
        
        // Group by account type and aggregate
        return aggregateDeltasByAccountType(accountDeltas);
    }

    /**
     * Get quarterly account balance deltas aggregated by account type
     */
    @Cacheable(value = "accountBalanceCache", 
                key = "#root.methodName + '_' + #userId",
                unless = "#result == null || #result.isEmpty()")
    @Transactional(readOnly = true)
    public Map<String, List<Map<String, Object>>> getQuarterlyDeltaByAccountType(Integer userId) {
        if (userId == null || userId <= 0) {
            throw new RuntimeException("User not found");
        }

        // Get quarterly deltas grouped by account ID
        Map<String, List<Map<String, Object>>> quarterlyDeltas = getAccountBalanceQuarterlyRollingDeltasGroupedByAccountId(userId);
        
        // Transform to group by account type for each quarter
        Map<String, List<Map<String, Object>>> result = new HashMap<>();
        
        for (Map.Entry<String, List<Map<String, Object>>> entry : quarterlyDeltas.entrySet()) {
            String quarter = entry.getKey();
            List<Map<String, Object>> accounts = entry.getValue();
            
            List<Map<String, Object>> aggregatedByType = aggregateDeltasByAccountType(accounts);
            result.put(quarter, aggregatedByType);
        }
        
        return result;
    }

    /**
     * Helper method to aggregate account deltas by account type
     */
    private List<Map<String, Object>> aggregateDeltasByAccountType(List<Map<String, Object>> accountDeltas) {
        // Group accounts by type
        Map<String, List<Map<String, Object>>> groupedByType = accountDeltas.stream()
            .collect(Collectors.groupingBy(account -> (String) account.get("accountType")));
        
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (Map.Entry<String, List<Map<String, Object>>> entry : groupedByType.entrySet()) {
            String accountType = entry.getKey();
            List<Map<String, Object>> accountsOfType = entry.getValue();
            
            // Calculate aggregated values
            double totalCurrentBalance = accountsOfType.stream()
                .mapToDouble(acc -> ((Number) acc.get("currentBalance")).doubleValue())
                .sum();
                
            double totalPastBalance = accountsOfType.stream()
                .mapToDouble(acc -> ((Number) acc.get("pastBalance")).doubleValue())
                .sum();
                
            double totalDeltaAmount = accountsOfType.stream()
                .mapToDouble(acc -> ((Number) acc.get("deltaAmount")).doubleValue())
                .sum();
            
            // Calculate percentage change for the aggregated amounts
            double deltaPercentage = 0.0;
            if (totalPastBalance != 0) {
                deltaPercentage = (totalDeltaAmount / Math.abs(totalPastBalance)) * 100.0;
            } else if (totalCurrentBalance > 0) {
                deltaPercentage = 100.0;
            }
            
            // Get account categories and subtypes for this type
            Set<String> categories = accountsOfType.stream()
                .map(acc -> (String) acc.get("accountCategory"))
                .collect(Collectors.toSet());
                
            Set<String> subtypes = accountsOfType.stream()
                .map(acc -> (String) acc.get("accountSubtype"))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
            
            // Create aggregated result
            Map<String, Object> typeAggregate = new HashMap<>();
            typeAggregate.put("accountType", accountType);
            typeAggregate.put("accountCategories", new ArrayList<>(categories));
            typeAggregate.put("accountSubtypes", new ArrayList<>(subtypes));
            typeAggregate.put("accountCount", accountsOfType.size());
            typeAggregate.put("totalCurrentBalance", Math.round(totalCurrentBalance * 100.0) / 100.0);
            typeAggregate.put("totalPastBalance", Math.round(totalPastBalance * 100.0) / 100.0);
            typeAggregate.put("totalDeltaAmount", Math.round(totalDeltaAmount * 100.0) / 100.0);
            typeAggregate.put("deltaPercentage", Math.round(deltaPercentage * 100.0) / 100.0);
            
            // Add period information if available from first account
            if (!accountsOfType.isEmpty()) {
                Map<String, Object> firstAccount = accountsOfType.get(0);
                if (firstAccount.containsKey("currentPeriodDate")) {
                    typeAggregate.put("currentPeriodDate", firstAccount.get("currentPeriodDate"));
                }
                if (firstAccount.containsKey("pastPeriodDate")) {
                    typeAggregate.put("pastPeriodDate", firstAccount.get("pastPeriodDate"));
                }
                if (firstAccount.containsKey("quarter")) {
                    typeAggregate.put("quarter", firstAccount.get("quarter"));
                }
                if (firstAccount.containsKey("comparisonType")) {
                    typeAggregate.put("comparisonType", firstAccount.get("comparisonType"));
                }
            }
            
            result.add(typeAggregate);
        }
        
        // Sort by account type for consistent ordering
        result.sort((a, b) -> ((String) a.get("accountType")).compareTo((String) b.get("accountType")));
        
        return result;
    }

    // Filter out accounts with no balance data for the period
    @Cacheable(value = "accountBalanceCache", 
                key = "#root.methodName + '_' + #userId + '_' + #months",
                unless = "#result == null || #result.isEmpty()")
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getAccountBalancePeriodDelta(Integer userId, Integer months) {
        if (userId == null || userId <= 0) {
            throw new RuntimeException("User not found");
        }

        LocalDate currentDate = LocalDate.now();
        LocalDate currentPeriodStart;
        LocalDate pastPeriodEnd;
        LocalDate pastPeriodStart;

        if (months == 1) {
            currentPeriodStart = currentDate.minusDays(29);
            pastPeriodEnd = currentDate.minusDays(30);
            pastPeriodStart = pastPeriodEnd.minusDays(29);
        } else {
            currentPeriodStart = currentDate.minusMonths(months).plusDays(1);
            pastPeriodEnd = currentDate.minusMonths(months);
            pastPeriodStart = pastPeriodEnd.minusMonths(months).plusDays(1);
        }

        List<Object[]> results = accountBalanceRepository.getAccountBalancePeriodDeltaWithDateRange(
            userId, currentPeriodStart, currentDate, pastPeriodStart, pastPeriodEnd
        );

        return convertPeriodDeltaResults(results, currentPeriodStart, currentDate, pastPeriodStart, pastPeriodEnd)
            .stream()
            .filter(account -> {
                Double currentBalance = account.get("currentBalance") != null ? ((Number) account.get("currentBalance")).doubleValue() : null;
                Double pastBalance = account.get("pastBalance") != null ? ((Number) account.get("pastBalance")).doubleValue() : null;
                return (currentBalance != null && currentBalance != 0) || (pastBalance != null && pastBalance != 0);
            })
            .collect(Collectors.toList());
    }

    // For account type aggregation, also filter out types with no balances
    @Cacheable(value = "accountBalanceCache", 
                key = "#root.methodName + '_' + #userId + '_' + #months",
                unless = "#result == null || #result.isEmpty()")
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getAccountBalanceDeltaByAccountType(Integer userId, Integer months) {
        List<Map<String, Object>> accountDeltas = getAccountBalancePeriodDelta(userId, months);
        List<Map<String, Object>> aggregated = aggregateDeltasByAccountType(accountDeltas);
        // Only include types with at least one account and non-zero balances
        return aggregated.stream()
            .filter(typeAgg -> {
                Double totalCurrentBalance = typeAgg.get("totalCurrentBalance") != null ? ((Number) typeAgg.get("totalCurrentBalance")).doubleValue() : null;
                Double totalPastBalance = typeAgg.get("totalPastBalance") != null ? ((Number) typeAgg.get("totalPastBalance")).doubleValue() : null;
                return (totalCurrentBalance != null && totalCurrentBalance != 0) || (totalPastBalance != null && totalPastBalance != 0);
            })
            .collect(Collectors.toList());
    }

    // Method for clearing cache related to account balances
    public void clearAccountBalanceCache(Integer userId) {
        clearCacheByPatterns("accountBalanceCache",
            "getBalancesGroupedByAccountType_" + userId,
            "getAggregatedBalances_" + userId + "_",
            "getAggregatedBalancesByAccountType_" + userId + "_",
            "getAccountBalanceMonthlyDeltas_" + userId + "_",
            "getAggregatedBalancesGroupedByAccountId_" + userId + "_",
            "getAccountBalanceMonthlyDeltasGroupedByAccountId_" + userId + "_",
            "getUniqueAccountIds_" + userId,
            "getAccountBalanceYTDDelta_" + userId + "_",
            "getAccountBalanceQuarterlyRollingDeltasGroupedByAccountId_" + userId + "_",
            "getAccountBalanceByAccountType_" + userId + "_",
            "getAccountBalanceYTDDeltaByAccountType_" + userId,
            "getAccountBalancePeriodDelta_" + userId + "_",
            "getQuarterlyDeltaByAccountType_" + userId,
            "getAccountBalanceDeltaByAccountType_" + userId + "_"
        );
    }

    private void clearCacheByPatterns(String cacheName, String... patterns) {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
            if (cache != null && cache instanceof CaffeineCache caffeineCache) {
                com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache =
                    (com.github.benmanes.caffeine.cache.Cache<Object, Object>) caffeineCache.getNativeCache();
    
                // Remove all cache entries that match any of the provided patterns
                nativeCache.asMap().keySet().removeIf(key -> {
                    if (key instanceof String keyStr) {
                        for (String pattern : patterns) {
                            if (keyStr.startsWith(pattern)) {
                                return true;
                            }
                        }
                    }
                    return false;
                });
    
                log.info("Cleared cache entries matching patterns: {} from cache: {}",
                    java.util.Arrays.toString(patterns), cacheName);
            } else {
                log.warn("Cache '{}' not found or not a CaffeineCache instance", cacheName);
            }
        } catch (Exception e) {
            log.error("Error clearing cache '{}' with patterns: {}",
                cacheName, java.util.Arrays.toString(patterns), e);
        }
    }
}