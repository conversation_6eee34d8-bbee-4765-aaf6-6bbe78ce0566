package com.pennypal.fintech.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.pennypal.fintech.dto.CategoryMonthlySummaryDto;

import com.pennypal.fintech.dto.BudgetDto;
import com.pennypal.fintech.dto.BudgetExpenseDto;
import com.pennypal.fintech.dto.BudgetUIDto;
import com.pennypal.fintech.dto.BudgetRuleDto;
import com.pennypal.fintech.dto.CategoryDto;
import com.pennypal.fintech.dto.CategoryMonthlySummaryDto;
import com.pennypal.fintech.dto.SankeyChartDto;
import com.pennypal.fintech.dto.SubCategoryDto;
import com.pennypal.fintech.dto.TransactionDto;
import com.pennypal.fintech.repository.TransactionRepository;

import com.pennypal.fintech.entity.Budget;
import com.pennypal.fintech.entity.BudgetRules;
import com.pennypal.fintech.entity.Category;
import com.pennypal.fintech.entity.NotificationRules;
import com.pennypal.fintech.entity.SubCategory;
import com.pennypal.fintech.entity.Transactions;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.entity.UserNotificationRules;
import com.pennypal.fintech.entity.CustomSubCategory;

import com.pennypal.fintech.repository.BudgetRepository;
import com.pennypal.fintech.repository.CategoryRepository;
import com.pennypal.fintech.repository.CustomSubCategoryRepository;
import com.pennypal.fintech.repository.NotificationRuleRepository;
import com.pennypal.fintech.repository.NotificationTrackingRepository;
import com.pennypal.fintech.repository.SubCategoryRepository;
import com.pennypal.fintech.repository.UserRepository;
import com.pennypal.fintech.repository.UserNotificationRulesRepository;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

@Service
@Transactional
@Slf4j
public class BudgetService {
    
    @Autowired
    private final BudgetRepository budgetRepository;
    @Autowired
    private final CategoryRepository categoryRepository;
    @Autowired
    private final SubCategoryRepository subCategoryRepository;
    @Autowired
    private final CustomSubCategoryRepository customSubCategoryRepository;
    @Autowired
    private final UserRepository userRepository;
    @Autowired
    private final NotificationRuleRepository notificationRuleRepository;
    @Autowired
    private final NotificationTrackingRepository notificationTrackingRepository;
    @Autowired
    private final NotificationRuleService notificationRuleService;
    @Autowired
    private final UserNotificationRulesRepository userNotificationRulesRepository;

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private CacheManager cacheManager;
    
    public BudgetService(BudgetRepository budgetRepository, CategoryRepository categoryRepository,
                         SubCategoryRepository subCategoryRepository, CustomSubCategoryRepository customSubCategoryRepository, 
                         UserRepository userRepository, NotificationRuleRepository notificationRuleRepository,
                         NotificationTrackingRepository notificationTrackingRepository,
                         NotificationRuleService notificationRuleService,
                         UserNotificationRulesRepository userNotificationRulesRepository) {
        this.budgetRepository = budgetRepository;
        this.categoryRepository = categoryRepository;
        this.subCategoryRepository = subCategoryRepository;
        this.customSubCategoryRepository = customSubCategoryRepository;
        this.userRepository = userRepository;
        this.notificationRuleRepository = notificationRuleRepository;
        this.notificationTrackingRepository = notificationTrackingRepository;
        this.notificationRuleService = notificationRuleService;
        this.userNotificationRulesRepository = userNotificationRulesRepository;
    }

    public BudgetDto createBudget(BudgetDto budgetDto) {
        log.info("Creating budget with data: {}", budgetDto);
        log.info("CategoryId(): {}", budgetDto.getCategoryId());
        if (budgetDto.getCategoryId() == null) {
            throw new IllegalArgumentException("Category ID must be provided.");
        }

        Category category = categoryRepository.findById(budgetDto.getCategoryId())
                .orElseThrow(() -> new RuntimeException("Category not found"));
        log.info("Category found: {}", category);
        SubCategory subCategory = null;
        Integer customSubCategoryId = null;

        log.info("SubcategoryId(): {}", budgetDto.getSubcategoryId());
        log.info("CustomSubCategoryName(): {}", budgetDto.getCustomSubCategoryName());
        log.info("CustomSubCategoryId(): {}", budgetDto.getCustomSubCategoryId());
        // 1. User selects a default subcategory
        if (budgetDto.getSubcategoryId() != null) {
            subCategory = subCategoryRepository.findById(budgetDto.getSubcategoryId())
                    .orElseThrow(() -> new RuntimeException("Subcategory not found"));
            log.info("Subcategory found: {}", subCategory);
        }
        // 2. User types a custom subcategory name (new or reused)
        else if (budgetDto.getCustomSubCategoryName() != null &&
                 !budgetDto.getCustomSubCategoryName().trim().isEmpty()) {

            String name = budgetDto.getCustomSubCategoryName().trim();
            String icon = (budgetDto.getIconKey() != null && !budgetDto.getIconKey().trim().isEmpty())
                            ? budgetDto.getIconKey()
                            : "FaMiscellaneous";

            // Check if the custom subcategory already exists for this user
            Optional<CustomSubCategory> existing = customSubCategoryRepository
                    .findByUserIdAndCustomSubCategoryName(budgetDto.getUserId(), name);
            log.info("Custom subcategory exists: {}", existing.isPresent());

            CustomSubCategory customSubCategory;
            if (existing.isPresent()) {
                customSubCategory = existing.get();
                log.info("Reusing existing custom subcategory: {} for user: {}", name, budgetDto.getUserId());
            } else {
                customSubCategory = new CustomSubCategory();
                customSubCategory.setUserId(budgetDto.getUserId());
                customSubCategory.setCategoryId(budgetDto.getCategoryId());
                customSubCategory.setCustomSubCategoryName(name);
                customSubCategory.setCustomSubCategoryIcon(icon);
                customSubCategory = customSubCategoryRepository.save(customSubCategory);
                log.info("Created new custom subcategory: {} for user: {}", name, budgetDto.getUserId());
            }

            customSubCategoryId = customSubCategory.getCustomSubCategoryId();
        }
        // 3. User provides an existing customSubCategoryId
        else if (budgetDto.getCustomSubCategoryId() != null) {
            CustomSubCategory existingCustomSubCategory = customSubCategoryRepository
                    .findById(budgetDto.getCustomSubCategoryId())
                    .orElseThrow(() -> new RuntimeException("Custom subcategory not found"));

            if (!existingCustomSubCategory.getUserId().equals(budgetDto.getUserId())) {
                throw new RuntimeException("Custom subcategory does not belong to the user");
            }

            customSubCategoryId = existingCustomSubCategory.getCustomSubCategoryId();
        }

        Users user = userRepository.findById(budgetDto.getUserId())
                .orElseThrow(() -> new RuntimeException("User not found"));

        Budget budget = new Budget();

        /**
         * APT-81 fix for duplicate budget
         * If budget already exists for this category and subCategory/custom subCategory,
         * for same month/year and user, update that instead of creating a new one
         */
        if (customSubCategoryId != null) {
            Optional<Budget> existingBudget = budgetRepository.findLatestBudgetByCategoryIdAndCustomSubCategoryId(
                budgetDto.getUserId(),
                budgetDto.getCategoryId(),
                customSubCategoryId,
                budgetDto.getDateAsLocalDate());

            if (existingBudget.isPresent()) {
                budget = existingBudget.get();
                log.info("Reusing existing budget: {} for user: {}", budget.getId(), budgetDto.getUserId());
                budget.setAllocated(budgetDto.getAllocated() != null ? budgetDto.getAllocated() : BigDecimal.ZERO);
                String iconKey = determineIconKey(budgetDto.getIconKey(), null, customSubCategoryId, null);
                budget.setIcon(iconKey);
                budget.setRollover(Boolean.TRUE.equals(budgetDto.getIsRollover()));
                budget.setExclude(Boolean.TRUE.equals(budgetDto.getIsExcluded()));
                budget.setRemaining(budget.getAllocated().subtract(budget.getActual()).setScale(2, RoundingMode.HALF_EVEN));

                Budget savedBudget = budgetRepository.save(budget);

                // Evict only the specific entry from the cache
                int year = budgetDto.getDateAsLocalDate().getYear();
                int month = budgetDto.getDateAsLocalDate().getMonthValue();
                // String cacheKey = "getBudgetByUserAndYearAndMonth_" + budgetDto.getUserId() + "_" + year + "_" + month;
                // cacheManager.getCache("budgetCache").evict(cacheKey);
                clearBudgetRelatedCache(budgetDto.getUserId(), year, month);
                
                return convertToDto(savedBudget);
            }
        }
        if (budgetDto.getSubcategoryId() != null) {
            Optional<Budget> existingBudget = budgetRepository.findLatestBudgetByCategoryIdAndSubCategoryId(
                budgetDto.getUserId(),
                budgetDto.getCategoryId(),
                budgetDto.getSubcategoryId(),
                budgetDto.getDateAsLocalDate());

            if (existingBudget.isPresent()) {
                budget = existingBudget.get();
                log.info("Reusing existing budget: {} for user: {}", budget.getId(), budgetDto.getUserId());
                budget.setAllocated(budgetDto.getAllocated() != null ? budgetDto.getAllocated() : BigDecimal.ZERO);
                String iconKey = determineIconKey(budgetDto.getIconKey(), subCategory, null, null);
                budget.setIcon(iconKey);
                budget.setRollover(Boolean.TRUE.equals(budgetDto.getIsRollover()));
                budget.setExclude(Boolean.TRUE.equals(budgetDto.getIsExcluded()));
                budget.setRemaining(budget.getAllocated().subtract(budget.getActual()).setScale(2, RoundingMode.HALF_EVEN));

                Budget savedBudget = budgetRepository.save(budget);

                // Evict only the specific entry from the cache
                int year = budgetDto.getDateAsLocalDate().getYear();
                int month = budgetDto.getDateAsLocalDate().getMonthValue();
                // String cacheKey = "getBudgetByUserAndYearAndMonth_" + budgetDto.getUserId() + "_" + year + "_" + month;
                // cacheManager.getCache("budgetCache").evict(cacheKey);
                clearBudgetRelatedCache(budgetDto.getUserId(), year, month);
                
                return convertToDto(savedBudget);
            }
        }
        /** End of APT-81 fix */
        budget.setCategory(category);
        budget.setSubCategory(subCategory);
        budget.setCustomSubCategoryId(customSubCategoryId);
        budget.setUser(user);
        budget.setAllocated(budgetDto.getAllocated() != null ? budgetDto.getAllocated() : BigDecimal.ZERO);
        budget.setActual(BigDecimal.ZERO);
        budget.setRollover(Boolean.TRUE.equals(budgetDto.getIsRollover()));
        budget.setExclude(Boolean.TRUE.equals(budgetDto.getIsExcluded()));
        budget.setDate(budgetDto.getDateAsLocalDate() != null ? budgetDto.getDateAsLocalDate() : LocalDate.now());
        budget.setUser(user);
        budget.setRemaining(budget.getAllocated().subtract(budget.getActual()).setScale(2, RoundingMode.HALF_EVEN));

        // Set icon with fallback logic
        String iconKey = determineIconKey(budgetDto.getIconKey(), subCategory, customSubCategoryId, category);
        budget.setIcon(iconKey);

        Budget savedBudget = budgetRepository.save(budget);

        log.info("Budget created successfully with ID: {}", savedBudget.getId());

        /**
         * APT-123/124 fix for slow loading
         * Evict only the specific entry from the cache
         */
        int year = budgetDto.getDateAsLocalDate().getYear();
        int month = budgetDto.getDateAsLocalDate().getMonthValue();
        // String cacheKey = "getBudgetByUserAndYearAndMonth_" + budgetDto.getUserId() + "_" + year + "_" + month;
        // cacheManager.getCache("budgetCache").evict(cacheKey);
        clearBudgetRelatedCache(budgetDto.getUserId(), year, month);
        /** End of APT-123/124 fix */
        
        return convertToDto(savedBudget);
    }

    private String determineIconKey(String providedIcon, SubCategory subCategory, Integer customSubCategoryId, Category category) {
        // 1. Use provided icon if valid
        if (providedIcon != null && !providedIcon.trim().isEmpty() && !providedIcon.equalsIgnoreCase("null")) {
            return providedIcon.trim();
        }
        
        // 2. Use subcategory icon if available
        if (subCategory != null && subCategory.getIconKey() != null && !subCategory.getIconKey().trim().isEmpty()) {
            return subCategory.getIconKey();
        }
        
        // 3. Use custom subcategory icon if available
        if (customSubCategoryId != null) {
            Optional<CustomSubCategory> customSubCategory = customSubCategoryRepository.findById(customSubCategoryId);
            if (customSubCategory.isPresent() && 
                customSubCategory.get().getCustomSubCategoryIcon() != null && 
                !customSubCategory.get().getCustomSubCategoryIcon().trim().isEmpty()) {
                return customSubCategory.get().getCustomSubCategoryIcon();
            }
        }
        
        // 4. Use category icon if available
        if (category.getCategoryIconKey() != null && !category.getCategoryIconKey().trim().isEmpty()) {
            return category.getCategoryIconKey();
        }
        
        // 5. Default fallback
        return "FaMiscellaneous";
    }
    
    private BudgetDto convertToDto(Budget budget) {
        BudgetDto dto = new BudgetDto();
        dto.setId(budget.getId());
        dto.setCategoryId(budget.getCategory().getId());
        dto.setCategoryName(budget.getCategory().getCategory());
        if (budget.getSubCategory() != null) {
            dto.setSubcategoryId(budget.getSubCategory().getId());
            dto.setSubcategoryName(budget.getSubCategory().getSubCategory());
            dto.setCustomSubCategoryId(null);
            dto.setCustomSubCategoryName(null);
        } else if (budget.getCustomSubCategoryId() != null) {
            dto.setSubcategoryId(null);
            dto.setSubcategoryName(null);
            dto.setCustomSubCategoryId(budget.getCustomSubCategoryId());
            
            // Get custom subcategory name
            Optional<CustomSubCategory> customSubCategory = 
                customSubCategoryRepository.findById(budget.getCustomSubCategoryId());
            if (customSubCategory.isPresent()) {
                dto.setCustomSubCategoryName(customSubCategory.get().getCustomSubCategoryName());
            }
        } else {
            dto.setSubcategoryId(null);
            dto.setCustomSubCategoryId(null);
            dto.setCustomSubCategoryName(null);
        }
        dto.setAllocated(budget.getAllocated());
        dto.setActual(budget.getActual());
      
        dto.setIsRollover(budget.getRollover());
        dto.setIsExcluded(budget.getExclude());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        dto.setDate(budget.getDate() != null ? budget.getDate().format(formatter) : null);
        if (budget.getUser() != null) {
            dto.setUserId(budget.getUser().getId());
        }
        
        // Set icon using helper method
        dto.setIconKey(determineIconKey(budget.getIcon(), budget.getSubCategory(), 
                                       budget.getCustomSubCategoryId(), budget.getCategory()));
        
        return dto;
    }

    public BudgetDto mapToDto(Budget budget) {
        return convertToDto(budget);
    }

    public Budget updateBudget(BudgetDto dto) {
        if (dto.getAllocated() == null || dto.getActual() == null) {
            throw new RuntimeException("Allocated or Actual value cannot be null");
        }
        if (dto.getId() == 0) {
            throw new RuntimeException("Invalid budget ID: " + dto.getId());
        }
        
        Budget existingBudget = budgetRepository.findById(dto.getId())
                .orElseThrow(() -> new RuntimeException("Budget not found with ID: " + dto.getId()));

        log.info("Updating Budget: ID={}", dto.getId());
        log.info("Old Allocated: {}, New Allocated: {}", existingBudget.getAllocated(), dto.getAllocated());
        log.info("Old Actual: {}, New Actual: {}", existingBudget.getActual(), dto.getActual());

        existingBudget.setAllocated(dto.getAllocated());
        existingBudget.setActual(dto.getActual());
        
        BigDecimal remaining = existingBudget.getAllocated().subtract(existingBudget.getActual());
        if (remaining.compareTo(BigDecimal.ZERO) < 0) {
            log.warn("Remaining budget is negative: {}", remaining);
            // Don't throw exception, just log warning - allow negative budgets
        }
        existingBudget.setRemaining(remaining.setScale(2, RoundingMode.HALF_EVEN));
        
        existingBudget.setIsDynamic(dto.getIsDynamic() != null ? dto.getIsDynamic() : existingBudget.getIsDynamic());
        existingBudget.setDynamicAllocated(dto.getDynamicAllocated() != null ? dto.getDynamicAllocated() : existingBudget.getDynamicAllocated());
        
        // Handle subcategory updates
        handleSubcategoryUpdate(dto, existingBudget);
        
        // Handle date update
        try {
            if (dto.getDate() != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                existingBudget.setDate(LocalDate.parse(dto.getDate(), formatter));
            }
        } catch (DateTimeParseException e) {
            log.error("Error parsing date: {}", dto.getDate());
            throw new RuntimeException("Invalid date format: " + dto.getDate(), e);
        }
        
        // Update icon
        if (dto.getIconKey() != null) {
            existingBudget.setIcon(dto.getIconKey());
        }

        // Handle rollover
        if (dto.getIsRollover() != null) {
            existingBudget.setRollover(dto.getIsRollover());
        }

        // Propogate to future budgets if rollover is enabled/disabled
        if (dto.getIsRollover() != null) {
            propagateRolloverChange(existingBudget, dto.getIsRollover());
        }

        /**
         * APT-123/124 fix for slow loading
         * Evict only the specific entry from the cache
         */
        int year = existingBudget.getDate().getYear();
        int month = existingBudget.getDate().getMonthValue();
        // String cacheKey = "getBudgetByUserAndYearAndMonth_" + existingBudget.getUser().getId() + "_" + year + "_" + month;
        // cacheManager.getCache("budgetCache").evict(cacheKey);
        clearBudgetRelatedCache(existingBudget.getUser().getId(), year, month);
        /** End of APT-123/124 fix */
        
        return budgetRepository.save(existingBudget);
    }

    private void propagateRolloverChange(Budget existingBudget, boolean newRolloverValue) {
        List<Budget> futureBudgets = new ArrayList<>();
        if (existingBudget.getCustomSubCategoryId() != null) {
            futureBudgets = budgetRepository.findFutureBudgetsByCategoryIdAndCustomSubCategoryId(
                existingBudget.getUser().getId(),
                existingBudget.getCategory().getId(),
                existingBudget.getCustomSubCategoryId(),
                existingBudget.getDate());
        } else {
            futureBudgets = budgetRepository.findFutureBudgetsByCategoryIdAndSubCategoryId(
                existingBudget.getUser().getId(),
                existingBudget.getCategory().getId(),
                existingBudget.getSubCategory() != null ? existingBudget.getSubCategory().getId() : null,
                existingBudget.getDate());
        }

        // If no future budgets, return
        if (futureBudgets.isEmpty()) {
            log.info("No future budgets found to propagate rollover change.");
            return;
        }

        // Update rollover for all future budgets
        log.info("Propagating rollover change to {} future budgets", futureBudgets.size());
        for (Budget futureBudget : futureBudgets) {
            futureBudget.setRollover(newRolloverValue);
            budgetRepository.save(futureBudget);
        }
    }

    private void handleSubcategoryUpdate(BudgetDto dto, Budget existingBudget) {
        // Handle custom subcategory creation/update from name
        if (dto.getCustomSubCategoryName() != null && !dto.getCustomSubCategoryName().trim().isEmpty()) {
            String name = dto.getCustomSubCategoryName().trim();
            String icon = (dto.getIconKey() != null && !dto.getIconKey().trim().isEmpty()) 
                         ? dto.getIconKey() : "FaMiscellaneous";

            // Check if custom subcategory already exists for this user
            Optional<CustomSubCategory> existing = customSubCategoryRepository
                    .findByUserIdAndCustomSubCategoryName(existingBudget.getUser().getId(), name);

            CustomSubCategory customSubCategory;
            if (existing.isPresent()) {
                customSubCategory = existing.get();
            } else {
                customSubCategory = new CustomSubCategory();
                customSubCategory.setUserId(existingBudget.getUser().getId());
                customSubCategory.setCustomSubCategoryName(name);
                customSubCategory.setCustomSubCategoryIcon(icon);
                customSubCategory = customSubCategoryRepository.save(customSubCategory);
            }

            existingBudget.setCustomSubCategoryId(customSubCategory.getCustomSubCategoryId());
            existingBudget.setSubCategory(null); // Clear default subcategory
        }
        // Handle existing custom subcategory ID
        else if (dto.getCustomSubCategoryId() != null) {
            CustomSubCategory customSubCategory = customSubCategoryRepository.findById(dto.getCustomSubCategoryId())
                    .orElseThrow(() -> new RuntimeException("Custom subcategory not found"));
            
            if (!customSubCategory.getUserId().equals(existingBudget.getUser().getId())) {
                throw new RuntimeException("Custom subcategory does not belong to the user");
            }
            
            existingBudget.setCustomSubCategoryId(dto.getCustomSubCategoryId());
            existingBudget.setSubCategory(null); // Clear default subcategory
        }
        // Handle default subcategory
        else if (dto.getSubcategoryId() != null) {
            SubCategory subCategory = subCategoryRepository.findById(dto.getSubcategoryId())
                    .orElseThrow(() -> new RuntimeException("Subcategory not found"));
            existingBudget.setSubCategory(subCategory);
            existingBudget.setCustomSubCategoryId(null); // Clear custom subcategory
        }
    }

    // Update budget (old method - kept for backward compatibility)
    public Budget updateBudget(Budget budget) {
        Budget existingBudget = budgetRepository.findById(budget.getId())
                .orElseThrow(() -> new RuntimeException("Budget not found"));
        
        // Update fields
        existingBudget.setAllocated(budget.getAllocated());
        existingBudget.setActual(budget.getActual());
        
        // Calculate remaining
        BigDecimal remaining = budget.getAllocated().subtract(budget.getActual());
        remaining = remaining.setScale(2, RoundingMode.HALF_EVEN);
        existingBudget.setRemaining(remaining);
        existingBudget.setIsDynamic(budget.getIsDynamic());
        existingBudget.setDynamicAllocated(budget.getDynamicAllocated());

        log.info("Updated Budget - Allocated: {}, Actual: {}, Remaining: {}", 
                budget.getAllocated(), budget.getActual(), remaining);

        return budgetRepository.save(existingBudget);
    }

  // In BudgetService.java
public List<BudgetDto> getBudgetsByUser(Integer userId, Integer month) {
    List<Budget> budgets;

        if (month != null) {
            budgets = budgetRepository.findByUserWithMonth(userId, month);
        } else {
            budgets = budgetRepository.findByUserId(userId);
        }
        
        List<BudgetDto> budgetDtos = new ArrayList<>();
        Map<Integer, BudgetDto> dtoMap = new HashMap<>();
        
        for (Budget budget : budgets) {
            Category category = budget.getCategory();
            SubCategory subCategory = budget.getSubCategory();
            Integer customSubCategoryId = budget.getCustomSubCategoryId();
            
            // Skip if neither subcategory nor custom subcategory exists
            if (subCategory == null && customSubCategoryId == null) {
                continue;
            }

            SubCategoryDto subCategoryDto = new SubCategoryDto();
            if (subCategory != null) {
                subCategoryDto.setId(subCategory.getId());
                subCategoryDto.setSubCategory(subCategory.getSubCategory());
                String subIcon = subCategory.getIconKey();
                if (subIcon == null || subIcon.trim().isEmpty() || subIcon.equalsIgnoreCase("null")) {
                    subIcon = "FaMiscellaneous";
                }
                subCategoryDto.setIconKey(subIcon);
            } else if (customSubCategoryId != null) {
                // Handle custom subcategory
                Optional<CustomSubCategory> customSubCategory = 
                    customSubCategoryRepository.findById(customSubCategoryId);
                if (customSubCategory.isPresent()) {
                    subCategoryDto.setId(customSubCategory.get().getCustomSubCategoryId());
                    subCategoryDto.setSubCategory(customSubCategory.get().getCustomSubCategoryName());
                    String customIcon = customSubCategory.get().getCustomSubCategoryIcon();
                    if (customIcon == null || customIcon.trim().isEmpty() || customIcon.equalsIgnoreCase("null")) {
                        customIcon = "FaMiscellaneous";
                    }
                    subCategoryDto.setIconKey(customIcon);
                }
            }
            subCategoryDto.setBudget(budget.getAllocated().doubleValue());
            subCategoryDto.setSpent(budget.getActual().doubleValue());
            BigDecimal remaining = budget.getAllocated().subtract(budget.getActual()).setScale(2, RoundingMode.HALF_EVEN);
            subCategoryDto.setRemaining(remaining.doubleValue());

        // Check if the BudgetDto for this category already exists
        BudgetDto budgetDto;
        if (dtoMap.containsKey(category.getId())) {
            budgetDto = dtoMap.get(category.getId());
            budgetDto.getCategory().getSubCategories().add(subCategoryDto);  // Add subCategory to the existing list
        } else {
            // Create a new BudgetDto if it doesn't exist for this category
            budgetDto = new BudgetDto();
			budgetDto.setId(budget.getId());
            CategoryDto categoryDto = new CategoryDto();
            categoryDto.setId(category.getId());
            categoryDto.setCategory(category.getCategory());
			categoryDto.setCategoryIconKey(category.getCategoryIconKey());
            budgetDto.setCategory(categoryDto);

                List<SubCategoryDto> subCatDtoList = new ArrayList<>();
                subCatDtoList.add(subCategoryDto);
                categoryDto.setSubCategories(subCatDtoList);
                
                // Set icon key using helper method
                budgetDto.setIconKey(determineIconKey(budget.getIcon(), subCategory, customSubCategoryId, category));
                
                dtoMap.put(category.getId(), budgetDto);
            }
        }
        return new ArrayList<>(dtoMap.values());
    }

    public BudgetDto getBudgetWithIconKey(Integer id) {
        Budget budget = budgetRepository.findByIdWithSubCategoryAndIconKey(id);
        return mapToDto(budget);
    }
	  /**
     * Get all custom subcategories for a user
     */
    public List<CustomSubCategory> getCustomSubCategoriesByUser(Integer userId) {
        return customSubCategoryRepository.findByUserId(userId);}
   /**
     * Delete custom subcategory
     */
    public void deleteCustomSubCategory(Integer customSubCategoryId, Integer userId) {
        Optional<CustomSubCategory> customSubCategory = customSubCategoryRepository.findById(customSubCategoryId);
        if (customSubCategory.isPresent() && customSubCategory.get().getUserId().equals(userId)) {
            customSubCategoryRepository.deleteById(customSubCategoryId);
        } else {
            throw new RuntimeException("Custom subcategory not found or access denied");
        }
    }


    
    // Get budgets by category
    public List<Budget> getBudgetsByCategory(Integer categoryId) {
        return budgetRepository.findByCategoryId(categoryId);
    }

    // Delete budget
    public void deleteBudget(Integer budgetId) {
        // Get userId, year, month from budget
        Budget budget = budgetRepository.findById(budgetId).orElse(null);
        if (budget != null) {
            int userId = budget.getUser().getId();
            int year = budget.getDate().getYear();
            int month = budget.getDate().getMonthValue();
            clearBudgetRelatedCache(userId, year, month);
        }

        budgetRepository.deleteById(budgetId);
    }

    @Transactional
    public void updateBudgetWithTxn(TransactionDto transaction) {
        log.info("updateBudgetWithTxn called with transaction: {}", transaction);

        // Find the subcategory using the transaction's category name
        // SubCategory subCategory = subCategoryRepository.findBySubCategory(
        //     transaction.getCategory())

        if ("yes".equalsIgnoreCase(transaction.getReconcileFlag())) {
            log.info("Skipping budget update for reconciled transaction: {}", transaction.getId());
            return;
        }

        if (transaction.getReconcileFlag() != null && transaction.getReconcileFlag().equalsIgnoreCase("yes")) {
            log.info("Skipping budget update for reconciled transaction: {}", transaction.getId());
            return;
        }

        // Skip if transaction is marked to hide from budget
        if (transaction.isHideFromBudget()) {
            log.info("Skipping budget update for hidden transaction: {}", transaction.getId());
            return;
        }

        SubCategory subCategory = subCategoryRepository.findById(
            transaction.getSubCategoryId())
            .orElseThrow(() -> new RuntimeException("Category not found"));
        log.info("Subcategory found: {}", subCategory);
        
        // Flag to track if the budget is new or existing
        Optional<Budget> existingBudget = budgetRepository.findLatestBudgetByCategoryIdAndSubCategoryId(
            transaction.getUserId(),
            subCategory.getCategory().getId(),
            subCategory.getId(),
            transaction.getTransactionDate().toLocalDate());

        final boolean isNewBudget = !existingBudget.isPresent();
        log.info("isNewBudget: {}", isNewBudget);

        // Find the budget
        Budget budget = existingBudget
            // If no budget found, create a new one
            .orElseGet(() -> {
                Budget newBudget = new Budget();
                Users user = userRepository.findById(transaction.getUserId())
                    .orElseThrow(() -> new RuntimeException("User not found"));
                    
                newBudget.setUser(user);
                newBudget.setCategory(subCategory.getCategory());
                newBudget.setSubCategory(subCategory);
                newBudget.setDate(transaction.getTransactionDate().toLocalDate().withDayOfMonth(1));
                // newBudget.setAllocated(BigDecimal.ZERO);
                // rollover logic - check if previous month has a rollover budget with the same category and subcategory
                Optional<Budget> previousMonthBudget = budgetRepository.findLatestBudgetByCategoryIdAndSubCategoryId(
                    transaction.getUserId(),
                    subCategory.getCategory().getId(),
                    subCategory.getId(),
                    transaction.getTransactionDate().toLocalDate().minusMonths(1));
                if (previousMonthBudget.isPresent()) {
                    newBudget.setAllocated(previousMonthBudget.get().getAllocated());
                    newBudget.setRollover(true);
                } else {
                    newBudget.setAllocated(BigDecimal.ZERO);
                    newBudget.setRollover(false);
                }
                newBudget.setActual(BigDecimal.ZERO);
                newBudget.setRemaining(BigDecimal.ZERO);
                
                return budgetRepository.save(newBudget);
            });
        
        log.info("Budget found: {}", budget);
        // Update the budget
        // budget.setActual(budget.getActual().add(BigDecimal.valueOf(transaction.getTransactionAmount())));
        BigDecimal amount = BigDecimal.valueOf(transaction.getTransactionAmount());
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            amount = amount.multiply(BigDecimal.valueOf(-1));
        }
        budget.setActual(budget.getActual().add(amount));
        budget.setRemaining(budget.getAllocated().subtract(budget.getActual()));
        log.info("Updated budget: {}", budget);
        
        // Save the updated budget
        Budget savedBudget = budgetRepository.save(budget);

        // Check notification rules for new budget
        if (isNewBudget) {
            log.info("Checking new budget notification rules for budget: {}", savedBudget);
            checkNewBudgetNotificationRules(transaction, savedBudget);
        }

        // Check notification rules
        if (savedBudget.getAllocated().compareTo(BigDecimal.ZERO) > 0) {
            log.info("Checking notification rules for budget: {}", savedBudget);
            checkNotificationRules(transaction, savedBudget, subCategory.getSubCategory());
        }

        /**
         * APT-123/124 fix for slow loading
         * Evict only the specific entry from the cache
         */
        int year = savedBudget.getDate().getYear();
        int month = savedBudget.getDate().getMonthValue();
        // String cacheKey = "getBudgetByUserAndYearAndMonth_" + transaction.getUserId() + "_" + year + "_" + month;
        // cacheManager.getCache("budgetCache").evict(cacheKey);
        clearBudgetRelatedCache(transaction.getUserId(), year, month);
        /** End of APT-123/124 fix */
    }
    
    public void updateBudgetWithTxnWithRule(TransactionDto transaction,
                                            BudgetRules budgetRule,
                                            String txnCategory) {
        log.info("updateBudgetWithTxnWithRule called with transaction: {}, budgetRule: {}, txnCategory: {}", transaction, budgetRule, txnCategory);

        if ("yes".equalsIgnoreCase(transaction.getReconcileFlag())) {
            log.info("Skipping budget update for reconciled transaction: {}", transaction.getId());
            return;
        }

        if (transaction.getReconcileFlag() != null && transaction.getReconcileFlag().equalsIgnoreCase("yes")) {
            log.info("Skipping budget update for reconciled transaction: {}", transaction.getId());
            return;
        }

        // Skip if transaction is marked to hide from budget
        if (transaction.isHideFromBudget()) {
            log.info("Skipping budget update for hidden transaction: {}", transaction.getId());
            return;
        }

        // Find the target subcategory using the transaction's category name
        // SubCategory toSubCategory = subCategoryRepository.findBySubCategory(transaction.getCategory())
        // SubCategory toSubCategory = subCategoryRepository.findById(transaction.getSubCategoryId())
        //     .orElseThrow(() -> new RuntimeException("Category not found"));
        // log.info("Target Subcategory found: {}", toSubCategory);
        
        // Find the source subcategory using the saved txnCategory
        SubCategory fromSubCategory = subCategoryRepository.findBySubCategory(txnCategory)
            .orElseThrow(() -> new RuntimeException("Subcategory not found"));
        log.info("Source Subcategory found: {}", fromSubCategory);
        
        // Flag to track if the budget is new or existing
        // Optional<Budget> existingBudget = budgetRepository.findLatestBudgetByCategoryIdAndSubCategoryId(
        //     transaction.getUserId(),
        //     toSubCategory.getCategory().getId(),
        //     toSubCategory.getId(),
        //     transaction.getTransactionDate().toLocalDate());

        // final boolean isNewBudget = !existingBudget.isPresent();
        // log.info("isNewBudget: {}", isNewBudget);

        // Find the target budget
        // Budget toBudget = existingBudget
        //     .orElseGet(() -> {
        //         // If no budget found, create a new one
        //         Budget newBudget = new Budget();

        //         Users user = userRepository.findById(transaction.getUserId())
        //             .orElseThrow(() -> new RuntimeException("User not found"));

        //         newBudget.setUser(user);
        //         newBudget.setCategory(toSubCategory.getCategory());
        //         newBudget.setSubCategory(toSubCategory);
        //         newBudget.setDate(transaction.getTransactionDate().toLocalDate().withDayOfMonth(1));
        //         newBudget.setAllocated(BigDecimal.ZERO);
        //         newBudget.setActual(BigDecimal.ZERO);
        //         newBudget.setRemaining(BigDecimal.ZERO);

        //         return budgetRepository.save(newBudget);
        //     });
        // log.info("Target Budget found: {}", toBudget);
        
        // Find the source budget
        Budget fromBudget = budgetRepository.findLatestBudgetByCategoryIdAndSubCategoryId(
            transaction.getUserId(),
            fromSubCategory.getCategory().getId(),
            fromSubCategory.getId(),
            transaction.getTransactionDate().toLocalDate())
            // If no budget found, return null
            .orElse(null);
        log.info("Source Budget found: {}", fromBudget);

        // Find target subcategory
        CustomSubCategory toCustomSubCategory;
        SubCategory toSubCategory;
        Budget toBudget;
        Boolean isNewBudget = false;
        if (transaction.getCustomSubCategoryId() != null) {
            toCustomSubCategory = customSubCategoryRepository.findByCustomSubCategoryId(transaction.getCustomSubCategoryId());
            // Flag to track if the budget is new or existing
            Optional<Budget> existingBudget = budgetRepository.findLatestBudgetByCategoryIdAndCustomSubCategoryId(
                transaction.getUserId(),
                toCustomSubCategory.getCategoryId(),
                toCustomSubCategory.getCustomSubCategoryId(),
                transaction.getTransactionDate().toLocalDate());

            isNewBudget = !existingBudget.isPresent();
            log.info("isNewBudget: {}", isNewBudget);

            // Find the budget
            toBudget = existingBudget
                // If no budget found, create a new one
                .orElseGet(() -> {
                    Budget newBudget = new Budget();
                    Users user = userRepository.findById(transaction.getUserId())
                        .orElseThrow(() -> new RuntimeException("User not found"));
                    Category category = categoryRepository.findById(toCustomSubCategory.getCategoryId())
                        .orElseThrow(() -> new RuntimeException("Category not found: " + toCustomSubCategory.getCategoryId()));
                    newBudget.setUser(user);
                    newBudget.setCategory(category);
                    newBudget.setCustomSubCategoryId(toCustomSubCategory.getCustomSubCategoryId());
                    newBudget.setDate(transaction.getTransactionDate().toLocalDate().withDayOfMonth(1));
                    // newBudget.setAllocated(BigDecimal.ZERO);
                    // rollover logic - check if previous month has a rollover budget with the same category and custom subcategory
                    Optional<Budget> previousMonthBudget = budgetRepository.findLatestBudgetByCategoryIdAndCustomSubCategoryId(
                        transaction.getUserId(),
                        toCustomSubCategory.getCategoryId(),
                        toCustomSubCategory.getCustomSubCategoryId(),
                        transaction.getTransactionDate().toLocalDate().minusMonths(1));
                    if (previousMonthBudget.isPresent()) {
                        newBudget.setAllocated(previousMonthBudget.get().getAllocated());
                        newBudget.setRollover(true);
                    } else {
                        newBudget.setAllocated(BigDecimal.ZERO);
                        newBudget.setRollover(false);
                    }
                    newBudget.setActual(BigDecimal.ZERO);
                    newBudget.setRemaining(BigDecimal.ZERO);
                    
                    return budgetRepository.save(newBudget);
                });
            log.info("Target Budget found: {}", toBudget);
            toSubCategory = null;
        } else {
            toSubCategory = subCategoryRepository.findById(transaction.getSubCategoryId())
                .orElseThrow(() -> new RuntimeException("SubCategory not found: " + transaction.getSubCategoryId()));
            // Flag to track if the budget is new or existing
            Optional<Budget> existingBudget = budgetRepository.findLatestBudgetByCategoryIdAndSubCategoryId(
                transaction.getUserId(),
                toSubCategory.getCategory().getId(),
                toSubCategory.getId(),
                transaction.getTransactionDate().toLocalDate());

            isNewBudget = !existingBudget.isPresent();
            log.info("isNewBudget: {}", isNewBudget);

            // Find the budget
            toBudget = existingBudget
                // If no budget found, create a new one
                .orElseGet(() -> {
                    Budget newBudget = new Budget();
                    Users user = userRepository.findById(transaction.getUserId())
                        .orElseThrow(() -> new RuntimeException("User not found"));
                    Category category = categoryRepository.findById(toSubCategory.getCategory().getId())
                        .orElseThrow(() -> new RuntimeException("Category not found: " + toSubCategory.getCategory().getId()));
                    newBudget.setUser(user);
                    newBudget.setCategory(category);
                    newBudget.setSubCategory(toSubCategory);
                    newBudget.setDate(transaction.getTransactionDate().toLocalDate().withDayOfMonth(1));
                    // newBudget.setAllocated(BigDecimal.ZERO);
                    // rollover logic - check if previous month has a rollover budget with the same category and subcategory
                    Optional<Budget> previousMonthBudget = budgetRepository.findLatestBudgetByCategoryIdAndSubCategoryId(
                        transaction.getUserId(),
                        toSubCategory.getCategory().getId(),
                        toSubCategory.getId(),
                        transaction.getTransactionDate().toLocalDate().minusMonths(1));
                    if (previousMonthBudget.isPresent()) {
                        newBudget.setAllocated(previousMonthBudget.get().getAllocated());
                        newBudget.setRollover(true);
                    } else {
                        newBudget.setAllocated(BigDecimal.ZERO);
                        newBudget.setRollover(false);
                    }
                    newBudget.setActual(BigDecimal.ZERO);
                    newBudget.setRemaining(BigDecimal.ZERO);
                    
                    return budgetRepository.save(newBudget);
                });
            log.info("Target Budget found: {}", toBudget);
            toCustomSubCategory = null;
        }
        
        BigDecimal transactionAmount = BigDecimal.valueOf(transaction.getTransactionAmount());
        BigDecimal adjustedAmount = transactionAmount;
        log.info("Transaction amount: {}", transactionAmount);
        
        // Calculate adjusted amount based on budget rules if any
        if (budgetRule != null) {
            // if (budgetRule.getThresholdAmount() != null && 
            //     transactionAmount.compareTo(budgetRule.getThresholdAmount()) <= 0) {
            //     return;
            // }
            
            // If max amount is set, cap the transaction at that amount
            if (budgetRule.getMaxTransferAmount() != null) {
                log.info("Max transfer amount: {}", budgetRule.getMaxTransferAmount());
                adjustedAmount = adjustedAmount.min(budgetRule.getMaxTransferAmount());
                log.info("Adjusted amount after max transfer: {}", adjustedAmount);
            }
            
            // If min amount is set, ensure transaction meets minimum
            if (budgetRule.getMinTransferAmount() != null) {
                log.info("Min transfer amount: {}", budgetRule.getMinTransferAmount());
                adjustedAmount = adjustedAmount.max(budgetRule.getMinTransferAmount());
                log.info("Adjusted amount after min transfer: {}", adjustedAmount);
            }

            // If max percentage is set, cap the transaction at that percentage of the original amount
            if (budgetRule.getMaxTransferPercent() != null) {
                log.info("Max transfer percent: {}", budgetRule.getMaxTransferPercent());
                BigDecimal maxAmount = transactionAmount
                    .multiply(budgetRule.getMaxTransferPercent())
                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                adjustedAmount = adjustedAmount.min(maxAmount);
                log.info("Adjusted amount after max transfer percent: {}", adjustedAmount);
            }

            // If min percentage is set, ensure transaction meets minimum
            if (budgetRule.getMinTransferPercent() != null) {
                log.info("Min transfer percent: {}", budgetRule.getMinTransferPercent());
                BigDecimal minAmount = transactionAmount
                    .multiply(budgetRule.getMinTransferPercent())
                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                adjustedAmount = adjustedAmount.max(minAmount);
                log.info("Adjusted amount after min transfer percent: {}", adjustedAmount);
            }
            log.info("Adjusted amount: {}", adjustedAmount);
        }
        
        // Update target budget with final adjusted amount and save
        toBudget.setActual(toBudget.getActual().add(adjustedAmount));
        toBudget.setRemaining(toBudget.getAllocated().subtract(toBudget.getActual()));
        log.info("Updated target budget: {}", toBudget);
        Budget savedToBudget = budgetRepository.save(toBudget);

        // Update source budget with final adjusted amount and save
        if (fromBudget != null) {
            fromBudget.setActual(fromBudget.getActual().subtract(adjustedAmount));
            fromBudget.setRemaining(fromBudget.getAllocated().subtract(fromBudget.getActual()));
            log.info("Updated source budget: {}", fromBudget);
            budgetRepository.save(fromBudget);
        }
        
        // Check notification rules for new budget
        if (isNewBudget) {
            log.info("Checking new budget notification rules for budget: {}", savedToBudget);
            checkNewBudgetNotificationRules(transaction, savedToBudget);
        }
        
        // Check notification rules
        // if (savedToBudget.getAllocated().compareTo(BigDecimal.ZERO) > 0) {
        //     log.info("Checking notification rules for budget: {}", savedToBudget);
        //     checkNotificationRules(transaction, savedToBudget, toSubCategory.getSubCategory());
        // }
        if (savedToBudget.getAllocated().compareTo(BigDecimal.ZERO) > 0) {
            log.info("Checking notification rules for budget: {}", savedToBudget);
            if (transaction.getCustomSubCategoryId() != null) {
                checkNotificationRules(transaction, savedToBudget, toCustomSubCategory.getCustomSubCategoryName());
            } else {
                checkNotificationRules(transaction, savedToBudget, toSubCategory.getSubCategory());
            }
        }

        /**
         * APT-123/124 fix for slow loading
         * Evict only the specific entry from the cache
         */
        int year = savedToBudget.getDate().getYear();
        int month = savedToBudget.getDate().getMonthValue();
        // String cacheKey = "getBudgetByUserAndYearAndMonth_" + transaction.getUserId() + "_" + year + "_" + month;
        // cacheManager.getCache("budgetCache").evict(cacheKey);
        clearBudgetRelatedCache(transaction.getUserId(), year, month);
        /** End of APT-123/124 fix */
    }

    public void checkNotificationRules(
        TransactionDto transaction,
        Budget savedBudget,
        String categoryName) {
        log.info("Inside checkNotificationRules in BudgetService");

        //***BEGIN NOTIFICATION RULE CHECK***//
        // Fetch the notification rule for "BUDGET_ALERT"
        BigDecimal percentageThreshold;
        String messageTemplate;
        String ruleType;
        NotificationRules.NotificationSeverity severity;

        if (!userNotificationRulesRepository.existsByUserIdAndRuleType(transaction.getUserId(), "BUDGET_ALERT")) {
            NotificationRules rule1 = notificationRuleRepository.findByRuleType("BUDGET_ALERT");
            if (rule1 == null || !rule1.getIsEnabled()) {
                log.info("Fetched master notification rule is inactive. Returning...");
                return;
            }
            log.info("Fetched master notification rule: {}", rule1);
            percentageThreshold = rule1.getPercentageThreshold();
            messageTemplate = rule1.getMessageTemplate();
            ruleType = rule1.getRuleType();
            severity = rule1.getSeverity();
        } else {
            UserNotificationRules rule2 = userNotificationRulesRepository.findByUserIdAndRuleType(transaction.getUserId(), "BUDGET_ALERT");
            if (rule2 == null || !rule2.getIsEnabled()) {
                log.info("Fetched user notification rule is inactive. Returning...");
                return; // Skip processing if rule is not enabled
            }
            log.info("Fetched user notification rule: {}", rule2);
            percentageThreshold = rule2.getPercentageThreshold();
            messageTemplate = rule2.getMessageTemplate();
            ruleType = rule2.getRuleType();
            severity = NotificationRules.NotificationSeverity.valueOf(rule2.getSeverity());
        }
        log.info("Percentage threshold: {}", percentageThreshold);
        log.info("Message template: {}", messageTemplate);
        log.info("Rule type: {}", ruleType);
        log.info("Severity: {}", severity);
        
        String monthName = transaction.getTransactionDate().toLocalDate().getMonth().name();
        monthName = monthName.substring(0, 1).toUpperCase() + monthName.substring(1).toLowerCase();
        
        // Check if the budget exceeds the rule's percentage threshold
        BigDecimal usagePercentage = savedBudget.getActual()
            .divide(savedBudget.getAllocated(), 2, RoundingMode.HALF_UP)
            .multiply(new BigDecimal("100"));
        log.info("Usage percentage: {}", usagePercentage);

        if (usagePercentage.compareTo(percentageThreshold) >= 0) {
            log.info("Budget exceeds threshold - creating notification");
            String message = messageTemplate
                .replace("{allocated}", savedBudget.getAllocated().toString())
                .replace("{category}", categoryName)
                .replace("{month}", monthName);
            
            // If notification not already sent, create and save it
            if (!notificationTrackingRepository.existsByUserIdAndRuleTypeAndMessage(
                    transaction.getUserId(), ruleType, message)) {
                log.info("Creating notification for user: {} with message: {}", transaction.getUserId(), message);
                notificationRuleService.createNotification(transaction.getUserId(),
                    message, severity, ruleType);
            } else {
                log.info("Notification already sent for user: {} with message: {}", transaction.getUserId(), message);
            }
        }
        //***END NOTIFICATION RULE CHECK***//
    }

    public void checkNewBudgetNotificationRules(
        TransactionDto transaction,
        Budget savedBudget) {
            log.info("Inside checkNewBudgetNotificationRules in BudgetService");
            log.info("Saved budget: {}", savedBudget);
            // Fetch the notification rule for "NEW_BUDGET_ALERT"
            String messageTemplate;
            String ruleType;
            NotificationRules.NotificationSeverity severity;

            if (!userNotificationRulesRepository.existsByUserIdAndRuleType(transaction.getUserId(), "NEW_BUDGET_ALERT")) {
                NotificationRules rule1 = notificationRuleRepository.findByRuleType("NEW_BUDGET_ALERT");
                if (rule1 == null || !rule1.getIsEnabled()) {
                    log.info("Fetched master notification rule is inactive. Returning...");
                    return;
                }
                log.info("Fetched master notification rule: {}", rule1);
                messageTemplate = rule1.getMessageTemplate();
                ruleType = rule1.getRuleType();
                severity = rule1.getSeverity();
            } else {
                UserNotificationRules rule2 = userNotificationRulesRepository.findByUserIdAndRuleType(transaction.getUserId(), "NEW_BUDGET_ALERT");
                if (rule2 == null || !rule2.getIsEnabled()) {
                    log.info("Fetched user notification rule is inactive. Returning...");
                    return;
                }
                log.info("Fetched user notification rule: {}", rule2);
                messageTemplate = rule2.getMessageTemplate();
                ruleType = rule2.getRuleType();
                severity = NotificationRules.NotificationSeverity.valueOf(rule2.getSeverity());
            }
            log.info("Message template: {}", messageTemplate);
            log.info("Rule type: {}", ruleType);
            log.info("Severity: {}", severity);
            
            String monthName = transaction.getTransactionDate().toLocalDate().getMonth().name();
            monthName = monthName.substring(0, 1).toUpperCase() + monthName.substring(1).toLowerCase();
            log.info("Month name: {}", monthName);
            
            String message;
            if (savedBudget.getCustomSubCategoryId() != null) {
                CustomSubCategory customSubCategory = customSubCategoryRepository.findByCustomSubCategoryId(savedBudget.getCustomSubCategoryId());
                message = messageTemplate
                .replace("{Month}", monthName)
                .replace("{Category}", savedBudget.getCategory().getCategory())
                .replace("{Subcategory}", customSubCategory.getCustomSubCategoryName());
            } else {
                message = messageTemplate
                .replace("{Month}", monthName)
                .replace("{Category}", savedBudget.getCategory().getCategory())
                .replace("{Subcategory}", savedBudget.getSubCategory().getSubCategory());
            }
            log.info("Notification message: {}", message);
            
            // If notification not already sent, create and save it
            if (!notificationTrackingRepository.existsByUserIdAndRuleTypeAndMessage(
                    transaction.getUserId(), ruleType, message)) {
                log.info("Creating notification for user: {} with message: {}", transaction.getUserId(), message);
                notificationRuleService.createNotification(transaction.getUserId(),
                    message, severity, ruleType);
            } else {
                log.info("Notification already sent for user: {} with message: {}", transaction.getUserId(), message);
            }
        }

    @Cacheable(value = "budgetCache", 
               key = "#root.methodName + '_' + #userId + '_' + #year + '_' + #month",
               unless = "#result == null")
    @Transactional(readOnly = true)
    public Map<String, Object> getBudgetSummaryByMonth(Integer userId, Integer year, Integer month) {
        log.info("getBudgetSummaryByMonth called with userId={}, year={}, month={}", userId, year, month);
        // Validate user exists
        if (!userRepository.existsById(userId)) {
            log.error("User not found");
            throw new RuntimeException("User not found");
        }

        // Get raw data from repository
        List<Object[]> results = budgetRepository.getBudgetSummaryByMonth(userId, year, month);
        log.info("Raw data size: {}", results.size());
        
        if (results == null || results.isEmpty() || results.get(0) == null) {
            log.warn("No data found for the requested month and year");
            return Collections.emptyMap();
        }

        Object[] row = results.get(0);
        
        Map<String, Object> summary = new HashMap<>();
        summary.put("actualIncome", row[0]);
        summary.put("totalBudget", row[1]);
        summary.put("actualBudget", row[2]);
        summary.put("remainingBudget", row[3]);
        log.info("Budget summary: {}", summary);
        
        return summary;
    }

    @Cacheable(value = "budgetCache", 
               key = "#root.methodName + '_' + #userId + '_' + #year + '_' + #month",
               unless = "#result == null")
    public SankeyChartDto getSankeyChartData(Integer userId, Integer year, Integer month) {
        log.info("getSankeyChartData called with userId={}, year={}, month={}", userId, year, month);
        if (!userRepository.existsById(userId)) {
            log.error("User not found");
            throw new RuntimeException("User not found");
        }

        // Check if year and month are not from future
        /**
         * APT-110 fix for 500 error
         * Fix - allow future dates - to account for rollover budgets
         */
        // LocalDate now = LocalDate.now();
        // LocalDate requestedDate = LocalDate.of(year, month, 1);
        // if (requestedDate.isAfter(now)) {
        //     log.error("Cannot retrieve data for future dates");
        //     throw new IllegalArgumentException("Cannot retrieve data for future dates");
        // }

        List<Object[]> rawData = budgetRepository.getSankeyData(userId, year, month);
        if (rawData == null || rawData.isEmpty()) {
            log.warn("No data found for the requested month and year");
            return new SankeyChartDto();
        }
        log.info("Raw data size: {}", rawData.size());
                
        // LinkedHashMap - doubly linked list! maintains insertion order!!
        Map<String, Integer> nodeIndices = new LinkedHashMap<>();
        List<SankeyChartDto.Node> nodes = new ArrayList<>();
        List<SankeyChartDto.Link> links = new ArrayList<>();
        Map<String, Integer> nodeLevels = new HashMap<>();

        // First pass: collect unique nodes
        for (Object[] row : rawData) {
            String source = (String) row[0];
            String target = (String) row[1];
            String category = (String) row[4];
            Integer level = ((Number) row[3]).intValue();

            // Add source node if not exists
            if (!nodeIndices.containsKey(source)) {
                SankeyChartDto.Node sourceNode = new SankeyChartDto.Node();
                sourceNode.setId(source);
                sourceNode.setName(source);
				sourceNode.setCategory(category);
                sourceNode.setLevel(level);
                nodes.add(sourceNode);
                nodeIndices.put(source, nodes.size() - 1);
            }

            // Add target node if not exists
            if (!nodeIndices.containsKey(target)) {
                SankeyChartDto.Node targetNode = new SankeyChartDto.Node();
                targetNode.setId(target);
                targetNode.setName(target);
				targetNode.setCategory(category);
                targetNode.setLevel(level);
                nodes.add(targetNode);
                nodeIndices.put(target, nodes.size() - 1);
            }

            // Add source node level if not exists
            if (!nodeLevels.containsKey(source)) {
                nodeLevels.put(source, level);
            }

            // Add target node level if not exists
            if (!nodeLevels.containsKey(target) && target != null) {
                nodeLevels.put(target, level + 1);
            }
        }

        // Second pass: create links between nodes
        for (Object[] row : rawData) {
            String source = (String) row[0];
            String target = (String) row[1];
            /** APT-110 fix for 500 error
             * If no proper data, return empty SankeyChartDto
             */
            log.info("row[2]: {}", row[2]);
            if (row[2] == null) {
                log.warn("Value is null for source: {}, target: {}", source, target);
                return new SankeyChartDto();
            }
            Double value = ((Number) row[2]).doubleValue();

            if (source != target) {
                SankeyChartDto.Link link = new SankeyChartDto.Link();
                link.setSource(source);
                link.setTarget(target);
                link.setValue(value);
                links.add(link);
            }
        }

        SankeyChartDto sankeyChartDto = new SankeyChartDto();
        sankeyChartDto.setNodes(nodes);
        sankeyChartDto.setLinks(links);
        sankeyChartDto.setNodeLevels(nodeLevels);
        log.info("Sankey chart data: {}", sankeyChartDto);

        return sankeyChartDto;
    }

    @Cacheable(value = "budgetCache", 
               key = "#root.methodName + '_' + #userId + '_' + #year + '_' + #month",
               unless = "#result == null")
    public SankeyChartDto getSankeyChartDataWithSubcategories(Integer userId, Integer year, Integer month) {
        log.info("getSankeyChartDataWithSubcategories called with userId={}, year={}, month={}", userId, year, month);
        if (!userRepository.existsById(userId)) {
            log.error("User not found");
            throw new RuntimeException("User not found");
        }

        // Check if year and month are not from future
        LocalDate now = LocalDate.now();
        LocalDate requestedDate = LocalDate.of(year, month, 1);
        if (requestedDate.isAfter(now)) {
            log.error("Cannot retrieve data for future dates");
            throw new IllegalArgumentException("Cannot retrieve data for future dates");
        }

        List<Object[]> rawData = budgetRepository.getSankeyDataWithSubcategories(userId, year, month);
        if (rawData == null || rawData.isEmpty()) {
            log.warn("No data found for the requested month and year");
            return new SankeyChartDto();
        }
        log.info("Raw data size: {}", rawData.size());
                
        // LinkedHashMap - doubly linked list! maintains insertion order!!
        Map<String, Integer> nodeIndices = new LinkedHashMap<>();
        List<SankeyChartDto.Node> nodes = new ArrayList<>();
        List<SankeyChartDto.Link> links = new ArrayList<>();
        Map<String, Integer> nodeLevels = new HashMap<>();

        // First pass: collect unique nodes
        for (Object[] row : rawData) {
            String source = (String) row[0];
            String target = (String) row[1];
            String category = (String) row[4];
            Integer level = ((Number) row[3]).intValue();

            // Add source node if not exists
            if (!nodeIndices.containsKey(source)) {
                SankeyChartDto.Node sourceNode = new SankeyChartDto.Node();
                sourceNode.setId(source);
                sourceNode.setCategory(category);
                nodes.add(sourceNode);
                nodeIndices.put(source, nodes.size() - 1);
            }

            // Add target node if not exists
            if (!nodeIndices.containsKey(target)) {
                SankeyChartDto.Node targetNode = new SankeyChartDto.Node();
                targetNode.setId(target);
                targetNode.setCategory(category);
                nodes.add(targetNode);
                nodeIndices.put(target, nodes.size() - 1);
            }

            // Add source node level if not exists
            if (!nodeLevels.containsKey(source)) {
                nodeLevels.put(source, level);
            }

            // Add target node level if not exists
            if (!nodeLevels.containsKey(target) && target != null) {
                nodeLevels.put(target, level + 1);
            }
        }

        // Second pass: create links between nodes
        for (Object[] row : rawData) {
            String source = (String) row[0];
            String target = (String) row[1];
            /** APT-110 fix for 500 error
             * If no proper data, return empty SankeyChartDto
             */
            if (row[2] == null) {
                log.warn("Value is null for source: {}, target: {}", source, target);
                return new SankeyChartDto();
            }
            Double value = ((Number) row[2]).doubleValue();

            if (source != target) {
                SankeyChartDto.Link link = new SankeyChartDto.Link();
                link.setSource(source);
                link.setTarget(target);
                link.setValue(value);
                links.add(link);
            }
        }

        SankeyChartDto sankeyChartDto = new SankeyChartDto();
        sankeyChartDto.setNodes(nodes);
        sankeyChartDto.setLinks(links);
        sankeyChartDto.setNodeLevels(nodeLevels);
        log.info("Sankey chart data: {}", sankeyChartDto);

        return sankeyChartDto;
    }

    @Cacheable(value = "budgetCache",
               key = "#root.methodName + '_' + #userId",
               unless = "#result == null || #result.isEmpty()")
    @Transactional(readOnly = true)
    public List<BudgetExpenseDto> getCurrentMonthBudgetExpenses(Integer userId) {
        // Validate user exists
        if (!userRepository.existsById(userId)) {
            throw new RuntimeException("User not found with ID: " + userId);
        }
        
        List<Object[]> results = budgetRepository.getCurrentMonthBudgetExpenses(userId);
        List<BudgetExpenseDto> dtoList = new ArrayList<>();
        
        for (Object[] row : results) {
            int i = 0;
            Integer categoryId = convertToInteger(row[i++]);
            String categoryName = (String) row[i++];
            Integer subcategoryId = convertToInteger(row[i++]);
            String subcategoryName = (String) row[i++];
            String iconKey = (String) row[i++];
            BigDecimal actualExpense = (BigDecimal) row[i++];
            
            BudgetExpenseDto dto = new BudgetExpenseDto(
                categoryId, categoryName, subcategoryId, subcategoryName, 
                iconKey, actualExpense
            );
            
            dtoList.add(dto);
        }
        
        return dtoList;
    }

    /**
     * Get the past 12 months' budget expenses for a user (excluding income)
     * @param userId The user ID
     * @return List of budget expenses for the past 12 months
     */
    @Cacheable(value = "budgetCache",
            key = "#root.methodName + '_' + #userId",
            unless = "#result == null || #result.isEmpty()")
    @Transactional(readOnly = true)
    public List<BudgetExpenseDto> getPast12MonthsExpenses(Integer userId) {
        // Validate user exists
        if (!userRepository.existsById(userId)) {
            throw new RuntimeException("User not found with ID: " + userId);
        }
        
        List<Object[]> results = budgetRepository.getPast12MonthsExpenses(userId);
        List<BudgetExpenseDto> dtoList = new ArrayList<>();
        
        for (Object[] row : results) {
            int i = 0;
            
            // Handle potential Long to Integer conversions properly
            Integer categoryId = convertToInteger(row[i++]);
            String categoryName = (String) row[i++];
            Integer subcategoryId = convertToInteger(row[i++]);
            String subcategoryName = (String) row[i++];
            String iconKey = (String) row[i++];
            Integer year = convertToInteger(row[i++]);
            Integer month = convertToInteger(row[i++]);
            BigDecimal actualExpense = (BigDecimal) row[i++];
            
            BudgetExpenseDto dto = new BudgetExpenseDto();
            dto.setCategoryId(categoryId);
            dto.setCategoryName(categoryName);
            dto.setSubcategoryId(subcategoryId);
            dto.setSubcategoryName(subcategoryName);
            dto.setIconKey(iconKey);
            dto.setYear(year);
            dto.setMonth(month);
            dto.setActualExpense(actualExpense);
            
            dtoList.add(dto);
        }
        
        return dtoList;
    }

    /**
     * Helper method to safely convert Number objects to Integer
     * Handles cases where database returns Long instead of Integer
     */
    private Integer convertToInteger(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return Integer.valueOf(value.toString());
    }

    public List<CategoryMonthlySummaryDto> getCategorySummary(
            Integer categoryId, Integer subCategoryId, Integer userId, int months) {
        if (categoryId == null || !categoryRepository.existsById(categoryId)) {
            throw new IllegalArgumentException("Invalid categoryId: " + categoryId);
        }
        if (userId == null || !userRepository.existsById(userId)) {
            throw new IllegalArgumentException("Invalid userId: " + userId);
        }
        if (subCategoryId != null && !subCategoryRepository.existsById(subCategoryId)) {
            throw new IllegalArgumentException("Invalid subCategoryId: " + subCategoryId);
        }

        LocalDate endDate = LocalDate.now().withDayOfMonth(1);
        LocalDate startDate = endDate.minusMonths(months - 1);

        List<CategoryMonthlySummaryDto> summary = transactionRepository.getCategoryMonthlySummary(
                categoryId, subCategoryId, userId, startDate, months
        );

        // Ensure all months are returned, filling missing months with 0
        List<CategoryMonthlySummaryDto> completeSummary = new ArrayList<>();
        for (int i = 0; i < months; i++) {
            LocalDate currentMonth = endDate.minusMonths(i);
            String monthStr = currentMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            CategoryMonthlySummaryDto dto = summary.stream()
                    .filter(s -> s.getMonth().equals(monthStr))
                    .findFirst()
                    .orElse(new CategoryMonthlySummaryDto(monthStr, 0.0));
            completeSummary.add(dto);
        }
        Collections.reverse(completeSummary); // Reverse to show oldest to newest
        return completeSummary;
    }

    @Cacheable(value = "budgetCache",
               key = "#methodName + '_' + #userId + '_' + #year + '_' + #month",
               unless = "#result == null || #result.isEmpty()")
    public List<BudgetUIDto> getFutureBudgetsByUser(Integer userId, Integer year, Integer month) {
        log.info("getFutureBudgetsByUser called with userId={}, year={}, month={}", userId, year, month);

        // --- Validate inputs ---
        if (!userRepository.existsById(userId)) {
            throw new RuntimeException("User not found with ID: " + userId);
        }

        if (year == null || month == null) {
            throw new IllegalArgumentException("Month and year must be provided");
        }

        YearMonth currentMonth = YearMonth.now();
        log.debug("Current month: {}", currentMonth);
        YearMonth targetMonth = YearMonth.of(year, month);
        log.debug("Target month: {}", targetMonth);

        if (targetMonth.isBefore(currentMonth)) {
            throw new IllegalArgumentException("Target month cannot be in the past");
        }

        LocalDate start = currentMonth.atDay(1);
        LocalDate end = targetMonth.atEndOfMonth();
        
        log.debug("Fetching budgets from {} to {}", start, end);

        List<Budget> rawBudgets = budgetRepository.findRolloverBudgetsInRange(userId, start, end);

        if (rawBudgets == null || rawBudgets.isEmpty()) {
            log.warn("No rollover budgets found in range for user {}", userId);
            return Collections.emptyList();
        }

        List<Budget> futureBudgets = generateBudgetsWithRollover(rawBudgets, userId, targetMonth);

        // Add target month's non-rollover budgets
        LocalDate nonRolloverDate = targetMonth.atDay(1);
        List<Budget> nonRolloverBudgets = budgetRepository.findNonRolloverBudgetsByUserAndMonth(userId, nonRolloverDate);
        log.info("Found {} non-rollover budgets for {}", nonRolloverBudgets.size(), nonRolloverDate);

        // Combine both rollover and non-rollover budgets;
        // If the target month has original budget, replace the rollover budget with it or add if not present
        Set<String> nonRolloverKeys = nonRolloverBudgets.stream()
            .map(this::generateCategoryKey)
            .collect(Collectors.toSet());

        // Remove rollover budgets that are overridden by non-rollover ones
        futureBudgets = futureBudgets.stream()
                .filter(budget -> !nonRolloverKeys.contains(generateCategoryKey(budget)))
                .collect(Collectors.toList());

        // Add non-rollover budgets
        futureBudgets.addAll(nonRolloverBudgets);

        if (futureBudgets.isEmpty()) {
            log.warn("No enriched (rollover) budgets available for user {}", userId);
            return Collections.emptyList();
        }
        log.info("Found {} future budgets for user {}", futureBudgets.size(), userId);

        /**
         * APT-123/124 fix for slow loading
         * Stream, map and collect custom subcategory info in one go
         */
        // Stream list of unique custom subcategory IDs
        Set<Integer> customSubCategoryIds = futureBudgets.stream()
                .filter(b -> b.getCustomSubCategoryId() != null)
                .map(Budget::getCustomSubCategoryId)
                .collect(Collectors.toSet());

        // Fetch custom subcategory names and create map of id to name
        Map<Integer, String> customSubCategoryNameMap =
            customSubCategoryRepository.findAllById(customSubCategoryIds)
                .stream()
                .collect(Collectors.toMap(CustomSubCategory::getCustomSubCategoryId, CustomSubCategory::getCustomSubCategoryName));
        /** End of APT-123/124 fix */

        return futureBudgets.stream()
                .map(b -> mapToBudgetUIDto(b, customSubCategoryNameMap))
                .collect(Collectors.toList());
    }

    public List<Budget> generateBudgetsWithRollover(
        List<Budget> budgetsFromDb, int userId, YearMonth targetMonth) {

        Map<YearMonth, Map<String, Budget>> monthCategoryMap = new HashMap<>();

        for (Budget b : budgetsFromDb) {
            log.debug("Processing budget: id={}, rollover={}", b.getId(), b.getRollover());
            if (!Boolean.TRUE.equals(b.getRollover())) continue;

            YearMonth ym = YearMonth.from(b.getDate());
            if (ym.isAfter(targetMonth)) continue;

            String key = generateCategoryKey(b);
            log.debug("ym={}, key={}", ym, key);

            monthCategoryMap
                    .computeIfAbsent(ym, k -> new HashMap<>())
                    .put(key, b);
        }
        
        if (monthCategoryMap.isEmpty()) {
            log.warn("No valid rollover budgets found for user {}", userId);
            return Collections.emptyList();
        }
        log.debug("Month-category map: {}", monthCategoryMap);

        Map<String, Budget> targetMonthBudgets = monthCategoryMap.getOrDefault(targetMonth, new HashMap<>());
        log.debug("Target month budgets: {}", targetMonthBudgets);

        Set<String> allCategoryKeys = getAllCategoryKeys(monthCategoryMap);
        log.debug("All category keys: {}", allCategoryKeys);

        List<Budget> result = new ArrayList<>();

        for (String key : allCategoryKeys) {
            if (targetMonthBudgets.containsKey(key)) {
                result.add(targetMonthBudgets.get(key));
            } else {
                Budget latestRollover = findLatestRollover(monthCategoryMap, key, targetMonth);
                if (latestRollover != null) {
                    result.add(cloneForFutureMonth(latestRollover, targetMonth));
                }
            }
        }

        return result;
    }

    private String generateCategoryKey(Budget b) {
        return b.getCategory().getId() + "-" +
                (b.getCustomSubCategoryId() != null
                        ? b.getCustomSubCategoryId()
                        : b.getSubCategory() != null
                            ? b.getSubCategory().getId()
                            : "none");
    }

    private Set<String> getAllCategoryKeys(Map<YearMonth, Map<String, Budget>> monthData) {
        return monthData.values().stream()
                .flatMap(m -> m.keySet().stream())
                .collect(Collectors.toSet());
    }

    private Budget findLatestRollover(Map<YearMonth, Map<String, Budget>> monthData,
                                    String key,
                                    YearMonth beforeMonth) {
        return monthData.entrySet().stream()
                .filter(entry -> entry.getKey().isBefore(beforeMonth))
                .sorted(Map.Entry.<YearMonth, Map<String, Budget>>comparingByKey().reversed())
                .map(entry -> entry.getValue().get(key))
                .filter(Objects::nonNull)
                .filter(b -> Boolean.TRUE.equals(b.getRollover()))
                .findFirst()
                .orElse(null);
    }

    private Budget cloneForFutureMonth(Budget source, YearMonth month) {
        Budget clone = new Budget();
        clone.setUser(source.getUser());
        clone.setCategory(source.getCategory());
        clone.setSubCategory(source.getSubCategory());
        clone.setCustomSubCategoryId(source.getCustomSubCategoryId());
        clone.setAllocated(source.getAllocated());
        clone.setActual(BigDecimal.ZERO);
        clone.setRemaining(source.getAllocated());
        clone.setIsDynamic(source.getIsDynamic());
        clone.setDynamicAllocated(source.getDynamicAllocated());
        clone.setDate(month.atDay(1));
        clone.setRollover(source.getRollover());
        clone.setExclude(source.getExclude());
        clone.setIcon(source.getIcon());
        return clone;
    }

    @Cacheable(value = "budgetCache",
               key = "#root.methodName + '_' + #userId + '_' + #year + '_' + #month",
               unless = "#result == null || #result.isEmpty()")
    public List<BudgetUIDto> getBudgetByUserAndYearAndMonth(Integer userId, Integer year, Integer month) {
        log.info("getBudgetByUserAndYearAndMonth called with userId={}, year={}, month={}", userId, year, month);

        // Validate user exists
        if (!userRepository.existsById(userId)) {
            throw new RuntimeException("User not found with ID: " + userId);
        }

        // Validate year and month
        if (year == null || month == null) {
            throw new IllegalArgumentException("Year and month must be provided");
        }

        // For future dates, call  getFutureBudgetsByUser
        YearMonth currentMonth = YearMonth.now();
        YearMonth targetMonth = YearMonth.of(year, month);
        if (targetMonth.isAfter(currentMonth)) {
            log.info("Target month is in the future. Calling getFutureBudgetsByUser...");
            return getFutureBudgetsByUser(userId, year, month);
        }

        // Get budgets for the specified year and month - either subcategoryid or customsubcategoryid should be present
        List<Budget> budgets = budgetRepository.findByUserWithMonthAndYearv1(userId, month, year);
        if (budgets.isEmpty()) {
            log.warn("No budgets found for user {} in year {} and month {}", userId, year, month);
            return Collections.emptyList();
        }
        log.info("Found {} budgets for user {} in year {} and month {}", budgets.size(), userId, year, month);

        /**
         * APT-123/124 fix for slow loading
         * Stream, map and collect custom subcategory info in one go
         */
        // Stream list of unique custom subcategory IDs
        Set<Integer> customSubCategoryIds = budgets.stream()
                .filter(b -> b.getCustomSubCategoryId() != null)
                .map(Budget::getCustomSubCategoryId)
                .collect(Collectors.toSet());
        log.info("Found {} unique custom subcategory IDs", customSubCategoryIds.size());

        // Fetch custom subcategory names and create map of id to name
        Map<Integer, String> customSubCategoryNameMap =
            customSubCategoryRepository.findAllById(customSubCategoryIds)
                .stream()
                .collect(Collectors.toMap(CustomSubCategory::getCustomSubCategoryId, CustomSubCategory::getCustomSubCategoryName));
        log.info("Custom subcategory name map: {}", customSubCategoryNameMap);
        /** End of APT-123/124 fix */

        // Map to BudgetUIDto and pass custom subcategory name map
        return budgets.stream()
                .map(b -> mapToBudgetUIDto(b, customSubCategoryNameMap))
                .collect(Collectors.toList());
    }

    private BudgetUIDto mapToBudgetUIDto(Budget budget, Map<Integer, String> customSubCategoryNameMap) {
        log.debug("Mapping budget to BudgetUIDto: {}", budget);
        BudgetUIDto dto = new BudgetUIDto();
        dto.setId(budget.getId());
        dto.setDate(budget.getDate());
        dto.setCategoryId(budget.getCategory().getId());
        dto.setCategoryName(budget.getCategory().getCategory());
        dto.setSubcategoryId(budget.getSubCategory() != null ? budget.getSubCategory().getId() : null);
        dto.setSubcategoryName(budget.getSubCategory() != null ? budget.getSubCategory().getSubCategory() : null);
        dto.setCustomSubCategoryId(budget.getCustomSubCategoryId());
        /** APT-123/124 fix for slow loading
         * Get custom subcategory name from map
         */
        // if (budget.getCustomSubCategoryId() != null) {
        //     Optional<CustomSubCategory> customSubCategory = customSubCategoryRepository.findById(budget.getCustomSubCategoryId());
        //     if (customSubCategory.isPresent()) {
        //         dto.setCustomSubCategoryName(customSubCategory.get().getCustomSubCategoryName());
        //     }
        // }
        dto.setCustomSubCategoryName(customSubCategoryNameMap.get(budget.getCustomSubCategoryId()));
        /** End of APT-123/124 fix */
        dto.setActual(budget.getActual());
        dto.setAllocated(budget.getAllocated());
        dto.setRemaining(budget.getRemaining());
        dto.setRollover(budget.getRollover());
        dto.setIcon(budget.getIcon());
        return dto;
    }

    public void clearBudgetRelatedCache(Integer userId, Integer year, Integer month) {
        String cacheKey = "getBudgetSummaryByMonth_" + userId + "_" + year + "_" + month;
        cacheManager.getCache("budgetCache").evict(cacheKey);

        cacheKey = "getSankeyChartData_" + userId + "_" + year + "_" + month;
        cacheManager.getCache("budgetCache").evict(cacheKey);

        cacheKey = "getSankeyChartDataWithSubcategories_" + userId + "_" + year + "_" + month;
        cacheManager.getCache("budgetCache").evict(cacheKey);

        cacheKey = "getCurrentMonthBudgetExpenses_" + userId;
        cacheManager.getCache("budgetCache").evict(cacheKey);

        cacheKey = "getPast12MonthsExpenses_" + userId;
        cacheManager.getCache("budgetCache").evict(cacheKey);

        cacheKey = "getFutureBudgetsByUser_" + userId + "_" + year + "_" + month;
        cacheManager.getCache("budgetCache").evict(cacheKey);

        cacheKey = "getBudgetByUserAndYearAndMonth_" + userId + "_" + year + "_" + month;
        cacheManager.getCache("budgetCache").evict(cacheKey);
    }
}