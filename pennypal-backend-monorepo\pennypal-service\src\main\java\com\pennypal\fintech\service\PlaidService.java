package com.pennypal.fintech.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.time.LocalDate;
import com.plaid.client.model.TransactionsGetRequest;
import com.plaid.client.model.TransactionsGetRequestOptions;
import com.plaid.client.model.TransactionsGetResponse;

import com.pennypal.fintech.util.PennyPalPlaidApi;
import com.pennypal.fintech.dto.AccountDto;
import com.pennypal.fintech.dto.TransactionDto;
import com.pennypal.fintech.entity.Accounts;
import com.pennypal.fintech.entity.CategoryMapping;
import com.pennypal.fintech.entity.Reconcile;
import com.pennypal.fintech.entity.SubCategory;
import com.pennypal.fintech.entity.Transactions;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.repository.AccountBalanceRepository;
import com.pennypal.fintech.repository.AccountRepository;
import com.pennypal.fintech.repository.CategoryMappingRepository;
import com.pennypal.fintech.repository.ReconcileRepository;
import com.pennypal.fintech.repository.SubCategoryRepository;
import com.pennypal.fintech.repository.TransactionRepository;
import com.pennypal.fintech.repository.UserRepository;

import com.plaid.client.model.AccountBase;
import com.plaid.client.model.AccountsBalanceGetRequest;
import com.plaid.client.model.AccountsGetResponse;
import com.plaid.client.model.ItemPublicTokenExchangeRequest;
import com.plaid.client.model.ItemPublicTokenExchangeResponse;
import com.plaid.client.model.Transaction;
import com.plaid.client.model.TransactionsSyncRequest;
import com.plaid.client.model.TransactionsSyncResponse;
import com.plaid.client.request.PlaidApi;

import retrofit2.Response;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import com.pennypal.fintech.entity.AccountBalance;
import com.pennypal.fintech.dto.TransactionEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
@Service
public class PlaidService {
    private static final Logger logger = LoggerFactory.getLogger(PlaidService.class);
    @Autowired
    private AccountRepository accountRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private TransactionRepository transactionRepository;
    @Autowired
    private AccountBalanceRepository accountBalanceRepository;
    
    @Autowired
    private ReconcileRepository reconcileRepository;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private CategoryMappingRepository categoryMappingRepository;

    @Autowired
    private SubCategoryRepository subCategoryRepository;

    @Autowired
    private FinicityServicev1 finicityServicev1;

    @Autowired
    private MxService mxService;
  
    @Autowired
    private PennyPalPlaidApi pennyPalPlaidApi;

    @Autowired
    private AccountBalanceService accountBalanceService;

    @Autowired
    private TransactionService transactionService;
    
    // The missing PlaidApi client
    @Autowired
    private PlaidApi plaidApiClient;
    
    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("MMM");

    @Value("${plaid.client_id}")
    private String clientId;

    @Value("${plaid.secret}")
    private String secret;

    private static final String PLAID_SANDBOX_URL = "https://sandbox.plaid.com";

    public String createLinkToken() {
        try {
            System.out.println("Creating Plaid link token with client ID: " + clientId);
            // Create a proper Link Token request using the Plaid Java library
            com.plaid.client.model.LinkTokenCreateRequest request = new com.plaid.client.model.LinkTokenCreateRequest()
                    .clientName("PennyPal")
                    .language("en")
                    .countryCodes(List.of(com.plaid.client.model.CountryCode.US))
                    .user(new com.plaid.client.model.LinkTokenCreateRequestUser().clientUserId("user-id-123"))
                    .products(List.of(
                            com.plaid.client.model.Products.AUTH,
                            com.plaid.client.model.Products.TRANSACTIONS));
 // Log before API call
 System.out.println("Sending request to Plaid API");
            // Execute the request using your PennyPalPlaidApi client
            com.plaid.client.model.LinkTokenCreateResponse response = pennyPalPlaidApi.client()
                    .linkTokenCreate(request)
                    .execute()
                    .body();
  // Log API response
  System.out.println("Received response from Plaid API: " + (response != null));
            if (response != null && response.getLinkToken() != null) {
                return response.getLinkToken();
            } else {
                throw new RuntimeException("Failed to get link token from Plaid API");
            }
        } catch (Exception e) {
             // Log the full exception for debugging
        System.err.println("Error creating link token: " + e.getMessage());
            // Log the full exception for debugging
            e.printStackTrace();
            throw new RuntimeException("Error creating link token: " + e.getMessage(), e);
        }
    }

    // Initializing PlaidClient in the constructor
    @Transactional
    public String exchangePublicToken(String publicToken, int userId) throws Exception {
        ItemPublicTokenExchangeRequest request = new ItemPublicTokenExchangeRequest()
                .publicToken(publicToken);

        ItemPublicTokenExchangeResponse response = pennyPalPlaidApi.client()
                .itemPublicTokenExchange(request)
                .execute()
                .body();

        if (response != null) {
            String accessToken = response.getAccessToken();
            System.out.println("Access Token: " + accessToken);

            List<Accounts> accounts = getAccountDetailsAndSave(accessToken, userId);

            return accessToken;
        }

        return null;
    }

    // @Transactional
    public List<Accounts> getAccountDetailsAndSave(String accessToken, int userId) throws Exception {
          logger.info("Starting to fetch and save accounts for user: {}", userId);
        AccountsBalanceGetRequest request = new AccountsBalanceGetRequest()
                .accessToken(accessToken);
logger.info("About to save accounts...");
        Response<AccountsGetResponse> response = pennyPalPlaidApi.client()
                .accountsBalanceGet(request)
                .execute();

        List<Accounts> savedAccounts = new ArrayList<>();
logger.info("Accounts saved successfully");
        // Extract account details from the response
        List<AccountBase> accounts = response.body().getAccounts();
  logger.info("Retrieved {} accounts from Plaid for user: {}", accounts.size(), userId);
        // Retrieve User entity
        Optional<Users> userOptional = userRepository.findById(userId);
        if (!userOptional.isPresent()) {
            throw new RuntimeException("User not found with ID: " + userId);
        }
        Users user = userOptional.get(); // Get the User object
   logger.info("Prepared {} accounts for saving to database", savedAccounts.size());
        // Iterate over the accounts and save them into the database
        for (AccountBase account : accounts) {
            Accounts newAccount = new Accounts();

            // Set the user for the account
            newAccount.setUser(user);

            // Set other account details from the Plaid response
            newAccount.setAccessToken(accessToken);
            newAccount.setAccountType(account.getType().toString()); // Account type
            newAccount.setAccountSubtype(account.getSubtype().toString()); // Account subtype
            newAccount.setItemId(response.body().getItem().getItemId()); // Item ID
            newAccount.setInstitutionId(response.body().getItem().getInstitutionId()); // Institution ID
            newAccount.setAccountName(account.getName()); // Account name
            newAccount.setPlaidUniqueNo(account.getAccountId());
            LocalDateTime now = LocalDateTime.now();
            newAccount.setInsertDatetime(now);
            newAccount.setUpdateDatetime(now);
            // newAccount.setFinancialInstName(response.body().getItem().getInstitutionName());
            newAccount.setBalance(account.getBalances().getAvailable());
            newAccount.setCurrencyType(account.getBalances().getIsoCurrencyCode());
            newAccount.setAccountMask(account.getMask());

            // Categorizing the accounts
            if (account.getType().toString().equals("depository")) {
                newAccount.setAccountCategory("Cash");
            } else if (account.getType().toString().equals("credit")) {
                newAccount.setAccountCategory("Credit Cards");
            } else if (account.getType().toString().equals("loan")) {
                newAccount.setAccountCategory("Loan");
            } else if (account.getType().toString().equals("investment")) {
                newAccount.setAccountCategory("Investment Accounts");
            }
            System.out.println("Saving account: " + newAccount.getAccountName());
            savedAccounts.add(newAccount);
        }

    // Save all accounts in the database first
    try {
        List<Accounts> persistedAccounts = accountRepository.saveAll(savedAccounts);
        accountRepository.flush();
        
        // *** LOAD 6 MONTHS HISTORICAL TRANSACTIONS FOR NEW ACCOUNTS ***
        System.out.println("Loading 6 months historical transactions for " + persistedAccounts.size() + " new accounts");
        loadHistoricalTransactionsForNewAccounts(persistedAccounts);

        // Clear account balance cache
        accountBalanceService.clearAccountBalanceCache(userId);

        // Clear transaction related cache
        transactionService.clearTransactionRelatedCache(userId);
        
        return persistedAccounts;
    } catch (Exception e) {
        e.printStackTrace();
        throw e;
    }
}

/**
 * Load 6 months of historical transactions for newly added accounts
 * This method is called immediately after account creation
 */

@Transactional
public void loadHistoricalTransactionsForNewAccounts(List<Accounts> newAccounts) {
    for (Accounts account : newAccounts) {
        try {
            System.out.println("Loading 6 months historical transactions for new account ID: " + account.getId());
            
            String accessToken = account.getAccessToken();
            if (accessToken == null || accessToken.isEmpty()) {
                System.out.println("Access token not found for account ID: " + account.getId());
                continue;
            }
            
            // Calculate date range (last 2 months)
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusDays(30);;
            
            System.out.println("Fetching historical transactions from " + startDate + " to " + endDate + 
                              " for account ID: " + account.getId());
            
            List<TransactionDto> newTransactionDtos = new ArrayList<>();
            int totalTransactionsProcessed = 0;
            int offset = 0;
            final int batchSize = 500; // Plaid's maximum
            boolean hasMoreTransactions = true;
            
            // Generate sync ID for this historical load
            String syncId = generateSyncId(account.getId()) + "-HISTORICAL";
            account.setSyncId(syncId);
            
            while (hasMoreTransactions) {
                System.out.println("Fetching batch starting at offset: " + offset + " for account ID: " + account.getId());
                
                TransactionsGetRequest request = new TransactionsGetRequest()
                    .accessToken(accessToken)
                    .startDate(startDate)
                    .endDate(endDate)
                    .options(new TransactionsGetRequestOptions()
                        .offset(offset)  // Fixed: Use actual offset value
                        .count(batchSize)
                    );
                                
                // Fixed: Use pennyPalPlaidApi instead of plaidApiClient
                Response<TransactionsGetResponse> response = pennyPalPlaidApi.client()
                    .transactionsGet(request)
                    .execute();
                
                if (!response.isSuccessful()) {
                    System.out.println("Failed Plaid API response for account ID: " + account.getId() + 
                                     ", HTTP code: " + response.code() + 
                                     ", message: " + response.message());
                    if (response.errorBody() != null) {
                        try {
                            System.out.println("Error body: " + response.errorBody().string());
                        } catch (Exception e) {
                            System.out.println("Could not read error body: " + e.getMessage());
                        }
                    }
                    break;
                }
                
                TransactionsGetResponse transactionsResponse = response.body();
                if (transactionsResponse == null) {
                    System.out.println("Null response body from Plaid API for account ID: " + account.getId());
                    break;
                }
                
                List<Transaction> transactions = transactionsResponse.getTransactions();
                
                if (transactions == null || transactions.isEmpty()) {
                    System.out.println("No more transactions available for account ID: " + account.getId());
                    hasMoreTransactions = false;
                    break;
                }
                
                // Filter transactions for this specific account only
                List<Transaction> accountTransactions = transactions.stream()
                    .filter(tx -> tx.getAccountId().equals(account.getPlaidUniqueNo()))
                    .collect(Collectors.toList());
                
                System.out.println("Processing batch of " + accountTransactions.size() + 
                                 " transactions for account ID: " + account.getId() + " (total fetched: " + transactions.size() + ")");
                
                if (accountTransactions.isEmpty()) {
                    System.out.println("No transactions for this account in current batch, continuing...");
                    offset += batchSize;
                    
                    // Check if we should continue based on total available
                    int totalAvailable = transactionsResponse.getTotalTransactions();
                    hasMoreTransactions = (offset < totalAvailable);
                    continue;
                }
                
                // Process and save transactions
                List<TransactionDto> batchTransactionDtos = processAndSaveHistoricalTransactions(
                    accountTransactions, account);
                newTransactionDtos.addAll(batchTransactionDtos);
                totalTransactionsProcessed += batchTransactionDtos.size();
                
                // Update offset for next batch
                offset += batchSize;
                
                // Check if we have more transactions
                int totalAvailable = transactionsResponse.getTotalTransactions();
                hasMoreTransactions = (offset < totalAvailable);
                
                System.out.println("Processed " + totalTransactionsProcessed + " transactions so far. " +
                                 "Offset: " + offset + " of " + totalAvailable + 
                                 " total available for account ID: " + account.getId());
                
                // Safety check to prevent infinite loops
                if (offset > 10000) { // Reasonable upper limit
                    System.out.println("Safety limit reached (10000 transactions), stopping for account ID: " + account.getId());
                    break;
                }
            }
            
            // Update account with sync information
            account.setLastSyncTime(LocalDateTime.now());
            accountRepository.save(account);
            
            // Update account balance
            updateAccountBalanceWithHistory(account, syncId);
            
            // Perform reconciliation for the new transactions
            if (totalTransactionsProcessed > 0) {
                reconcileTransactionsByAccountId(account.getId());
            }
            
            // Publish event for new transactions
            if (!newTransactionDtos.isEmpty()) {
                System.out.println("Publishing transaction event with " + newTransactionDtos.size() + 
                                 " historical transactions for account ID: " + account.getId());
                eventPublisher.publishEvent(new TransactionEvent(this, newTransactionDtos));
            }
            
            System.out.println("Successfully loaded " + totalTransactionsProcessed + 
                             " historical transactions for account ID: " + account.getId());
            
        } catch (Exception e) {
            System.out.println("Error loading historical transactions for account ID: " + account.getId() + 
                             " - Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}


/**
 * Process and save historical transactions, return DTOs for event publishing
 */
private List<TransactionDto> processAndSaveHistoricalTransactions(
        List<Transaction> plaidTransactions, 
        Accounts account) {
    
    if (plaidTransactions == null || plaidTransactions.isEmpty()) {
        return new ArrayList<>();
    }
    
    Users user = account.getUser();
    List<Transactions> transactionList = new ArrayList<>();
    List<TransactionDto> transactionDtos = new ArrayList<>();
    
    for (Transaction plaidTx : plaidTransactions) {
        // Check if transaction already exists to avoid duplicates
        Optional<Transactions> existingTransaction = transactionRepository
            .findByTransactionId(plaidTx.getTransactionId());
        
        if (existingTransaction.isPresent()) {
            System.out.println("Transaction already exists, skipping: " + plaidTx.getTransactionId());
            continue;
        }
        
        Transactions transaction = new Transactions();
        transaction.setInsertDateTime(LocalDateTime.now());
        transaction.setUpdateDateTime(LocalDateTime.now());
        
        // Map Plaid transaction to entity
        transaction.setTransactionId(plaidTx.getTransactionId());
        transaction.setTransactionDate(plaidTx.getDate().atStartOfDay());
        transaction.setDescription(plaidTx.getName());
        transaction.setTransactionAmount(plaidTx.getAmount());
        transaction.setCategory(plaidTx.getCategory() != null && !plaidTx.getCategory().isEmpty() 
            ? String.join(", ", plaidTx.getCategory()) 
            : null);
        transaction.setMerchantName(plaidTx.getMerchantName());
        transaction.setTransactionType(plaidTx.getPaymentChannel().getValue());
        transaction.setAccount(account);
        transaction.setUser(user);
        
        transactionList.add(transaction);
        
        // Create DTO for event publishing
        TransactionDto dto = new TransactionDto();
        dto.setTransactionId(transaction.getTransactionId());
        dto.setTransactionDate(transaction.getTransactionDate());
        dto.setDescription(transaction.getDescription());
        dto.setTransactionAmount(transaction.getTransactionAmount());
        dto.setCategory(transaction.getCategory());
        dto.setMerchantName(transaction.getMerchantName());
        dto.setAccountId(account.getId());
        dto.setUserId(user.getId());
        
        transactionDtos.add(dto);
    }
    
    // Save all transactions in batches
    if (!transactionList.isEmpty()) {
        System.out.println("Saving " + transactionList.size() + " historical transactions for account ID: " + 
                         account.getId());
        transactionRepository.saveAll(transactionList);
        transactionRepository.flush();
    }
    
    return transactionDtos;
}

// Keep your existing syncTransactionsForAccount method as is - it will use transaction sync API



/**
 * Fallback method to load initial transactions using Transactions Get API
 * when Sync API fails to initialize
 */
private int loadInitialTransactionsWithGetAPI(Accounts account, String syncId) {
    try {
        System.out.println("Loading initial transactions using Get API for account ID: " + account.getId());
        
        // Get last 30 days of transactions to establish baseline
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(30);
        
        TransactionsGetRequest request = new TransactionsGetRequest()
            .accessToken(account.getAccessToken())
            .startDate(startDate)
            .endDate(endDate)
            .options(new TransactionsGetRequestOptions()
                .offset(0)
                .count(500)
            );
        
        Response<TransactionsGetResponse> response = plaidApiClient.transactionsGet(request)
            .execute();
        
        if (!response.isSuccessful() || response.body() == null) {
            System.out.println("Failed to load initial transactions for account ID: " + account.getId());
            return 0;
        }
        
        TransactionsGetResponse transactionsResponse = response.body();
        List<Transaction> transactions = transactionsResponse.getTransactions().stream()
            .filter(tx -> tx.getAccountId().equals(account.getPlaidUniqueNo()))
            .collect(Collectors.toList());
        
        List<TransactionDto> transactionDtos = processAndSaveHistoricalTransactions(transactions, account);
        
        // After loading initial transactions, try to initialize sync cursor again
        try {
            TransactionsSyncRequest syncRequest = new TransactionsSyncRequest()
                .accessToken(account.getAccessToken());
            
            Response<TransactionsSyncResponse> syncResponse = plaidApiClient.transactionsSync(syncRequest)
                .execute();
            
            if (syncResponse.isSuccessful() && syncResponse.body() != null) {
                String cursor = syncResponse.body().getNextCursor();
                account.setTransactionCursor(cursor);
                account.setLastSyncTime(LocalDateTime.now());
                accountRepository.save(account);
                System.out.println("Successfully initialized cursor after fallback load: " + cursor);
            }
        } catch (Exception e) {
            System.out.println("Could not initialize cursor after fallback load: " + e.getMessage());
        }
        
        // Publish events if transactions were loaded
        if (!transactionDtos.isEmpty()) {
            eventPublisher.publishEvent(new TransactionEvent(this, transactionDtos));
        }
        
        return transactionDtos.size();
        
    } catch (Exception e) {
        System.out.println("Error in fallback transaction loading for account ID: " + account.getId());
        e.printStackTrace();
        return 0;
    }
}
public List<AccountDto> getAccountsByUserId(int userId) {
        // Retrieve the accounts associated with the user
        List<Accounts> accounts = accountRepository.findByUser_Id(userId);

        if (accounts.isEmpty()) {
            System.out.println("No accounts found for user ID: " + userId);
        }

        // Map the Account entities to AccountDto objects
        List<AccountDto> accountDtos = accounts.stream()
                .map(this::mapToAccountDto)
                .collect(Collectors.toList());

        return accountDtos;
    }

    private AccountDto mapToAccountDto(Accounts account) {
        AccountDto dto = new AccountDto();
        dto.setId(account.getId());
        dto.setAccountName(account.getAccountName());
        dto.setAccountType(account.getAccountType());
        dto.setAccountCategory(account.getAccountCategory());
        dto.setLastSyncTime(account.getLastSyncTime());
        // Ensure balance is not null before accessing it
        Double balance = account.getBalance();
        if (balance == null) {
            balance = 0.0; // Set default value if balance is null
        }
        dto.setBalance(balance);
        dto.setAccountMask(account.getAccountMask());
        dto.setAccountSubtype(account.getAccountSubtype());
        dto.setInstitutionId(account.getInstitutionId());
        dto.setCurrencyType(account.getCurrencyType());
        dto.setAccountMask(account.getAccountMask());
        dto.setInsertDatetime(account.getInsertDatetime());
        dto.setUpdateDatetime(account.getUpdateDatetime());

        return dto;
    }

    public List<Accounts> getAccountsByInstitutionId(String institutionId) {
        // Query the AccountRepository to find all accounts with the given institutionId
        List<Accounts> accounts = accountRepository.findByInstitutionId(institutionId);

        if (accounts.isEmpty()) {
            System.out.println("No accounts found for institution ID: " + institutionId);
        }

        return accounts;
    }
  
 @Transactional
public int syncTransactionsForAccount(int accountId) {
    System.out.println("Starting transaction sync for account ID: " + accountId);
    
    Optional<Accounts> accountOpt = accountRepository.findById(accountId);
    if (accountOpt.isEmpty()) {
        System.out.println("Account not found with ID: " + accountId);
        throw new RuntimeException("Account not found with ID: " + accountId);
    }
    
    Accounts account = accountOpt.get();
    
    // Finicity
        if (account.getAuthPartner() != null && account.getAuthPartner().equals("finicity")) {
            logger.info("Syncing Finicity account ID: {}", accountId);
            try {
                finicityServicev1.getCustomerAccounts(account.getUser().getId());
            } catch (Exception e) {
                logger.error("Error syncing Finicity accounts for user ID: {}", account.getUser().getId(), e);
            }
            try {
                return finicityServicev1.syncTransactionsForAccount(accountId);
            } catch (Exception e) {
                logger.error("Error syncing Finicity transactions for account ID: {}", accountId, e);
                return 0;
            }
        }
if (account.getAuthPartner() != null && account.getAuthPartner().equals("mx")) {
        logger.info("Syncing MX account ID: {}", accountId);
        try {
            // Use the new single account sync method instead of syncing all accounts
            mxService.syncSingleAccount(accountId);
            return mxService.syncTransactionsForAccount(accountId);
        } catch (Exception e) {
            logger.error("Error syncing MX account ID: {}", accountId, e);
            return 0;
        }
    }
   
    // // MX
    // if (account.getAuthPartner() != null && account.getAuthPartner().equals("mx")) {
    //     logger.info("Syncing MX account ID: {}", accountId);
    //     try {
    //         mxService.getAccounts(account.getUser().getId());
    //     } catch (Exception e) {
    //         logger.error("Error syncing MX accounts for user ID: {}", account.getUser().getId(), e);
    //     }
    //     try {
    //         return mxService.syncTransactionsForAccount(accountId);
    //     } catch (Exception e) {
    //         logger.error("Error syncing MX transactions for account ID: {}", accountId, e);
    //         return 0;
    //     }
    // }
       
    String cursor = account.getTransactionCursor();
    String accessToken = account.getAccessToken();
    
    System.out.println("Retrieved cursor from account " + accountId + ": " + cursor);
    
    if (accessToken == null || accessToken.isEmpty()) {
        System.out.println("Access token not found for account ID: " + accountId);
        throw new RuntimeException("Access token not found for account ID: " + accountId);
    }
      String syncId = generateSyncId(account.getId()); // ✅ Declare syncId here
    account.setSyncId(syncId);
     int totalTransactionsProcessed = 0;
      List<TransactionDto> newTransactionDtos = new ArrayList<>();
    try {
       
        boolean hasMore = true;
        
        // String syncId = generateSyncId(account.getId());
        // account.setSyncId(syncId);
        // List<TransactionDto> newTransactionDtos = new ArrayList<>();
        
        // **FIX: Handle null/empty cursor properly**
        if (cursor == null || cursor.trim().isEmpty()) {
            System.out.println("No cursor available for account ID " + accountId + ". Initializing with empty sync to get initial cursor.");
            
          TransactionsSyncRequest initRequest = new TransactionsSyncRequest()
    .accessToken(accessToken);
            // Don't set cursor for initial request - let Plaid handle it
            
            Response<TransactionsSyncResponse> initResponse = plaidApiClient.transactionsSync(initRequest)
                .execute();
            
            if (!initResponse.isSuccessful() || initResponse.body() == null) {
                System.out.println("Failed to initialize cursor for account ID: " + accountId);
                System.out.println("Response Code: " + initResponse.code());
                System.out.println("Response Message: " + initResponse.message());
                if (initResponse.errorBody() != null) {
                    try {
                        System.out.println("Error Body: " + initResponse.errorBody().string());
                    } catch (Exception e) {
                        System.out.println("Could not read error body: " + e.getMessage());
                    }
                }
                
                // **FALLBACK: Use Transactions Get API for historical data**
                System.out.println("Falling back to historical transaction load for account ID: " + accountId);
                return loadInitialTransactionsWithGetAPI(account, syncId);
            }
            
            TransactionsSyncResponse initSyncResponse = initResponse.body();
            cursor = initSyncResponse.getNextCursor();
            
            // Process initial transactions if any
            List<TransactionDto> initialTransactions = processTransactionsAndReturnDtos(
                initSyncResponse.getAdded(), account, true);
            newTransactionDtos.addAll(initialTransactions);
            totalTransactionsProcessed += initialTransactions.size();
            
            // Process modified transactions
            List<TransactionDto> modifiedTransactions = processTransactionsAndReturnDtos(
                initSyncResponse.getModified(), account, false);
            newTransactionDtos.addAll(modifiedTransactions);
            totalTransactionsProcessed += modifiedTransactions.size();
            
            // Update cursor in account
            account.setTransactionCursor(cursor);
            account.setLastSyncTime(LocalDateTime.now());
            accountRepository.save(account);
            
            System.out.println("Initialized cursor for account " + accountId + ": " + cursor);
            
            hasMore = initSyncResponse.getHasMore();
        }
        
        // Continue with normal sync process if there are more transactions
        while (hasMore) {
            // Ensure cursor is not null or empty before using it
            if (cursor == null || cursor.trim().isEmpty()) {
                System.out.println("Cursor is null/empty during sync loop for account ID: " + accountId);
                break;
            }
            
            TransactionsSyncRequest request = new TransactionsSyncRequest()
                .accessToken(accessToken)
                .cursor(cursor);
            
            System.out.println("Using cursor for account ID " + accountId + ": " + cursor);
            
            Response<TransactionsSyncResponse> response = plaidApiClient.transactionsSync(request)
                .execute();
            
            if (!response.isSuccessful() || response.body() == null) {
                System.out.println("Plaid API Error Details:");
                System.out.println("Response Code: " + response.code());
                System.out.println("Response Message: " + response.message());
                if (response.errorBody() != null) {
                    try {
                        System.out.println("Error Body: " + response.errorBody().string());
                    } catch (Exception e) {
                        System.out.println("Could not read error body: " + e.getMessage());
                    }
                }
                break;
            }
            
            TransactionsSyncResponse syncResponse = response.body();
            String nextCursor = syncResponse.getNextCursor();
            hasMore = syncResponse.getHasMore();
            
            System.out.println("Received nextCursor from Plaid for account " + accountId + ": " + nextCursor);
            System.out.println("Plaid sync response for account " + accountId + " => has_more: " + hasMore);

            List<TransactionDto> batchNewTransactions = processTransactionsAndReturnDtos(
                syncResponse.getAdded(), account, true);
            newTransactionDtos.addAll(batchNewTransactions);
            totalTransactionsProcessed += batchNewTransactions.size();
            
            List<TransactionDto> batchModifiedTransactions = processTransactionsAndReturnDtos(
                syncResponse.getModified(), account, false);
            newTransactionDtos.addAll(batchModifiedTransactions);
            totalTransactionsProcessed += batchModifiedTransactions.size();
            
            cursor = nextCursor;
            account.setTransactionCursor(cursor);
            account.setLastSyncTime(LocalDateTime.now());
            
            System.out.println("Saving updated cursor for account " + accountId + ": " + cursor);
            accountRepository.save(account);
            System.out.println("Successfully saved account " + accountId + " with cursor: " + cursor);
        }
        
        // updateAccountBalanceWithHistory(account, syncId);
        
        if (!newTransactionDtos.isEmpty()) {
            System.out.println("Publishing transaction event with " + newTransactionDtos.size() + " transactions for account ID: " + accountId);
            eventPublisher.publishEvent(new TransactionEvent(this, newTransactionDtos));
        }
        
        System.out.println("Completed transaction sync for account ID: " + accountId + ", processed " + totalTransactionsProcessed + " transactions with syncId: " + syncId);
        return totalTransactionsProcessed;
        
    } catch (Exception e) {
        System.out.println("Error syncing transactions for account ID: " + accountId);
        e.printStackTrace();
        throw new RuntimeException("Failed to sync transactions for account ID: " + accountId, e);
    }
     finally {
        // ✅ Always refresh balance, even if sync fails
        updateAccountBalanceWithHistory(account, syncId);
    }
}

                 
      /**
     * Process a list of transactions and return DTOs
     * @param plaidTransactions Transactions from Plaid API
     * @param account The account entity
     * @param isNewTransaction Whether these are new transactions
     * @return List of transaction DTOs processed
     */
    private List<TransactionDto> processTransactionsAndReturnDtos(
            List<Transaction> plaidTransactions, 
            Accounts account, 
            boolean isNewTransaction) {
        
        if (plaidTransactions == null || plaidTransactions.isEmpty()) {
            return new ArrayList<>();
        }
        
        Users user = account.getUser();
        List<Transactions> transactionList = new ArrayList<>();
        List<TransactionDto> transactionDtos = new ArrayList<>();
        
        for (Transaction plaidTx : plaidTransactions) {
            // Skip transactions not belonging to this account
            if (!plaidTx.getAccountId().equals(account.getPlaidUniqueNo())) {
                continue;
            }
            
            Transactions transaction;
            
            if (!isNewTransaction) {
                // Try to find existing transaction by transaction ID
                Optional<Transactions> existingTransaction = transactionRepository
                    .findByTransactionId(plaidTx.getTransactionId());
                
                if (existingTransaction.isPresent()) {
                    transaction = existingTransaction.get();
                    transaction.setUpdateDateTime(LocalDateTime.now());
                } else {
                    // If not found, treat as new
                    transaction = new Transactions();
                    transaction.setInsertDateTime(LocalDateTime.now());
                    transaction.setUpdateDateTime(LocalDateTime.now());
                }
            } else {
                transaction = new Transactions();
                transaction.setInsertDateTime(LocalDateTime.now());
                transaction.setUpdateDateTime(LocalDateTime.now());
            }
            
            // Map Plaid transaction to entity
            transaction.setTransactionId(plaidTx.getTransactionId());
            transaction.setTransactionDate(plaidTx.getDate().atStartOfDay());
            transaction.setDescription(plaidTx.getName());
            transaction.setTransactionAmount(plaidTx.getAmount());
            // transaction.setCategory(plaidTx.getCategory() != null && !plaidTx.getCategory().isEmpty() 
            //     ? String.join(", ", plaidTx.getCategory()) 
            //     : null);
            String detailedPfc = plaidTx.getPersonalFinanceCategory() != null ? 
                plaidTx.getPersonalFinanceCategory().getDetailed() : null;
            String category = "";
            Integer subCategoryId = 0;
            if (detailedPfc != null) {
                Optional<CategoryMapping> categoryMapping = 
                    categoryMappingRepository.findByDetailedPfc(detailedPfc);
                if (categoryMapping.isPresent()) {
                    transaction.setCategory(categoryMapping.get().getDetailedPfcClean());
                    category = categoryMapping.get().getDetailedPfcClean();
                    Optional<SubCategory> subCategory = 
                        subCategoryRepository.findBySubCategory(categoryMapping.get().getDetailedPfcClean());
                    if (subCategory.isPresent()) {
                        transaction.setSubCategoryId(subCategory.get().getId());
                        subCategoryId = subCategory.get().getId();
                    }
                } else {
                    transaction.setCategory(detailedPfc);
                    category = detailedPfc;
                }
            } else {
                transaction.setCategory("Uncategorized");
                category = "Uncategorized";
            }
            transaction.setMerchantName(plaidTx.getMerchantName());
            transaction.setTransactionType(plaidTx.getPaymentChannel().getValue());
            transaction.setAccount(account);
            transaction.setUser(user);
            
            transactionList.add(transaction);
            
            // Create DTO for event publishing
            TransactionDto dto = new TransactionDto();
            dto.setTransactionId(transaction.getTransactionId());
            dto.setTransactionDate(transaction.getTransactionDate());
            dto.setDescription(transaction.getDescription());
            dto.setTransactionAmount(transaction.getTransactionAmount());
            // dto.setCategory(transaction.getCategory());
            dto.setCategory(category);
            dto.setSubCategoryId(subCategoryId);
            dto.setMerchantName(transaction.getMerchantName());
            // dto.setTransactionType(transaction.getTransactionType());
            dto.setAccountId(account.getId());
            dto.setUserId(user.getId());
            // Add any other fields needed for the DTO
            
            transactionDtos.add(dto);
        }
        
        // Save all transactions
        if (!transactionList.isEmpty()) {
            transactionRepository.saveAll(transactionList);
        }
        
        return transactionDtos;
    }
    
    /**
     * Generate a unique sync ID
     * Format: accountId-timestamp
     */
    private String generateSyncId(int accountId) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        return accountId + "-" + timestamp;
    }
    /**
 * Update account balance and store balance history
 * @param account The account entity
 * @param syncId The sync operation ID
 */
@Transactional    
private void updateAccountBalanceWithHistory(Accounts account, String syncId) {
    try {
        logger.info("Updating balance for account ID: {} with syncId: {}", account.getId(), syncId);
        
        // Call Plaid Balance API to get FRESH balance data
        AccountsBalanceGetRequest request = new AccountsBalanceGetRequest()
            .accessToken(account.getAccessToken());
        
        Response<AccountsGetResponse> response = plaidApiClient.accountsBalanceGet(request)
            .execute();
            
        if (!response.isSuccessful() || response.body() == null) {
            logger.error("Failed to fetch balance for account ID: {}, HTTP code: {}", 
                account.getId(), response.code());
            return;
        }
        
        AccountsGetResponse balanceResponse = response.body();
        LocalDateTime now = LocalDateTime.now();
        boolean balanceUpdated = false;
        
      for (AccountBase plaidAccount : balanceResponse.getAccounts()) {
    // Removed account ID check to always process the first account
    Double available = plaidAccount.getBalances().getAvailable();
    Double current = plaidAccount.getBalances().getCurrent();
    String currencyCode = plaidAccount.getBalances().getIsoCurrencyCode();

    // Prefer available balance, fallback to current
    Double newBalance = (available != null) ? available : current;

    if (newBalance != null) {
        logger.info("Fetched fresh balance from Plaid for account ID: {} - Available: {}, Current: {}, Using: {}", 
            account.getId(), available, current, newBalance);

        AccountBalance accountBalance = new AccountBalance();
        accountBalance.setAccount(account);
        accountBalance.setBalance(newBalance);
        accountBalance.setCurrencyCode(currencyCode != null ? currencyCode : "USD");
        accountBalance.setTimestamp(now);
        accountBalance.setUser(account.getUser());

        accountBalanceRepository.save(accountBalance);
        logger.info("✓ Saved balance history for account ID: {} with balance: {} {}", 
            account.getId(), newBalance, currencyCode);

        // Update the main account table
        Double previousBalance = account.getBalance();
        account.setBalance(newBalance);
        account.setCurrencyType(currencyCode != null ? currencyCode : "USD");
        account.setLastSyncTime(now);
        account.setSyncId(syncId);

        accountRepository.save(account);
        balanceUpdated = true;

        logger.info("✓ Updated main account balance for ID: {} from {} to {} {} with syncId: {}", 
            account.getId(), previousBalance, newBalance, currencyCode, syncId);
    } else {
        logger.warn("No balance data available from Plaid for account ID: {}", account.getId());
    }

    break; // Only process the first account
}

        if (!balanceUpdated) {
            logger.warn("Could not find matching account in Plaid response for account ID: {} with Plaid account ID: {}", 
                account.getId(), account.getPlaidUniqueNo());
        }
        
    } catch (Exception e) {
        logger.error("Error updating account balance for account ID: {} - Error: {}", 
            account.getId(), e.getMessage(), e);
    }
}



/**
 * Process removed transactions
 * @param removedTransactions Transactions removed in Plaid
 * @param account The account entity
 */
private void processRemovedTransactions(List<com.plaid.client.model.RemovedTransaction> removedTransactions, Accounts account) {
    if (removedTransactions == null || removedTransactions.isEmpty()) {
        return;
    }
    
    for (com.plaid.client.model.RemovedTransaction removedTx : removedTransactions) {
        // Find the transaction in our database
        Optional<Transactions> existingTransaction = transactionRepository
            .findByTransactionId(removedTx.getTransactionId());
            
        if (existingTransaction.isPresent()) {
            Transactions transaction = existingTransaction.get();
            // You have two options:
            // 1. Mark as removed with a flag (recommended)
            transaction.setRemoved(true);
            transaction.setUpdateDateTime(LocalDateTime.now());
            transactionRepository.save(transaction);
            
            logger.info("Marked transaction as removed: {}", removedTx.getTransactionId());
            
            // 2. Or actually delete it (uncomment if you prefer this approach)
            // transactionRepository.delete(transaction);
            // logger.info("Deleted removed transaction: {}", removedTx.getTransactionId());
        }
    }
}
    /**
     * Process a list of transactions
     * @param plaidTransactions Transactions from Plaid API
     * @param account The account entity
     * @param isNewTransaction Whether these are new transactions
     * @return Number of transactions processed
     */
 /**
 * Process a list of transactions
 * @param plaidTransactions Transactions from Plaid API
 * @param account The account entity
 * @param isNewTransaction Whether these are new transactions
 * @return Number of transactions processed
 */
private int processTransactions(List<Transaction> plaidTransactions, Accounts account, boolean isNewTransaction) {
    if (plaidTransactions == null || plaidTransactions.isEmpty()) {
        return 0;
    }
    
    Users user = account.getUser();
    List<Transactions> transactionList = new ArrayList<>();
    
    for (Transaction plaidTx : plaidTransactions) {
        // Skip transactions not belonging to this account
        if (!plaidTx.getAccountId().equals(account.getPlaidUniqueNo())) {
            continue;
        }
        
        Transactions transaction;
        
        if (!isNewTransaction) {
            // Try to find existing transaction by transaction ID
            Optional<Transactions> existingTransaction = transactionRepository
                .findByTransactionId(plaidTx.getTransactionId());
            
            if (existingTransaction.isPresent()) {
                transaction = existingTransaction.get();
                transaction.setUpdateDateTime(LocalDateTime.now());
            } else {
                // If not found, treat as new
                transaction = new Transactions();
                transaction.setInsertDateTime(LocalDateTime.now());
                transaction.setUpdateDateTime(LocalDateTime.now());
            }
        } else {
            transaction = new Transactions();
            transaction.setInsertDateTime(LocalDateTime.now());
            transaction.setUpdateDateTime(LocalDateTime.now());
        }
        
        // Map Plaid transaction to entity
        transaction.setTransactionId(plaidTx.getTransactionId());
        transaction.setTransactionDate(plaidTx.getDate().atStartOfDay());
        transaction.setDescription(plaidTx.getName());
        transaction.setTransactionAmount(plaidTx.getAmount());
        // transaction.setCategory(plaidTx.getCategory() != null && !plaidTx.getCategory().isEmpty() 
        //     ? String.join(", ", plaidTx.getCategory()) 
        //     : null);
        String detailedPfc = plaidTx.getPersonalFinanceCategory() != null ? 
            plaidTx.getPersonalFinanceCategory().getDetailed() : null;
        if (detailedPfc != null) {
            Optional<CategoryMapping> categoryMapping = 
                categoryMappingRepository.findByDetailedPfc(detailedPfc);
            if (categoryMapping.isPresent()) {
                transaction.setCategory(categoryMapping.get().getDetailedPfcClean());
                Optional<SubCategory> subCategory = 
                    subCategoryRepository.findBySubCategory(categoryMapping.get().getDetailedPfcClean());
                if (subCategory.isPresent()) {
                    transaction.setSubCategoryId(subCategory.get().getId());
                }
            } else {
                transaction.setCategory(detailedPfc);
            }
        } else {
            transaction.setCategory("Uncategorized");
        }
        transaction.setMerchantName(plaidTx.getMerchantName());
        transaction.setTransactionType(plaidTx.getPaymentChannel().getValue());
        transaction.setAccount(account);
        transaction.setUser(user);
        
        transactionList.add(transaction);
    }
    
    // Save all transactions
    if (!transactionList.isEmpty()) {
        transactionRepository.saveAll(transactionList);
        reconcileMatchingTransactions(transactionList);

    }
    
    return transactionList.size();
}

private void reconcileMatchingTransactions(List<Transactions> transactions) {
    // Create a list of unmatched transactions (not already reconciled, not removed)
    List<Transactions> unmatched = transactions.stream()
    .filter(tx -> tx.getRemoved() == null || !tx.getRemoved())
    .filter(tx -> tx.getReconcileFlag() == null || !"yes".equalsIgnoreCase(tx.getReconcileFlag().trim()))        
    .collect(Collectors.toList());

    for (int i = 0; i < unmatched.size(); i++) {
        Transactions t1 = unmatched.get(i);
        for (int j = i + 1; j < unmatched.size(); j++) {
            Transactions t2 = unmatched.get(j);

            // Check if the descriptions match and the amounts are opposite
            boolean isDescriptionMatch = t1.getDescription().trim().equalsIgnoreCase(t2.getDescription().trim());
            boolean isOppositeAmount = Math.abs(t1.getTransactionAmount() + t2.getTransactionAmount()) < 0.01;

            if (isDescriptionMatch && isOppositeAmount) {
                // Mark both as reconciled
                t1.setReconcileFlag("yes");
                t2.setReconcileFlag("yes");
                t1.setHideFromBudget(true);
                t2.setHideFromBudget(true);
                // Create Reconcile record
                // Create a common reconcile ID for both
                    String reconcileId = UUID.randomUUID().toString();
                    t1.setReconcileId(reconcileId);
                    t2.setReconcileId(reconcileId);
                    
                    // Save updated Transactions with reconcileId
                    transactionRepository.save(t1);
                    transactionRepository.save(t2);
                    // Reconcile entry for t1
                    Reconcile reconcile1 = new Reconcile();
                    reconcile1.setTransactionDate(t1.getTransactionDate());
                    reconcile1.setDescription(t1.getDescription().trim());
                    reconcile1.setCategory(t1.getCategory());
                    reconcile1.setAmount(t1.getTransactionAmount()); // keep actual sign
                    reconcile1.setReconcileId(reconcileId);
                    reconcile1.setReconcileFlag("yes");
                    reconcile1.setReconcileId(reconcileId);
                    reconcile1.setAccountName(t1.getAccount().getAccountName());
                    reconcileRepository.save(reconcile1);

                    // Reconcile entry for t2
                    Reconcile reconcile2 = new Reconcile();
                    reconcile2.setTransactionDate(t2.getTransactionDate());
                    reconcile2.setDescription(t2.getDescription().trim());
                    reconcile2.setCategory(t2.getCategory());
                    reconcile2.setAmount(t2.getTransactionAmount()); // keep actual sign
                    reconcile2.setReconcileId(reconcileId);
                    reconcile2.setReconcileFlag("yes");
                    reconcile2.setReconcileId(reconcileId);
                    reconcile2.setAccountName(t2.getAccount().getAccountName());
                    reconcileRepository.save(reconcile2);

                    break; // move to next outer transaction
                }
            }
        }
    }

public void reconcileTransactionsByAccountId(int accountId) {
    List<Transactions> transactions = transactionRepository.findByAccount_Id(accountId);
    reconcileMatchingTransactions(transactions);
}
    /**
     * Update account balance based on latest information
     * @param account The account to update
     */
    private void updateAccountBalance(Accounts account) {
        try {
            // In a real application, you might want to fetch the balance directly from Plaid
            // For this example, we're assuming balance is available from another source
            // or using the latest transactions for calculation
            
            // You would typically call the Plaid Balance API here
            // For example:
            /*
            AccountsBalanceGetRequest request = new AccountsBalanceGetRequest()
                .accessToken(account.getAccessToken());
            
            AccountsBalanceGetResponse response = plaidApiClient.accountsBalanceGet(request)
                .execute()
                .body();
                
            for (com.plaid.client.model.Account plaidAccount : response.getAccounts()) {
                if (plaidAccount.getAccountId().equals(account.getPlaidUniqueNo())) {
                    account.setBalance(plaidAccount.getBalances().getCurrent());
                    break;
                }
            }
            */
            
            // For now, we'll just update the updateDatetime
            account.setLastSyncTime(LocalDateTime.now());
            accountRepository.save(account);
            
        } catch (Exception e) {
            logger.error("Error updating account balance for account ID: {}", account.getId(), e);
        }
    }
    
/**
 * Asynchronously sync transactions for all accounts of a user
 * @param userId User ID
 * @return The number of accounts processed
 */
@Async
public CompletableFuture<Integer> asyncSyncAllAccountsForUser(int userId) {
    logger.info("Starting async sync for all accounts of user ID: {}", userId);
    List<Accounts> accounts = accountRepository.findByUser_Id(userId);
    int successCount = 0;
    
    for (Accounts account : accounts) {
        try {
            // int transactionsProcessed = syncTransactionsForAccount(account.getId());
            // logger.info("Synced {} transactions for account ID: {}", transactionsProcessed, account.getId());
            // successCount++;
            if (account.getAuthPartner() == null || 
                (!account.getAuthPartner().equals("finicity") && 
                 !account.getAuthPartner().equals("mx"))) {
                int transactionsProcessed = syncTransactionsForAccount(account.getId());
                logger.info("Synced {} transactions for account ID: {}", transactionsProcessed, account.getId());
                successCount++;
            }
        } catch (Exception e) {
            logger.error("✗ Error syncing account ID: {} for user ID: {} - Error: {}", 
                account.getId(), userId, e.getMessage(), e);
        }
    }
    // Get no. of Finicity accounts
    Integer numOfFinicityAccounts = accountRepository.findByUserIdAndAuthPartner(userId, "finicity").size();
    logger.info("Number of Finicity accounts: {}", numOfFinicityAccounts);
    if (numOfFinicityAccounts > 0) {
        // Sync Finicity accounts
        try {
            finicityServicev1.getCustomerAccounts(userId);
        } catch (Exception e) {
            logger.error("Error syncing Finicity accounts for user ID: {}", userId, e);
        }

        // Sync Finicty transactions
        try {
            finicityServicev1.syncTransactions(userId);
        } catch (Exception e) {
            logger.error("Error syncing Finicity transactions for user ID: {}", userId, e);
        }
    }

    // Get no. of MX accounts
    Integer numOfMxAccounts = accountRepository.findByUserIdAndAuthPartner(userId, "mx").size();
    logger.info("Number of MX accounts: {}", numOfMxAccounts);
    if (numOfMxAccounts > 0) {
        // Sync MX accounts
        try {
            mxService.getAccounts(userId);
        } catch (Exception e) {
            logger.error("Error syncing MX accounts for user ID: {}", userId, e);
        }

        // Sync MX transactions
        try {
            mxService.getTransactions(userId);
        } catch (Exception e) {
            logger.error("Error syncing MX transactions for user ID: {}", userId, e);
        }
    }
    
    logger.info("Completed async sync for all accounts of user ID: {}, successfully synced {}/{} accounts", 
        userId, successCount, accounts.size());
    return CompletableFuture.completedFuture(successCount);
}

}