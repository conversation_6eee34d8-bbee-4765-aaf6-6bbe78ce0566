package com.pennypal.fintech.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import com.pennypal.fintech.dto.BudgetDto;
import com.pennypal.fintech.dto.BudgetExpenseDto;
import com.pennypal.fintech.dto.BudgetUIDto;
import com.pennypal.fintech.dto.SankeyChartDto;
import com.pennypal.fintech.entity.Budget;
import com.pennypal.fintech.repository.CategoryRepository;
import com.pennypal.fintech.repository.SubCategoryRepository;
import com.pennypal.fintech.service.BudgetService;
import com.pennypal.fintech.entity.CustomSubCategory;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

@RestController
@Slf4j
@RequestMapping("/api/v1/budget")
@Tag(name = "Budget Management", description = "APIs for managing budgets")
public class BudgetController {
    
    @Autowired
    private final BudgetService budgetService;
    @Autowired
    private CategoryRepository categoryRepository;
    @Autowired
    private SubCategoryRepository subCategoryRepository;

    public BudgetController(BudgetService budgetService) {
        this.budgetService = budgetService;
    }

    @PostMapping("/add")
    @Operation(summary = "Create a new budget", description = "Creates a new budget entry")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Budget created successfully", content = @Content(mediaType = "application/json")),
        @ApiResponse(responseCode = "400", description = "Invalid input data", content = @Content(mediaType = "text/plain")),
        @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content(mediaType = "text/plain"))
    })
    public ResponseEntity<?> createBudget(
            @Parameter(description = "Budget data to create", required = true)
            @RequestBody BudgetDto budgetDto) {
        try {
            log.info("Inside createBudget method");
            log.info("Received BudgetDto : " + budgetDto);

            // Basic validation
            if (budgetDto == null) {
                return ResponseEntity.badRequest().body("Budget data cannot be null");
            }
            
            if (budgetDto.getUserId() == null) {
                return ResponseEntity.badRequest().body("User ID is required");
            }
            
            if (budgetDto.getCategoryId() == null) {
                return ResponseEntity.badRequest().body("Category ID is required");
            }
            
            // Validate subcategory selection logic
            boolean hasDefaultSubcategory = budgetDto.getSubcategoryId() != null;
            boolean hasCustomSubcategoryName = budgetDto.getCustomSubCategoryName() != null && 
                                            !budgetDto.getCustomSubCategoryName().trim().isEmpty();
            boolean hasCustomSubcategoryId = budgetDto.getCustomSubCategoryId() != null;
            
            // Count how many subcategory options are provided
            int selectionCount = (hasDefaultSubcategory ? 1 : 0) + 
                            (hasCustomSubcategoryName ? 1 : 0) + 
                            (hasCustomSubcategoryId ? 1 : 0);
            
            // At least one subcategory option must be provided
            if (selectionCount == 0) {
                return ResponseEntity.badRequest().body(
                    "Either subcategory ID, custom subcategory name, or custom subcategory ID must be provided");
            }
            
            // IMPORTANT: Allow multiple options but warn about priority
            if (selectionCount > 1) {
                log.warn("Multiple subcategory options provided. Priority: subCategoryId > customSubCategoryName > customSubCategoryId");
                
                // Log what was provided for debugging
                if (hasDefaultSubcategory) {
                    log.info("subCategoryId provided: {}", budgetDto.getSubcategoryId());
                }
                if (hasCustomSubcategoryName) {
                    log.info("customSubCategoryName provided: {}", budgetDto.getCustomSubCategoryName());
                }
                if (hasCustomSubcategoryId) {
                    log.info("customSubCategoryId provided: {}", budgetDto.getCustomSubCategoryId());
                }
            }
            
            // Additional validations
            if (budgetDto.getAllocated() != null && budgetDto.getAllocated().compareTo(BigDecimal.ZERO) < 0) {
                return ResponseEntity.badRequest().body("Allocated amount cannot be negative");
            }
            
            // Validate custom subcategory name length if provided
            if (hasCustomSubcategoryName && budgetDto.getCustomSubCategoryName().length() > 255) {
                return ResponseEntity.badRequest().body("Custom subcategory name cannot exceed 255 characters");
            }
            
            // Validate icon key length if provided
            if (budgetDto.getIconKey() != null && budgetDto.getIconKey().length() > 255) {
                return ResponseEntity.badRequest().body("Icon key cannot exceed 255 characters");
            }
            
            // Create the budget
            BudgetDto savedBudget = budgetService.createBudget(budgetDto);
            
            // Log the result for debugging
            log.info("Budget created successfully: ID={}, SubCategoryId={}, CustomSubCategoryId={}", 
                    savedBudget.getId(), savedBudget.getSubcategoryId(), savedBudget.getCustomSubCategoryId());
            
            return ResponseEntity.ok(savedBudget);

        } catch (IllegalArgumentException e) {
            log.error("Invalid input for budget creation: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body("Invalid input: " + e.getMessage());
        } catch (RuntimeException e) {
            log.error("Runtime error during budget creation: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body("Error creating budget: " + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error during budget creation: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body("Error creating budget: " + e.getMessage());
        }
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "Update an existing budget", description = "Updates budget details by ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Budget updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "404", description = "Budget not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> updateBudget(
        @Parameter(description = "ID of the budget to update", required = true)
        @PathVariable Integer id,
        @Parameter(description = "Updated budget data", required = true)
        @RequestBody BudgetDto budgetDto) {
        try {
            log.info("Received update request for Budget ID: " + id + " with data: " + budgetDto);
            if (id == null) {
                return ResponseEntity.badRequest().body("Invalid budget ID: " + id);
            }
            if (budgetDto == null) {
                return ResponseEntity.badRequest().body("Budget data cannot be null");
            }

            // Create new budget when id = 0
            if (id == 0) {
                BudgetDto newBudget = budgetService.createBudget(budgetDto);
                return ResponseEntity.ok(newBudget);
            }

            budgetDto.setId(id);
            Budget updatedBudget = budgetService.updateBudget(budgetDto);
            BudgetDto responseDto = budgetService.mapToDto(updatedBudget);
            return ResponseEntity.ok(responseDto);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body("Invalid input: " + e.getMessage());
        } catch (RuntimeException e) {
            if (e.getMessage().contains("not found")) {
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.badRequest().body("Error updating budget: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("Error updating budget with ID: " + id + " - " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Internal server error: " + e.getMessage());
        }
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "Get budgets by user", description = "Retrieves all budgets for a specific user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Budgets retrieved successfully", content = @Content(mediaType = "application/json")),
        @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content(mediaType = "text/plain"))
    })
    public ResponseEntity<List<BudgetDto>> getBudgetsByUser(
        @Parameter(description = "ID of the user to get budgets for", required = true)
        @PathVariable Integer userId) {
        try {
            List<BudgetDto> budgetDtos = budgetService.getBudgetsByUser(userId, null);
            return ResponseEntity.ok(budgetDtos);
        } catch (Exception e) {
            System.err.println("Error fetching budgets for user: " + userId + " - " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
      
    }

    @GetMapping("/user/{userId}/month/{month}")
    @Operation(summary = "Get budgets by user and month", description = "Retrieves budgets for a specific user and month")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Budgets retrieved successfully", content = @Content(mediaType = "application/json")),
        @ApiResponse(responseCode = "400", description = "Invalid month parameter", content = @Content(mediaType = "text/plain")),
        @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content(mediaType = "text/plain"))
    })
    public ResponseEntity<?> getBudgetsByUserAndMonth(
            @Parameter(description = "ID of the user", required = true)
            @PathVariable Integer userId,
            @Parameter(description = "Month number (1-12)", required = true)
            @PathVariable @Min(1) @Max(12) Integer month) {
        try {
            if (month < 1 || month > 12) {
                return ResponseEntity.badRequest().body("Month must be between 1 and 12");
            }
            List<BudgetDto> budgetDtos = budgetService.getBudgetsByUser(userId, month);
            return ResponseEntity.ok(budgetDtos);
        } catch (Exception e) {
            System.err.println("Error fetching budgets for user: " + userId + ", month: " + month + " - " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error fetching budgets: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get budget by ID", description = "Retrieves a specific budget by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Budget retrieved successfully", content = @Content(mediaType = "application/json")),
        @ApiResponse(responseCode = "404", description = "Budget not found", content = @Content(mediaType = "text/plain")),
        @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content(mediaType = "text/plain"))
        })
    public ResponseEntity<?> getBudgetById(
        @Parameter(description = "ID of the budget", required = true)
        @PathVariable Integer id) {
        try {
            BudgetDto budgetDto = budgetService.getBudgetWithIconKey(id);
            if (budgetDto == null) {
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.ok(budgetDto);
        } catch (Exception e) {
            System.err.println("Error fetching budget with ID: " + id + " - " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error fetching budget: " + e.getMessage());
        }
    }
    
    @GetMapping("/category/{categoryId}")
    @Operation(summary = "Get budgets by category", description = "Retrieves all budgets for a specific category")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Budgets retrieved successfully", content = @Content(mediaType = "application/json")),
        @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content(mediaType = "text/plain"))
    })
    public ResponseEntity<List<Budget>> getBudgetsByCategory(
        @Parameter(description = "ID of the category", required = true)
        @PathVariable Integer categoryId) {
        try {
            List<Budget> budgets = budgetService.getBudgetsByCategory(categoryId);
            return ResponseEntity.ok(budgets);
        } catch (Exception e) {
            System.err.println("Error fetching budgets for category: " + categoryId + " - " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete budget", description = "Deletes a budget by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Budget deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Budget not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> deleteBudget(
        @Parameter(description = "ID of the budget to delete", required = true)
        @PathVariable Integer id) {
        try {
            budgetService.deleteBudget(id);
            return ResponseEntity.ok().build();
        } catch (RuntimeException e) {
            if (e.getMessage().contains("not found")) {
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.badRequest().body("Error deleting budget: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("Error deleting budget with ID: " + id + " - " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error deleting budget: " + e.getMessage());
        }
    }

    // Custom subcategory management endpoints
    @GetMapping("/custom-subcategories/user/{userId}")
    @Operation(summary = "Get custom subcategories by user", description = "Retrieves all custom subcategories for a specific user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Custom subcategories retrieved successfully", content = @Content(mediaType = "application/json")),
        @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content(mediaType = "text/plain"))
    })
    public ResponseEntity<List<CustomSubCategory>> getCustomSubCategoriesByUser(
        @Parameter(description = "ID of the user to get custom subcategories for", required = true)
        @PathVariable Integer userId) {
        try {
            List<CustomSubCategory> customSubCategories = budgetService.getCustomSubCategoriesByUser(userId);
            return ResponseEntity.ok(customSubCategories);
        } catch (Exception e) {
            System.err.println("Error fetching custom subcategories for user: " + userId + " - " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @DeleteMapping("/custom-subcategories/{customSubCategoryId}/user/{userId}")
    @Operation(summary = "Delete custom subcategory", description = "Deletes a custom subcategory for a specific user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Custom subcategory deleted successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "404", description = "Custom subcategory not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> deleteCustomSubCategory(
            @PathVariable Integer customSubCategoryId,
            @PathVariable Integer userId) {
        try {
            budgetService.deleteCustomSubCategory(customSubCategoryId, userId);
            return ResponseEntity.ok().build();
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body("Error deleting custom subcategory: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("Error deleting custom subcategory: " + customSubCategoryId + " for user: " + userId + " - " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error deleting custom subcategory: " + e.getMessage());
        }
    }

    @Operation(
        summary = "Get monthly budget summary",
        description = "Retrieves a summary of income, budget, actual spending, and remaining amounts for a specific month"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved budget summary"),
        @ApiResponse(responseCode = "400", description = "Invalid parameters provided"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/summary_by_month/{userId}/{year}/{month}")
    public ResponseEntity<?> getBudgetSummaryByMonth(
            @Parameter(description = "User ID", required = true)
            @PathVariable Integer userId,
            
            @Parameter(description = "Year", required = true)
            @PathVariable Integer year,
            
            @Parameter(description = "Month (1-12)", required = true)
            @PathVariable @Min(1) @Max(12) Integer month) {
        
        try {
            // Validate year
            if (userId == null) {
                return ResponseEntity.badRequest().body("User ID is required.");
            }
            if (year == null) {
                return ResponseEntity.badRequest().body("Year is required.");
            }
            if (month == null) {
                return ResponseEntity.badRequest().body("Month is required.");
            }
            if (month < 1 || month > 12) {
                return ResponseEntity.badRequest().body("Month must be between 1 and 12.");
            }

            Map<String, Object> summary = budgetService.getBudgetSummaryByMonth(userId, year, month);
            
            if (summary == null || summary.isEmpty()) {
                return ResponseEntity.noContent().build();
            }
            
            return ResponseEntity.ok(summary);
            
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error retrieving budget summary: " + e.getMessage());
        }
    }

    @Operation(
        summary = "Get Sankey chart data",
        description = "Retrieves data for a Sankey chart representing budget allocations and actual spending for a specific month"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved Sankey chart data"),
        @ApiResponse(responseCode = "400", description = "Invalid parameters provided"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/sankey-chart")
    public ResponseEntity<?> getSankeyChartData(
            @Parameter(description = "User ID", required = true)
            @RequestParam Integer userId,
            @Parameter(description = "Year", required = true)
            @RequestParam Integer year,
            @Parameter(description = "Month (1-12)", required = true)
            @RequestParam Integer month) {
        try {
            if (userId == null) {
                return ResponseEntity.badRequest().body("User ID is required.");
            }
            if (year == null) {
                return ResponseEntity.badRequest().body("Year is required.");
            }
            if (month == null) {
                return ResponseEntity.badRequest().body("Month is required.");
            }
            if (month < 1 || month > 12) {
                return ResponseEntity.badRequest().body("Month must be between 1 and 12.");
            }

            SankeyChartDto chartData = budgetService.getSankeyChartData(userId, year, month);
            
            log.info("Sankey chart data: " + chartData);
            /**
             * APT-110 fix for 500 error
             */
            // if (chartData.getNodes().isEmpty()) {
            //     return ResponseEntity.noContent().build();
            // }
            
            return ResponseEntity.ok(chartData);
            
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error retrieving Sankey chart data: " + e.getMessage());
        }
    }

    @Operation(
        summary = "Get Sankey chart data",
        description = "Retrieves data for a Sankey chart representing budget allocations and actual spending for a specific month"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved Sankey chart data"),
        @ApiResponse(responseCode = "400", description = "Invalid parameters provided"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/sankey-chart/with-subcategories")
    public ResponseEntity<?> getSankeyChartDataWithSubcategories(
            @Parameter(description = "User ID", required = true)
            @RequestParam Integer userId,
            @Parameter(description = "Year", required = true)
            @RequestParam Integer year,
            @Parameter(description = "Month (1-12)", required = true)
            @RequestParam Integer month) {
        try {
            if (userId == null) {
                return ResponseEntity.badRequest().body("User ID is required.");
            }
            if (year == null) {
                return ResponseEntity.badRequest().body("Year is required.");
            }
            if (month == null) {
                return ResponseEntity.badRequest().body("Month is required.");
            }
            if (month < 1 || month > 12) {
                return ResponseEntity.badRequest().body("Month must be between 1 and 12.");
            }

            SankeyChartDto chartData = budgetService.getSankeyChartDataWithSubcategories(userId, year, month);
            
            log.info("Sankey chart data: " + chartData);
            /**
             * APT-110 fix for 500 error
             */
            // if (chartData.getNodes().isEmpty()) {
            //     return ResponseEntity.noContent().build();
            // }
            
            return ResponseEntity.ok(chartData);
            
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error retrieving Sankey chart data: " + e.getMessage());
        }
    }
    
    @Operation(
        summary = "Get current month budget expenses",
        description = "Retrieves all budget expenses for the current month for a specific user"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "Successfully retrieved budget expenses",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "204", 
            description = "No expenses found for the user",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "400", 
            description = "Invalid parameters provided",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "403", 
            description = "Forbidden access"
        ),
        @ApiResponse(
            responseCode = "500", 
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/user/{userId}/month")
    @PreAuthorize("hasAuthority('USER')")
    public ResponseEntity<?> getCurrentMonthBudgetExpenses(
        @Parameter(
            description = "ID of the user to get expenses for",
            required = true
        )
        @PathVariable Integer userId) {
        
        try {
            if (userId == null) {
                return ResponseEntity.badRequest().body("User ID is required.");
            }
            
            List<BudgetExpenseDto> expenses = budgetService.getCurrentMonthBudgetExpenses(userId);
            
            if (expenses.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NO_CONTENT).body("No expenses found for the user");
            }
            
            return ResponseEntity.ok(expenses);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error processing request: " + e.getMessage());
        }
    }

    /**
     * Get the past 12 months' budget expenses for a user (excluding income)
     * @param userId The user ID
     * @return List of budget expenses for the past 12 months
     */
    @Operation(
        summary = "Get past 12 months budget expenses",
        description = "Retrieves all budget expenses for the past 12 months for a specific user"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "Successfully retrieved budget expenses",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "204", 
            description = "No expenses found for the user",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "400", 
            description = "Invalid parameters provided",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "403", 
            description = "Forbidden access"
        ),
        @ApiResponse(
            responseCode = "500", 
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/user/{userId}/year")
    @PreAuthorize("hasAuthority('USER')")
    public ResponseEntity<?> getPast12MonthsExpenses(
        @Parameter(
            description = "ID of the user to get expenses for",
            required = true
        )
        @PathVariable Integer userId) {
        
        try {
            if (userId == null) {
                return ResponseEntity.badRequest().body("User ID is required.");
            }
            
            List<BudgetExpenseDto> expenses = budgetService.getPast12MonthsExpenses(userId);
            
            if (expenses.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NO_CONTENT).body("No expenses found for the user");
            }
            
            return ResponseEntity.ok(expenses);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error processing request: " + e.getMessage());
        }
    }

    @Operation(
        summary = "Get budget expenses by category and subcategory",
        description = "Retrieves all budget expenses for a specific user, category, and subcategory"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved budget expenses"),
        @ApiResponse(responseCode = "400", description = "Invalid parameters provided"),
        @ApiResponse(responseCode = "403", description = "Forbidden access"),
        @ApiResponse(responseCode = "500", description = "Internal server error")    
    })
    @GetMapping("/user/{userId}/year/{year}/month/{month}")
    public ResponseEntity<?> getBudgetByUserAndYearAndMonth(
            @Parameter(description = "User ID", required = true)
            @PathVariable Integer userId,
            @Parameter(description = "Year", required = true)
            @PathVariable Integer year,
            @Parameter(description = "Month", required = true)
            @PathVariable Integer month) {
        try {
            if (userId == null) {
                return ResponseEntity.badRequest().body("User ID is required.");
            }
            if (year == null) {
                return ResponseEntity.badRequest().body("Year is required.");
            }
            if (month == null) {
                return ResponseEntity.badRequest().body("Month is required.");
            }
            if (month < 1 || month > 12) {
                return ResponseEntity.badRequest().body("Month must be between 1 and 12.");
            }

            List<BudgetUIDto> budgetDtos = budgetService.getBudgetByUserAndYearAndMonth(userId, year, month);
            if (budgetDtos.isEmpty()) {
                return ResponseEntity.noContent().build();
            }
            return ResponseEntity.ok(budgetDtos);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error retrieving budget expenses: " + e.getMessage());
        }
    }
}