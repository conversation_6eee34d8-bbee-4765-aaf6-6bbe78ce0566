package com.pennypal.fintech.api.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.pennypal.fintech.service.SplitTransactionService;

import com.pennypal.fintech.dto.SplitTransactionDto;
import com.pennypal.fintech.entity.SplitTransaction;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/api/split-transactions")
@Tag(name = "Split Transaction Management", description = "APIs for managing split transactions and shared expenses")
public class SplitTransactionController {
    @Autowired
    private SplitTransactionService splitTransactionService;

 @PostMapping("/add")
    @Operation(summary = "Create split transactions",
               description = "Creates multiple split transactions for shared expenses")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Split transactions created successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of created split transaction DTOs")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid split transaction data",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<List<SplitTransactionDto>> createSplitTransactions(
            @Parameter(description = "List of split transaction data to create", required = true)
            @RequestBody List<SplitTransactionDto> dtoList) {
        List<SplitTransaction> createdTransactions = splitTransactionService.createSplitTransactions(dtoList);

        List<SplitTransactionDto> response = createdTransactions.stream().map(entity -> {
            SplitTransactionDto dto = new SplitTransactionDto();
            dto.setId(entity.getId());
            dto.setAmount(entity.getAmount());
            dto.setDate(entity.getDate());
            dto.setNotesDesc(entity.getNotesDesc());
            dto.setUserId(entity.getUser() != null ? entity.getUser().getId() : null);
            dto.setTransactionId(entity.getTransaction() != null ? entity.getTransaction().getId() : null);
            dto.setUserContactId(entity.getUserContact() != null ? entity.getUserContact().getId() : null);
            return dto;
        }).toList();

        return ResponseEntity.ok(response);
    }

     @GetMapping("/user/{userId}")
    @Operation(summary = "Get split transactions by user ID",
               description = "Retrieves all split transactions for a specific user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved split transactions",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of split transaction DTOs")
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<List<SplitTransactionDto>> getSplitTransactionsByUserId(
            @Parameter(description = "ID of the user to get split transactions for", required = true)
            @PathVariable Integer userId) {
        List<SplitTransactionDto> splitTransactions = splitTransactionService.getSplitTransactionsByUserId(userId);
        return ResponseEntity.ok(splitTransactions);
    }
}