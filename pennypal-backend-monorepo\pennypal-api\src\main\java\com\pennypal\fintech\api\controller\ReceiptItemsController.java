package com.pennypal.fintech.api.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.pennypal.fintech.repository.ReceiptItemRepository;

import com.pennypal.fintech.entity.ReceiptItems;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

@RestController
@RequestMapping("/api/v1/receipt-items")
public class ReceiptItemsController {
    
    @Autowired
    private ReceiptItemRepository receiptItemRepository;

    @Operation(summary = "Get receipt items by receipt ID",
               description = "Retrieves all items associated with a specific receipt")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved receipt items",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of receipt items")
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Receipt not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/by-receipt/{receiptId}")
    public ResponseEntity<List<ReceiptItems>> getItemsByReceiptId(
        @Parameter(description = "ID of the receipt to get items for", required = true)
        @PathVariable int receiptId) {
        List<ReceiptItems> items = receiptItemRepository.findByReceiptId(receiptId);
        return ResponseEntity.ok(items);
    }

    @Operation(summary = "Get all receipt items",
               description = "Retrieves all available receipt items")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved all receipt items",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of receipt items")
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/all")
    public ResponseEntity<List<ReceiptItems>> getAllItems() {
        List<ReceiptItems> items = receiptItemRepository.findAll();
        return ResponseEntity.ok(items);
    }

@Operation(summary = "Get item summary",
               description = "Retrieves a summary of all items with total spending amounts")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved item summary",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of item summaries")
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/summary")
    public ResponseEntity<List<Map<String, Object>>> getItemSummary() {
        List<Object[]> results = receiptItemRepository.findItemSummary();
        List<Map<String, Object>> summary = results.stream().map(row -> {
            Map<String, Object> map = new HashMap<>();
            map.put("item", row[0]);
            map.put("totalSpent", row[1]);  
            return map;
        }).collect(Collectors.toList());
        return ResponseEntity.ok(summary);
    }
}