package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.UserRatingDto;
import com.pennypal.fintech.dto.RatingResponseDto;
import com.pennypal.fintech.dto.RatingStatsDto;
import com.pennypal.fintech.service.RatingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@Slf4j
@RestController
@RequestMapping("/api/ratings")
@Tag(name = "User Ratings", description = "APIs for managing user ratings and feedback")
public class RatingController {
    
    @Autowired
    private RatingService ratingService;
    
    @PostMapping("/submit")
    @Operation(summary = "Submit user rating",
               description = "Submits a user rating and feedback for the application")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Rating submitted successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponse.class)
            )
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid rating data",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponse.class)
            )
        )
    })
    public ResponseEntity<?> submitRating(
            @Parameter(description = "User rating data to submit", required = true)
            @RequestBody UserRatingDto ratingDto) {
        try {
            log.info("Received rating submission request for user: {}", ratingDto.getUserId());
            
            RatingResponseDto response = ratingService.submitRating(ratingDto);
            
            return ResponseEntity.ok().body(new ApiResponse<>(
                true, 
                "Rating submitted successfully", 
                response
            ));
            
        } catch (Exception e) {
            log.error("Error submitting rating: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new ApiResponse<>(
                false, 
                e.getMessage(), 
                null
            ));
        }
    }
    
    @Operation(summary = "Get user rating",
               description = "Retrieves the rating provided by the user")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved user rating",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponse.class)
            )
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "404",
            description = "User rating not found",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponse.class)
            )
        )
    })
    @GetMapping("/user/{userId}")
    public ResponseEntity<?> getUserRating(
        @Parameter(description = "ID of the user to get rating for", required = true)
        @PathVariable Integer userId) {
        try {
            log.info("Received get rating request for user: {}", userId);
            
            RatingResponseDto rating = ratingService.getUserRating(userId);
            
            return ResponseEntity.ok().body(new ApiResponse<>(
                true, 
                "Rating retrieved successfully", 
                rating
            ));
            
        } catch (Exception e) {
            log.error("Error getting user rating: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(new ApiResponse<>(
                false, 
                e.getMessage(), 
                null
            ));
        }
    }
    
    @Operation(summary = "Get user rating stats",
               description = "Retrieves statistics related to the user's rating")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved user rating stats",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponse.class)
            )
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid user ID",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponse.class)
            )
        )
    })
    @GetMapping("/stats/{userId}")
    public ResponseEntity<?> getUserRatingStats(
        @Parameter(description = "ID of the user to get rating stats for", required = true)
        @PathVariable Integer userId) {
        try {
            log.info("Received rating stats request for user: {}", userId);
            
            RatingStatsDto stats = ratingService.getUserRatingStats(userId);
            
            return ResponseEntity.ok().body(new ApiResponse<>(
                true, 
                "Rating stats retrieved successfully", 
                stats
            ));
            
        } catch (Exception e) {
            log.error("Error getting rating stats: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new ApiResponse<>(
                false, 
                e.getMessage(), 
                null
            ));
        }
    }
    
    @Operation(summary = "Update user rating",
               description = "Updates the rating provided by the user")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Rating updated successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponse.class)
            )
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid rating data",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponse.class)
            )
        )
    })
    @PutMapping("/update")
    public ResponseEntity<?> updateRating(
        @Parameter(description = "User rating data to update", required = true)
        @RequestBody UserRatingDto ratingDto) {
        try {
            log.info("Received rating update request for user: {}", ratingDto.getUserId());
            
            RatingResponseDto response = ratingService.submitRating(ratingDto);
            
            return ResponseEntity.ok().body(new ApiResponse<>(
                true, 
                "Rating updated successfully", 
                response
            ));
            
        } catch (Exception e) {
            log.error("Error updating rating: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new ApiResponse<>(
                false, 
                e.getMessage(), 
                null
            ));
        }
    }
    
    @Operation(summary = "Delete user rating",
               description = "Deletes the rating provided by the user")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Rating deleted successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponse.class)
            )
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid user ID",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponse.class)
            )
        )
    })
    @DeleteMapping("/user/{userId}")
    public ResponseEntity<?> deleteUserRating(
        @Parameter(description = "ID of the user to delete rating for", required = true)
        @PathVariable Integer userId) {
        try {
            log.info("Received delete rating request for user: {}", userId);
            
            boolean deleted = ratingService.deleteUserRating(userId);
            
            return ResponseEntity.ok().body(new ApiResponse<>(
                true, 
                "Rating deleted successfully", 
                deleted
            ));
            
        } catch (Exception e) {
            log.error("Error deleting rating: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new ApiResponse<>(
                false, 
                e.getMessage(), 
                null
            ));
        }
    }
    
    // Inner class for API Response structure
    public static class ApiResponse<T> {
        private boolean success;
        private String message;
        private T data;
        
        public ApiResponse(boolean success, String message, T data) {
            this.success = success;
            this.message = message;
            this.data = data;
        }
        
        // Getters and setters
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public T getData() {
            return data;
        }
        
        public void setData(T data) {
            this.data = data;
        }
    }
}