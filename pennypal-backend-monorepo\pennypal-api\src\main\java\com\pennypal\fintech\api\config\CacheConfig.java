package com.pennypal.fintech.api.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.beans.factory.annotation.Value;
import java.time.Duration;
import java.util.Arrays;

@Configuration
@EnableCaching
public class CacheConfig {
    
    @Value("${cache.type:memory}")
    private String cacheType;

    @Bean
    @Profile("!redis")
    public CacheManager inMemoryCacheManager() {
        // SimpleCacheManager cacheManager = new SimpleCacheManager();
        // cacheManager.setCaches(Arrays.asList(
        //     new ConcurrentMapCache("transactionCache"),
        //     new ConcurrentMapCache("userCache"),
        //     new ConcurrentMapCache("aggregatedExpensesCache")
        // ));
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        
        // Configure default cache settings
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofHours(1))
            .maximumSize(1000)
            .recordStats());

        // Set the cache names
        cacheManager.setCacheNames(Arrays.asList(
            "transactionCache",
            "accountBalanceCache",
            "budgetRulesCache",
            "budgetCache",
            "notificationCache",
            "notificationRuleCache",
            "subCategoryCache",
            "chatbotCache",
            "customChartCache",
            "aclCache",
            "categoryCache",
            "svgIconCache",
            "splitTransactionCache",
            "paymentCache",
            "goalCache"
        ));
        return cacheManager;
    }
}