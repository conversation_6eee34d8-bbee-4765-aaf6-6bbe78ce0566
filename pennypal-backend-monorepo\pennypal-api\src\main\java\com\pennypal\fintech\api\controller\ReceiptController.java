package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.service.ReceiptService;
import com.pennypal.fintech.entity.Receipts;
import com.pennypal.fintech.repository.ReceiptRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.io.IOException;
import java.nio.file.*;
import java.util.*;

@RestController
@RequestMapping("/api/receipts")
@Tag(name = "Receipt Management", description = "APIs for managing receipts, documents, and file uploads")
public class ReceiptController {
    @Autowired
    private ReceiptService receiptService;
    
    @Autowired
    private ReceiptRepository receiptRepository;
   
    @Value("${file.upload-dir}")
    private String uploadDir;

    @PostMapping("/uploadReceipt")
    @Operation(summary = "Upload receipt document",
               description = "Uploads a receipt document with metadata and optional QR data")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Receipt uploaded successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "Upload response with file details")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid file or missing required parameters",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error during file upload",
            content = @Content
        )
    })
    public ResponseEntity<Map<String, Object>> uploadDocument(
            @Parameter(description = "Receipt file to upload", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "Document name", required = true)
            @RequestParam("docName") String docName,
            @Parameter(description = "Document type", required = true)
            @RequestParam("docType") String docType,
            @Parameter(description = "Document category", required = true)
            @RequestParam("category") String category,
            @Parameter(description = "QR code data (optional)", required = false)
            @RequestParam(value = "qrData", required = false) String qrData,
            @Parameter(description = "File size", required = true)
            @RequestParam("size") Double size) {
        try {
            if (file.isEmpty()) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "No file uploaded");
            }
          
            String originalFileName = file.getOriginalFilename();
            String uuid = UUID.randomUUID().toString();
            String newFileName = uuid + "_" + originalFileName;
    
            // Save file locally
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }
            Path filePath = uploadPath.resolve(newFileName);
            Files.write(filePath, file.getBytes());
   
            System.out.println("File saved to: " + filePath.toString());

            String imageUrl = "/uploads/" + newFileName;
    
            // Process the file based on document type
            Map<String, Object> responseData;
            if (docType.equalsIgnoreCase("receipt")) {
                responseData = receiptService.processReceipt(file);
            } else if (docType.equalsIgnoreCase("id")) {
                responseData = receiptService.processIdDocument(file);
            } else if (docType.equalsIgnoreCase("invoice")) {
                responseData = receiptService.processInvoiceDocument(file);
            } else if (docType.equalsIgnoreCase("tax")) {
                responseData = receiptService.processTaxDocument(file);
            } else if (docType.equalsIgnoreCase("contract")) {
                responseData = receiptService.processContractDocument(file);
            } else {
                responseData = new HashMap<>();
            }
            
            // Add common document data
            responseData.put("savedFilePath", imageUrl);
            responseData.put("docName", docName);
            responseData.put("docType", docType);
            responseData.put("category", category);
            responseData.put("size", size);
            responseData.put("qrData", qrData);
            
            return ResponseEntity.ok(responseData);
    
        } catch (IOException e) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "File upload failed", e);
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error processing document", e);
        }
    }

    // Simplified endpoint for receipts only
 @Operation(summary = "Upload receipt documents",
               description = "Uploads one or more receipt documents")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Receipts uploaded successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of upload responses with file details")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid file or missing required parameters",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error during file upload",
            content = @Content
        )
    })
    @PostMapping("/upload")
    public ResponseEntity<?> uploadReceipts(
        @Parameter(description = "List of receipt files to upload", required = true)
        @RequestParam("file") List<MultipartFile> files) {
        if (files == null || files.isEmpty()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("error", "No files provided"));
        }

        List<Map<String, Object>> results = new ArrayList<>();
        for (MultipartFile file : files) {
            if (file.isEmpty()) {
                results.add(Map.of("error", "Empty file: " + file.getOriginalFilename()));
                continue;
            }

            try {
                String originalFileName = file.getOriginalFilename();
                String uuid = UUID.randomUUID().toString();
                String newFileName = uuid + "_" + originalFileName;

                Path uploadPath = Paths.get(uploadDir);
                if (!Files.exists(uploadPath)) {
                    Files.createDirectories(uploadPath);
                }

                Path filePath = uploadPath.resolve(newFileName);
                Files.write(filePath, file.getBytes());
                System.out.println("File saved to: " + filePath.toString());

                String imageUrl = "/uploads/" + newFileName;

                Map<String, Object> receiptResponse = receiptService.processReceipt(file);
                receiptResponse.put("savedFilePath", imageUrl);
                receiptResponse.put("docType", "receipt");

                results.add(receiptResponse);
            } catch (IOException e) {
                System.err.println("IO error for file " + file.getOriginalFilename() + ": " + e.getMessage());
                results.add(Map.of("error", "Upload failed for " + file.getOriginalFilename() + ": " + e.getMessage()));
            } catch (Exception e) {
                System.err.println("Processing error for file " + file.getOriginalFilename() + ": " + e.getMessage());
                results.add(Map.of("error", "Processing error for " + file.getOriginalFilename() + ": " + e.getMessage()));
            }
        }

        return results.stream().anyMatch(r -> r.containsKey("error"))
            ? ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(results)
            : ResponseEntity.ok(results);
    }

    // Common save endpoint for both
    @Operation(summary = "Save receipt to database",
               description = "Saves a receipt to the database with associated transaction data")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Receipt saved successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "Save response with receipt details")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid receipt data",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PostMapping("/saveReceipt")
    public ResponseEntity<Map<String, Object>> saveReceipt(
        @Parameter(description = "Receipt data to be saved", required = true)
        @RequestBody Map<String, Object> receiptData) {
        try {
            System.out.println("Received receipt data: " + receiptData);  
            
            String savedFilePath = (String) receiptData.get("savedFilePath");
            
            if (savedFilePath != null && !savedFilePath.isEmpty()) {
                Path originalPath = Paths.get(uploadDir, Paths.get(savedFilePath).getFileName().toString());
                Path scannedDir = Paths.get(uploadDir, "scanned-copy");
                if (!Files.exists(scannedDir)) {
                    Files.createDirectories(scannedDir);
                }
    
                Path scannedPath = scannedDir.resolve(originalPath.getFileName());
                Files.copy(originalPath, scannedPath, StandardCopyOption.REPLACE_EXISTING);
    
                String scannedFileUrl = "/uploads/scanned-copy/" + scannedPath.getFileName().toString();
                receiptData.put("scannedCopyPath", scannedFileUrl);
            }
            
            Receipts savedReceipt = receiptService.saveReceiptToDatabase(receiptData);
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Receipt saved successfully");
            response.put("id", savedReceipt.getId());
            response.put("docName", savedReceipt.getDocName());
            response.put("filePath", receiptData.get("savedFilePath"));
            response.put("scannedCopyPath", receiptData.get("scannedCopyPath"));
            response.put("transTotal", savedReceipt.getTransTotal());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error saving receipt: " + e.getMessage(), e);
        }
    }

    @Operation(summary = "Delete receipt by ID",
               description = "Deletes a receipt by its unique identifier")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully deleted the receipt",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "Deletion response")
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Receipt not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> deleteReceipt(
        @Parameter(description = "Unique identifier of the receipt to delete", required = true)
        @PathVariable Integer id) {
        try {
            boolean deleted = receiptService.deleteReceiptById(id);
            if (deleted) {
                return ResponseEntity.ok().body(Map.of("message", "Receipt deleted successfully", "id", id));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of("error", "Receipt not found"));
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("error", "Error deleting receipt"));
        }
    }

    @Operation(summary = "Get all receipts",
               description = "Retrieves all available receipts")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved all receipts",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of receipts")
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/all")
    public ResponseEntity<List<Receipts>> getAllReceipts() {
        try {
            List<Receipts> receipts = receiptRepository.findAll();
            return ResponseEntity.ok(receipts);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to fetch receipts", e);
        }
    }

@Operation(summary = "Get receipts by user ID",
               description = "Retrieves all receipts for a specific user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved user receipts",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of user receipts")
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/user/{userId}")
    public ResponseEntity<List<Receipts>> getReceiptsByUserId(
            @Parameter(description = "ID of the user to get receipts for", required = true)
            @PathVariable Long userId) {
        try {
            List<Receipts> receipts = receiptRepository.findByUserId(userId);
            return ResponseEntity.ok(receipts);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to fetch receipts", e);
        }
    }

 @Operation(summary = "Get receipt details by transaction ID",
               description = "Retrieves details of a specific receipt by its associated transaction ID")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved receipt details",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "Receipt details")
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Receipt not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/getReceiptDetails/{transactionId}")
    public ResponseEntity<Map<String, Object>> getReceiptDetails(
            @Parameter(description = "ID of the transaction to get receipt details for", required = true)
            @PathVariable int transactionId) {
        try {
            Map<String, Object> receiptDetails = receiptService.getReceiptDetailsByTransactionId(transactionId);
            return ResponseEntity.ok(receiptDetails);
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error fetching receipt details", e);
        }
    }

    @Operation(summary = "Get all receipt transaction IDs",
               description = "Retrieves a list of all transaction IDs associated with receipts")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved receipt transaction IDs",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of transaction IDs")
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/getReceiptTransactionIds")
    public ResponseEntity<List<Integer>> getReceiptTransactionIds() {
        List<Integer> transactionIds = receiptService.getReceiptTransactionIds();
        return ResponseEntity.ok(transactionIds);
    }

    @Operation(summary = "Get document details",
               description = "Retrieves details of a specific document by its ID")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved document details",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "Document details")
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Document not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/getDocumentDetails/{documentId}")
    public ResponseEntity<Map<String, Object>> getDocumentDetails(@PathVariable int documentId) {
        try {
            Map<String, Object> documentDetails = receiptService.getDocumentDetails(documentId);
            // For receipts, ensure amount is included
            if ("receipt".equalsIgnoreCase((String)documentDetails.get("docType"))) {
                Double transTotal = documentDetails.get("transTotal") != null ? 
                    Double.parseDouble(documentDetails.get("transTotal").toString()) : 0.0;
                documentDetails.put("amount", transTotal);
            } else {
                documentDetails.put("amount", 0.0);
            }
            
            return ResponseEntity.ok(documentDetails);
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error fetching document details", e);
        }
    }    
}