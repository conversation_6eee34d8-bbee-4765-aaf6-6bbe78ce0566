// Enhanced AclService with primary/secondary user support using UserRelationship
package com.pennypal.fintech.service;

import com.pennypal.fintech.dto.UserPermissionDto;
import com.pennypal.fintech.dto.SecondaryUserPermissionDto;
import com.pennypal.fintech.entity.UserRelationship;
import com.pennypal.fintech.repository.AclPermissionRepository;
import com.pennypal.fintech.repository.MembershipRepository;
import com.pennypal.fintech.exception.UnauthorizedException;

import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Service
public class AclService {
    
    private final AclPermissionRepository aclPermissionRepository;
    private final MembershipRepository membershipRepository;
    
    public AclService(AclPermissionRepository aclPermissionRepository, 
                     MembershipRepository membershipRepository) {
        this.aclPermissionRepository = aclPermissionRepository;
        this.membershipRepository = membershipRepository;
    }
    
    /**
     * Get permissions for primary user
     */
    @Cacheable(value = "aclCache", key = "#userId")
    @Transactional(readOnly = true)
    public List<UserPermissionDto> getPermissionsByUserId(Integer userId) {
        return aclPermissionRepository.findPermissionsByUserId(userId);
    }
    
    /**
     * Get permissions for secondary user based on primary user's permissions
     */
    public List<SecondaryUserPermissionDto> getSecondaryUserPermissions(Integer primaryUserId, Integer secondaryUserId) {
        // Check if secondary user has access to primary user's account
        UserRelationship relationship = membershipRepository.findBySecondaryUserId(secondaryUserId)
            .orElseThrow(() -> new UnauthorizedException("Secondary user does not have access to this account"));
        
        if (relationship.getPrimaryUserId()==(primaryUserId)) {
            throw new UnauthorizedException("Secondary user does not have access to this specific account");
        }
        
        if (!"ACTIVE".equals(relationship.getStatus())) {
            throw new UnauthorizedException("Secondary user access is not active");
        }
        
        // Get all permissions for primary user
        List<UserPermissionDto> primaryPermissions = getPermissionsByUserId(primaryUserId);
        
        // Filter permissions based on secondary user's permission type
        return filterPermissionsByType(primaryPermissions, relationship.getPermissionType());
    }
    
    /**
     * Check if secondary user can perform specific action on specific page
     */
    public boolean canSecondaryUserPerformAction(Integer primaryUserId, Integer secondaryUserId, 
                                               String pageName, String actionName) {
        try {
            List<SecondaryUserPermissionDto> permissions = getSecondaryUserPermissions(primaryUserId, secondaryUserId);
            
            return permissions.stream()
                .anyMatch(permission -> 
                    permission.getPageName().equals(pageName) && 
                    permission.getActionName().equals(actionName));
        } catch (UnauthorizedException e) {
            return false;
        }
    }
    
    /**
     * Get all secondary users who have access to primary user's account
     */
    public List<UserRelationship> getSecondaryUsersForPrimary(Integer primaryUserId) {
        return membershipRepository.findByPrimaryUserId(primaryUserId).stream()
            .filter(relationship -> "ACTIVE".equals(relationship.getStatus()))
            .collect(Collectors.toList());
    }
    
    /**
     * Grant access to secondary user by email (creates pending relationship)
     */
    public void grantSecondaryUserAccess(Integer primaryUserId, String secondaryUserEmail, 
                                       String permissionType, String relationshipType) {
        UserRelationship relationship = new UserRelationship();
        relationship.setPrimaryUserId(primaryUserId);
        relationship.setSecondaryUserEmail(secondaryUserEmail);
        relationship.setPermissionType(permissionType);
        relationship.setRelationshipType(relationshipType);
        relationship.setStatus("PENDING");
        relationship.setCreatedAt(java.time.LocalDateTime.now());
        
        membershipRepository.save(relationship);
    }
    
    /**
     * Grant access to secondary user by ID (directly activate relationship)
     */
    public void grantSecondaryUserAccess(Integer primaryUserId, Integer secondaryUserId, 
                                       String permissionType, String relationshipType) {
        UserRelationship relationship = new UserRelationship();
        relationship.setPrimaryUserId(primaryUserId);
        relationship.setSecondaryUserId(secondaryUserId);
        relationship.setPermissionType(permissionType);
        relationship.setRelationshipType(relationshipType);
        relationship.setStatus("ACTIVE");
        relationship.setCreatedAt(java.time.LocalDateTime.now());
        
        membershipRepository.save(relationship);
    }
    
    /**
     * Revoke secondary user access
     */
    public void revokeSecondaryUserAccess(Integer primaryUserId, Integer secondaryUserId) {
        UserRelationship relationship = membershipRepository.findBySecondaryUserId(secondaryUserId)
            .orElseThrow(() -> new IllegalArgumentException("User relationship not found"));
        
        if (relationship.getPrimaryUserId()==(primaryUserId)) {
            throw new IllegalArgumentException("Secondary user is not associated with this primary user");
        }
        
        relationship.setStatus("REJECTED");
        relationship.setUpdatedAt(java.time.LocalDateTime.now());
        
        membershipRepository.save(relationship);
    }
    
    /**
     * Update secondary user permission type
     */
    public void updateSecondaryUserPermissionType(Integer primaryUserId, Integer secondaryUserId, 
                                                String newPermissionType) {
        UserRelationship relationship = membershipRepository.findBySecondaryUserId(secondaryUserId)
            .orElseThrow(() -> new IllegalArgumentException("User relationship not found"));
        
        if (relationship.getPrimaryUserId()==(primaryUserId)) {
            throw new IllegalArgumentException("Secondary user is not associated with this primary user");
        }
        
        relationship.setPermissionType(newPermissionType);
        relationship.setUpdatedAt(java.time.LocalDateTime.now());
        
        membershipRepository.save(relationship);
    }
    
    /**
     * Activate pending relationship when secondary user accepts invitation
     */
    public void activateSecondaryUserAccess(Integer secondaryUserId, String inviteToken) {
        UserRelationship relationship = membershipRepository.findBySecondaryUserId(secondaryUserId)
            .orElseThrow(() -> new IllegalArgumentException("User relationship not found"));
        
        if (!relationship.getInviteToken().equals(inviteToken)) {
            throw new UnauthorizedException("Invalid invite token");
        }
        
        if (relationship.getTokenExpiresAt().isBefore(java.time.LocalDateTime.now())) {
            throw new UnauthorizedException("Invite token has expired");
        }
        
        relationship.setStatus("ACTIVE");
        relationship.setUpdatedAt(java.time.LocalDateTime.now());
        
        membershipRepository.save(relationship);
    }
    
    /**
     * Find relationship by secondary user email
     */
    public UserRelationship findRelationshipBySecondaryUserEmail(String email) {
        return membershipRepository.findBySecondaryUserEmail(email)
            .orElseThrow(() -> new IllegalArgumentException("User relationship not found for email: " + email));
    }
    
    /**
     * Filter permissions based on permission type
     */
    private List<SecondaryUserPermissionDto> filterPermissionsByType(List<UserPermissionDto> permissions, 
                                                                   String permissionType) {
        Set<String> allowedActions = getAllowedActions(permissionType);
        
        return permissions.stream()
            .filter(permission -> allowedActions.contains(permission.getActionName()))
            .map(permission -> new SecondaryUserPermissionDto(
                permission.getPageName(), 
                permission.getActionName(), 
                permissionType
            ))
            .collect(Collectors.toList());
    }
    
    /**
     * Get allowed actions based on permission type
     */
    private Set<String> getAllowedActions(String permissionType) {
        if (permissionType == null) {
            return Set.of("VIEW", "READ");
        }
        
        switch (permissionType.toUpperCase()) {
            case "READ_ONLY":
                return Set.of("VIEW", "READ", "DOWNLOAD");
            case "WRITE":
            case "FULL_ACCESS":
                return Set.of("VIEW", "READ", "CREATE", "UPDATE", "DELETE", "DOWNLOAD");
            default:
                return Set.of("VIEW", "READ");
        }
    }
}