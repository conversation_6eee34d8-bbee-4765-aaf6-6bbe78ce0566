package com.pennypal.fintech.repository;

import com.pennypal.fintech.entity.Reconcile;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ReconcileRepository extends JpaRepository<Reconcile, Long> {

     List<Reconcile> findAllByRemovedFalse();
    List<Reconcile> findByReconcileId(String reconcileId);
   Optional<Reconcile> findByReconcileIdAndAmountAndDescription(String reconcileId, Double amount, String description);
Optional<Reconcile> findByTransactionIdAndReconcileFlag(String transactionId, String reconcileFlag);
    // Reconcile findByTransactionId(Long transactionId);    
    Optional<Reconcile> findByTransactionIdAndReconcileIdAndAmountAndDescription(
        String transactionId, String reconcileId, Double amount, String description);

        List<Reconcile> findByReconcileFlag(String reconcileFlag);
}
