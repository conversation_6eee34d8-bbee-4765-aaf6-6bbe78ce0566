package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.*;
import com.pennypal.fintech.entity.Investment;
import com.pennypal.fintech.service.PlaidInvestmentService;
import com.plaid.client.model.InvestmentTransaction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

@RestController
@RequestMapping("/api/v1/plaid/investment")
@Tag(name = "Plaid Investment Management", description = "APIs for managing investment data through Plaid integration")
public class PlaidInvestmentController {

    private static final Logger logger = LoggerFactory.getLogger(PlaidInvestmentController.class);

    @Autowired
    private PlaidInvestmentService plaidInvestmentService;

    /**
     * Get investment account summary for a user
     */
    @Operation(summary = "Get investment accounts",
               description = "Retrieves all investment accounts for a specific user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved investment accounts",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/accounts/{userId}")
    public ResponseEntity<?> getInvestmentAccounts(
        @Parameter(description = "ID of the user to get investment accounts for", required = true)
        @PathVariable Integer userId) {
        try {
            logger.info("Getting investment accounts for user: {}", userId);
            List<InvestmentAccountResponseDto> accounts = plaidInvestmentService.getInvestmentAccountSummary(userId);
            return ResponseEntity.ok(accounts);
        } catch (Exception e) {
            logger.error("Error getting investment accounts for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error retrieving investment accounts: " + e.getMessage());
        }
    }

    /**
     * Get holdings for a specific investment account
     */
    @Operation(summary = "Get account holdings",
               description = "Retrieves the holdings for a specific investment account")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved account holdings",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Account not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/accounts/{userId}/holdings/{accountId}")
    public ResponseEntity<?> getAccountHoldings(
            @Parameter(description = "ID of the user to get holdings for", required = true)
            @PathVariable Integer userId,
            @Parameter(description = "ID of the account to get holdings for", required = true)
            @PathVariable Integer accountId) {
        try {
            logger.info("Getting holdings for user: {} and account: {}", userId, accountId);
            InvestmentHoldingsResponseDto holdings = plaidInvestmentService.getInvestmentHoldings(accountId);
            return ResponseEntity.ok(holdings);
        } catch (RuntimeException e) {
            logger.error("Error getting holdings for user: {} and account: {}", userId, accountId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body("Error retrieving holdings: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error getting holdings for user: {} and account: {}", userId, accountId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error retrieving holdings: " + e.getMessage());
        }
    }

    /**
     * Sync investment holdings for a user
     */
    @Operation(summary = "Sync investment holdings",
               description = "Synchronizes investment holdings for a specific user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully synced investment holdings",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid user ID or sync failed",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PostMapping("/sync-holdings/{userId}")
    public ResponseEntity<?> syncInvestmentHoldings(
        @Parameter(description = "ID of the user to sync holdings for", required = true)
        @PathVariable Integer userId) {
        try {
            logger.info("Syncing investment holdings for user: {}", userId);
            List<Investment> investments = plaidInvestmentService.syncInvestmentHoldingsForUser(userId);
            return ResponseEntity.ok("Successfully synced " + investments.size() + " investment holdings");
        } catch (RuntimeException e) {
            logger.error("Error syncing holdings for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body("Error syncing holdings: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error syncing holdings for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error syncing holdings: " + e.getMessage());
        }
    }

    /**
     * Sync investment holdings for a specific account
     */
    @Operation(summary = "Sync account holdings",
               description = "Synchronizes investment holdings for a specific account")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully synced account holdings",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid user ID or account ID or sync failed",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PostMapping("/sync-holdings/{userId}/account/{accountId}")
    public ResponseEntity<?> syncAccountHoldings(
            @Parameter(description = "ID of the user to sync holdings for", required = true)
            @PathVariable Integer userId,
            @Parameter(description = "ID of the account to sync holdings for", required = true)
            @PathVariable Integer accountId) {
        try {
            logger.info("Syncing investment holdings for user: {} and account: {}", userId, accountId);
            
            // Create sync request for specific account
            SyncInvestmentRequestDto request = new SyncInvestmentRequestDto();
            request.setUserId(userId);
            request.setAccountId(String.valueOf(accountId));
            request.setIncludeTransactions(false);
            
            plaidInvestmentService.syncInvestmentData(request);
            return ResponseEntity.ok("Investment holdings sync initiated for account: " + accountId);
        } catch (Exception e) {
            logger.error("Error syncing holdings for user: {} and account: {}", userId, accountId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error syncing holdings: " + e.getMessage());
        }
    }

    /**
     * Get investment transactions for an account
     */
    @Operation(summary = "Get investment transactions",
               description = "Retrieves investment transactions for a specific account")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved investment transactions",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Account not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/accounts/{userId}/transactions/{accountId}")
    public ResponseEntity<?> getInvestmentTransactions(
            @Parameter(description = "ID of the user to get transactions for", required = true)
            @PathVariable Integer userId,
            @Parameter(description = "ID of the account to get transactions for", required = true)
            @PathVariable Integer accountId,
            @Parameter(description = "Number of days back to get transactions for", required = false)
            @RequestParam(defaultValue = "90") Integer daysBack) {
        try {
            logger.info("Getting investment transactions for user: {}, account: {}, days back: {}", 
                    userId, accountId, daysBack);
            List<InvestmentTransaction> transactions = plaidInvestmentService.getInvestmentTransactions(accountId, daysBack);
            return ResponseEntity.ok(transactions);
        } catch (RuntimeException e) {
            logger.error("Error getting transactions for user: {} and account: {}", userId, accountId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body("Error retrieving transactions: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error getting transactions for user: {} and account: {}", userId, accountId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error retrieving transactions: " + e.getMessage());
        }
    }

    /**
     * Get user's investment portfolio summary
     */
    @Operation(summary = "Get investment portfolio summary",
               description = "Retrieves a comprehensive portfolio summary for a user including total value and performance")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved portfolio summary",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/portfolio/{userId}")
    public ResponseEntity<?> getInvestmentPortfolio(
        @Parameter(description = "ID of the user to get portfolio summary for", required = true)
        @PathVariable Integer userId) {
        try {
            logger.info("Getting investment portfolio for user: {}", userId);
            PortfolioSummaryDto portfolio = plaidInvestmentService.getPortfolioSummary(userId);
            return ResponseEntity.ok(portfolio);
        } catch (Exception e) {
            logger.error("Error getting portfolio for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error retrieving portfolio: " + e.getMessage());
        }
    }

    /**
     * Comprehensive sync - sync holdings and transactions
     */
    @Operation(summary = "Sync all investment data",
               description = "Synchronizes both holdings and transactions for a specific user and account")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully initiated sync",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PostMapping("/sync-all/{userId}")
    public ResponseEntity<?> syncAllInvestmentData(
            @Parameter(description = "ID of the user to sync data for", required = true)
            @PathVariable Integer userId,
            @Parameter(description = "ID of the account to sync data for", required = false)
            @RequestParam(required = false) String accountId,
            @Parameter(description = "Whether to include transactions in the sync", required = false)
            @RequestParam(defaultValue = "false") Boolean includeTransactions,
            @Parameter(description = "Number of days back to get transactions for", required = false)
            @RequestParam(defaultValue = "90") Integer daysBack) {
        try {
            logger.info("Starting comprehensive sync for user: {}, account: {}, includeTransactions: {}", 
                    userId, accountId, includeTransactions);
                    
            SyncInvestmentRequestDto request = new SyncInvestmentRequestDto();
            request.setUserId(userId);
            request.setAccountId(accountId);
            request.setIncludeTransactions(includeTransactions);
            request.setDaysBack(daysBack);
            
            plaidInvestmentService.syncInvestmentData(request);
            
            String message = accountId != null ? 
                    "Investment data sync initiated for account: " + accountId :
                    "Investment data sync initiated for all user accounts";
                    
            return ResponseEntity.ok(message);
        } catch (Exception e) {
            logger.error("Error during comprehensive sync for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error syncing investment data: " + e.getMessage());
        }
    }

    /**
     * Get investment holdings by security type
     */
    @Operation(summary = "Get holdings by security type",
               description = "Retrieves investment holdings grouped by security type for a specific user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved holdings by type",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/holdings/{userId}/by-type")
    public ResponseEntity<?> getHoldingsByType(
        @Parameter(description = "ID of the user to get holdings for", required = true)
        @PathVariable Integer userId) {
        try {
            logger.info("Getting holdings by type for user: {}", userId);
            PortfolioSummaryDto portfolio = plaidInvestmentService.getPortfolioSummary(userId);
            
            // You can enhance this to group holdings by security type
            // For now, return the portfolio summary
            return ResponseEntity.ok(portfolio);
        } catch (Exception e) {
            logger.error("Error getting holdings by type for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error retrieving holdings by type: " + e.getMessage());
        }
    }

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<?> healthCheck() {
        return ResponseEntity.ok("Plaid Investment Service is running");
    }
}