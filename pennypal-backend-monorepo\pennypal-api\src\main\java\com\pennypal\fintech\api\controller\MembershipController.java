package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.FamilyMemberDto;
import com.pennypal.fintech.dto.FamilyMemberInviteRequestDto;
import com.pennypal.fintech.dto.FamilyMemberInviteResponseDto;
import com.pennypal.fintech.dto.ValidateInviteLinkResponseDto;
import com.pennypal.fintech.entity.UserRelationship;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.repository.MembershipRepository;
import com.pennypal.fintech.repository.UserRepository;
import com.pennypal.fintech.service.MembershipService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.http.MediaType;
import java.util.Optional;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/api/membership")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Family Membership", description = "APIs for managing family memberships, invitations, and user relationships")
public class MembershipController {
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private MembershipRepository membershipRepository;

    private final MembershipService membershipService;

    @GetMapping("/family-members")
    @Operation(summary = "Get family members",
               description = "Retrieves all family members for the current user (primary users only)")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved family members",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of family member DTOs")
            )
        ),
        @ApiResponse(
            responseCode = "403",
            description = "Access denied - only primary users can view family members",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<?> getFamilyMembers() {
        try {
            log.debug("Fetching family members for current user");
            List<FamilyMemberDto> members = membershipService.getFamilyMembers();
            return ResponseEntity.ok(members);
        } catch (RuntimeException e) {
            log.error("Error fetching family members: {}", e.getMessage());
            // Check if it's a primary user access denial
            if (e.getMessage().contains("Only primary users can view")) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of(
                        "success", false,
                        "message", e.getMessage()
                    ));
            }
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(Map.of(
                    "success", false,
                    "message", e.getMessage()
                ));
        } catch (Exception e) {
            log.error("Unexpected error fetching family members: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of(
                    "success", false,
                    "message", "An error occurred while fetching family members"
                ));
        }
    }
    
    @Operation(summary = "Invite family member",
               description = "Invites a new family member to join the user's household")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully sent invitation",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = FamilyMemberInviteResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request parameters",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = FamilyMemberInviteResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "403",
            description = "Access denied - only primary users can invite",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = FamilyMemberInviteResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "409",
            description = "Conflict - user already invited or part of another household",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = FamilyMemberInviteResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = FamilyMemberInviteResponseDto.class)
            )
        )
    })
    @PostMapping(value = "/invite", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> inviteFamilyMember(
        @Parameter(description = "Invite request data", required = true)
        @RequestBody FamilyMemberInviteRequestDto request) {
        try {
            log.debug("Received invite request: {}", request);
            FamilyMemberInviteResponseDto response = membershipService.inviteFamilyMember(request);
            
            // If the invite was not successful, return appropriate status code
            if (!response.isSuccess()) {
                String message = response.getMessage();
                
                // Check for specific error types - updated to match service messages
                if (message.contains("Only primary users can invite")) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
                } else if (message.contains("already part of") || 
                          message.contains("already sent") || 
                          message.contains("already has their own") ||
                          message.contains("already belongs to")) {
                    return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
                } else if (message.contains("required") || message.contains("must be")) {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
                }
                
                // Default to conflict for other business logic errors
                return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
            }
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            log.error("Invalid request parameters: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(FamilyMemberInviteResponseDto.builder()
                    .success(false)
                    .message(e.getMessage())
                    .build());
        } catch (RuntimeException e) {
            log.error("Runtime error during invite: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(FamilyMemberInviteResponseDto.builder()
                    .success(false)
                    .message(e.getMessage())
                    .build());
        } catch (Exception e) {
            log.error("Unexpected error during invite: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(FamilyMemberInviteResponseDto.builder()
                    .success(false)
                    .message("An error occurred while processing the invitation")
                    .build());
        }
    }
    
    @Operation(summary = "Validate invite link",
               description = "Validates an invitation link and returns user information if valid")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully validated invitation link",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ValidateInviteLinkResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid invitation link",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ValidateInviteLinkResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ValidateInviteLinkResponseDto.class)
            )
        )
    })
    @GetMapping("/validate-invite")
    public ResponseEntity<ValidateInviteLinkResponseDto> validateInviteLink(
        @Parameter(description = "Invitation token", required = true)
        @RequestParam("token") String token) {
        try {
            log.debug("Validating invite token");
            ValidateInviteLinkResponseDto response = membershipService.validateInviteLink(token);
            
            // Return appropriate status based on validation result
            if (!response.isValid()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error validating invite link: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ValidateInviteLinkResponseDto.builder()
                    .valid(false)
                    .message("An error occurred while validating the invitation")
                    .build());
        }
    }
    
    @Operation(summary = "Complete family member signup",
               description = "Completes the signup process for a new family member using an invitation token")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully completed signup",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "Signup completion response")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request parameters",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "Signup completion response")
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "Signup completion response")
            )
        )
    })
    @PostMapping(value = "/complete-signup", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Map<String, Object>> completeSignup(
            @Parameter(description = "Invitation token", required = true)
            @RequestParam("token") String token,
            @Parameter(description = "Signup details", required = true)
            @RequestBody Map<String, String> signupDetails) {
     
        try {
            log.debug("Completing signup with token");
            Map<String, Object> result = membershipService.completeSignup(token, signupDetails);
            return ResponseEntity.ok(result);
        } catch (RuntimeException e) {
            log.error("Runtime error during signup completion: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(Map.of(
                    "success", false,
                    "message", e.getMessage()
                ));
        } catch (Exception e) {
            log.error("Unexpected error during signup completion: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of(
                    "success", false,
                    "message", "An error occurred while completing the signup"
                ));
        }
    }
   
    /**
     * Revoke family member access - only primary users can revoke
     */
    @Operation(summary = "Revoke family member access",
               description = "Revokes access for a family member (only primary users can perform this action)")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully revoked family member access",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "Revoke access response")
            )
        ),
        @ApiResponse(
            responseCode = "403",
            description = "Access denied - only primary users can revoke",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "Revoke access response")
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Family member not found",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "Revoke access response")
            )
        ),
        @ApiResponse(
            responseCode = "409",
            description = "Conflict - family member already revoked",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "Revoke access response")
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "Revoke access response")
            )
        )
    })
    @DeleteMapping("/revoke/{relationshipId}")
    public ResponseEntity<Map<String, Object>> revokeFamilyMember(
        @Parameter(description = "Relationship ID to revoke", required = true)
        @PathVariable int relationshipId) {
        try {
            log.info("Revoking family member with relationship ID: {}", relationshipId);
            
            Map<String, Object> result = membershipService.revokeFamilyMember(relationshipId);
            
            // If the revoke was not successful, return appropriate HTTP status
            if (!(boolean) result.get("success")) {
                String message = (String) result.get("message");
                
                // Check for specific error types based on actual service messages
                if (message.contains("Only primary users can revoke")) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN).body(result);
                } else if (message.contains("not found") || message.contains("not authorized")) {
                    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
                } else if (message.contains("already revoked")) {
                    return ResponseEntity.status(HttpStatus.CONFLICT).body(result);
                }
                
                // Default to bad request for other business logic errors
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
            }
            
            return ResponseEntity.ok(result);
            
        } catch (RuntimeException e) {
            log.error("Runtime error revoking family member: ", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(Map.of(
                    "success", false,
                    "message", e.getMessage()
                ));
        } catch (Exception e) {
            log.error("Unexpected error revoking family member: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of(
                    "success", false,
                    "message", "An error occurred while revoking family member access"
                ));
        }
    }
}