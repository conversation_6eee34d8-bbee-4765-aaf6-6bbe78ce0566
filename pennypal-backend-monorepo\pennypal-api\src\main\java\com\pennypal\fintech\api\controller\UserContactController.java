package com.pennypal.fintech.api.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.pennypal.fintech.service.UserContactService;

import com.pennypal.fintech.entity.UserContact;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/api/contacts")
@Tag(name = "User Contacts", description = "APIs for managing user contacts and relationships")
public class UserContactController {
    @Autowired
    private UserContactService userContactService;

    @GetMapping
    @Operation(summary = "Get user contacts",
               description = "Retrieves all contacts for a specific user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved user contacts",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<List<UserContact>> getUserContacts(
            @Parameter(description = "ID of the user to get contacts for", required = true)
            @RequestParam Integer userId) {
        List<UserContact> contacts = userContactService.getUserContacts(userId);
        System.out.println("Fetched Contacts: " + contacts); // Add a log to see the contacts data
        return ResponseEntity.ok(contacts);
    }
}