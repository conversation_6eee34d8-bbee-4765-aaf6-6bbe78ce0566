package com.pennypal.fintech.api.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pennypal.fintech.dto.AccountDto;
import com.pennypal.fintech.dto.CommonResponseDto;
import com.pennypal.fintech.entity.Accounts;
import com.pennypal.fintech.repository.AccountRepository;
import com.pennypal.fintech.service.AccountBalanceService;
import com.pennypal.fintech.service.AccountService;
import com.pennypal.fintech.service.PlaidService;
import com.pennypal.fintech.service.TokenService;
import com.pennypal.fintech.service.TransactionService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping("/api")
@Tag(name = "Plaid Integration", description = "APIs for Plaid financial data aggregation and account linking")
public class PlaidController {
    private static final Logger logger = LoggerFactory.getLogger(PlaidController.class);

    @Autowired
    private AccountBalanceService accountBalanceService;
    
    @Autowired
    private TokenService tokenService;

    @Autowired
    private AccountService accountService;
   
	@Autowired
    private PlaidService plaidService; 

    @Autowired
    private TransactionService transactionService;

    @Autowired
    private AccountRepository accountRepository;

    @PostMapping("/create_link_token")
    @Operation(summary = "Create Plaid link token",
               description = "Creates a link token for Plaid Link initialization")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully created link token",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Error creating link token",
            content = @Content(mediaType = "application/json")
        )
    })
    public ResponseEntity<Map<String,String>> createLinkToken() {
        try {
            Map<String,String> linkTokenMap = tokenService.createLinkToken();
            return ResponseEntity.ok(linkTokenMap);
        } catch (Exception e) {
            logger.error("Error creating link token", e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "Error creating link token: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
	
	@PostMapping("/exchange_public_token")
    @Operation(summary = "Exchange Plaid public token",
               description = "Exchanges a public token for an access token and saves it for the user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Token exchange response",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request data",
            content = @Content(mediaType = "application/json")
        )
    })
    public ResponseEntity<CommonResponseDto> exchangePublicToken(
            @Parameter(description = "Request body containing publicToken and userId", required = true)
            @RequestBody Map<String, String> requestBody) {
        CommonResponseDto commonResponseDto = new CommonResponseDto();
        try {
            String publicToken = requestBody.get("publicToken");
            Integer userId = requestBody.get("userId") != null ? Integer.parseInt(requestBody.get("userId")) : null;
            logger.info("Exchange public token request - userId: {}, publicToken present: {}", 
                    userId, publicToken != null && !publicToken.isEmpty());
            if (publicToken == null || publicToken.isEmpty()) {
                commonResponseDto.setSuccess(false);
                commonResponseDto.setMessage("Public token is required");
            } else if (userId == null) {
                commonResponseDto.setSuccess(false);
                commonResponseDto.setMessage("User ID is required");
            } else {
                String accessToken = plaidService.exchangePublicToken(publicToken, userId);
                if (accessToken != null) {
                    commonResponseDto.setSuccess(true);
                    commonResponseDto.setMessage("Access token exchanged and saved successfully.");
                } else {
                    commonResponseDto.setSuccess(false);
                    commonResponseDto.setMessage("Failed to exchange public token");
                }
            }
        } catch (NumberFormatException e) {
            commonResponseDto.setSuccess(false);
            commonResponseDto.setMessage("Invalid user ID format");
        } catch (Exception e) {
            commonResponseDto.setSuccess(false);
            commonResponseDto.setMessage("Error exchanging public token: " + e.getMessage());
        }
        return ResponseEntity.ok(commonResponseDto);
    }
    
    @Operation(summary = "Get accounts by institution ID",
               description = "Retrieves all accounts for a specific institution")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved institution accounts",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/accounts/institution/{institutionId}")
    public ResponseEntity<List<Accounts>> getAccountsByInstitutionId(
        @Parameter(description = "ID of the institution to get accounts for", required = true)
        @PathVariable String institutionId) {
        try {
            List<Accounts> accounts = plaidService.getAccountsByInstitutionId(institutionId);
            return ResponseEntity.ok(accounts);
        } catch (Exception e) {
            logger.error("Error fetching accounts by institution ID: {}", institutionId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @Operation(summary = "Get accounts by user ID",
               description = "Retrieves all accounts for a specific user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved user accounts",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/accounts/{userId}")
    public ResponseEntity<List<AccountDto>> getAccountsByUserId(
        @Parameter(description = "ID of the user to get accounts for", required = true)
        @PathVariable int userId) { 
        try {
            // Get accounts for the user
            List<AccountDto> accounts = plaidService.getAccountsByUserId(userId);
            
            if (accounts.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
            }

            // Get aggregated balances for the user (3 months with 15-day intervals)
            List<Map<String, Object>> aggregatedBalances = accountBalanceService
                    .getAggregatedBalancesGroupedByAccountId(userId, 3, 30);

            // Group the aggregated balances by accountId for easier lookup
            Map<Integer, List<Map<String, Object>>> balancesByAccountId = aggregatedBalances.stream()
                    .filter(map -> map.get("accountId") instanceof Integer)
                    .collect(Collectors.groupingBy(
                        map -> (Integer) map.get("accountId"),
                        Collectors.mapping(
                            map -> {
                                Map<String, Object> transformedBalance = Map.of(
                                    "Id", map.get("accountId"),
                                    "groupEndDate", map.get("groupEndDate"),
                                    "aggregatedBalance", map.get("aggregatedBalance")
                                );
                                return transformedBalance;
                            },
                            Collectors.toList()
                        )
                    ));

            // Attach filtered balances to matching accounts
            for (AccountDto account : accounts) {
                Integer currentAccountId = account.getId();
                
                if (currentAccountId != null) {
                    List<Map<String, Object>> accountSpecificBalances = 
                        balancesByAccountId.getOrDefault(currentAccountId, new ArrayList<>());
                    
                    account.setAggregatedBalances(accountSpecificBalances);
                } else {
                    account.setAggregatedBalances(new ArrayList<>());
                }
            }

            return ResponseEntity.ok(accounts);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    // @GetMapping("/accounts/{userId}")
    //     public ResponseEntity<List<AccountDto>> getAccountsByUserId(@PathVariable int userId) {
    //         try {
    //             // Fetch the accounts by userId from the service
    //             List<AccountDto> accounts = plaidService.getAccountsByUserId(userId);

    //             // If no accounts found, return a 404 Not Found status
    //             if (accounts.isEmpty()) {
    //                 return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
    //             }

    //             // If accounts found, return them with a 200 OK status
    //             return ResponseEntity.ok(accounts);
    //         } catch (Exception e) {
    //             // Log the exception for debugging purposes
    //             e.printStackTrace();

    //             // Return a 500 Internal Server Error status if something goes wrong
    //             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
    //         }
    //     }
    
    /**
     * Sync transactions for a specific account
     */
    @Operation(summary = "Sync transactions for an account",
               description = "Synchronizes transactions for a specific account")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully synced transactions",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid account ID or sync failed",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PostMapping("/accounts/{accountId}/synctransactions")
    public ResponseEntity<Map<String, Object>> syncTransactions(
        @Parameter(description = "ID of the account to sync transactions for", required = true)
        @PathVariable int accountId) {
        Map<String, Object> response = new HashMap<>();
        try {
            int transactionsCount = plaidService.syncTransactionsForAccount(accountId);

            Optional<Accounts> accountOpt = accountRepository.findById(accountId);
            if (accountOpt.isEmpty()) {
                response.put("success", false);
                response.put("message", "Account not found");
                response.put("accountId", accountId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
            Accounts account = accountOpt.get();
            int userId = account.getUser().getId();

            // Clear account balance cache
            accountBalanceService.clearAccountBalanceCache(userId);

            // Clear transaction related cache
            transactionService.clearTransactionRelatedCache(userId);

            response.put("success", true);
            response.put("message", transactionsCount + " transactions synced successfully");
            response.put("transactionsProcessed", transactionsCount);
            response.put("accountId", accountId);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            // Check for Plaid error (e.g. ITEM_LOGIN_REQUIRED)
            String errorMsg = e.getMessage();
            boolean isPlaidError = errorMsg != null && errorMsg.contains("ITEM_LOGIN_REQUIRED");

            response.put("success", false); // <-- Set to false on error!
            response.put("message", errorMsg);
            response.put("errorCode", isPlaidError ? "ITEM_LOGIN_REQUIRED" : "PLAID_SYNC_ERROR");
            response.put("accountId", accountId);

            // Use 400 Bad Request for Plaid errors, 500 for others
            HttpStatus status = isPlaidError ? HttpStatus.BAD_REQUEST : HttpStatus.INTERNAL_SERVER_ERROR;
            return ResponseEntity.status(status).body(response);
        }
    }

    /**
     * Trigger async sync for all accounts of a user
     */
    @Operation(summary = "Sync all accounts for a user",
               description = "Initiates asynchronous synchronization for all accounts of a specific user")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully initiated account sync",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PostMapping("/users/{userId}/syncallaccounts")
    public ResponseEntity<Map<String, Object>> syncAllAccountsForUser(
        @Parameter(description = "ID of the user to sync accounts for", required = true)
        @PathVariable int userId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            logger.info("Starting async sync for all accounts of user ID: {}", userId);
            
            // Validate user exists by checking if they have any accounts
            List<AccountDto> userAccounts = plaidService.getAccountsByUserId(userId);
            if (userAccounts.isEmpty()) {
                logger.warn("No accounts found for user ID: {}", userId);
                response.put("success", false);
                response.put("message", "No accounts found for user ID: " + userId);
                response.put("errorCode", "NO_ACCOUNTS_FOUND");
                response.put("userId", userId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            // Start async sync process
            plaidService.asyncSyncAllAccountsForUser(userId);

            // Clear account balance cache
            accountBalanceService.clearAccountBalanceCache(userId);

            // Clear transaction related cache
            transactionService.clearTransactionRelatedCache(userId);

            response.put("success", true);
            response.put("message", "Account synchronization process started for " + userAccounts.size() + " accounts");
            response.put("userId", userId);
            response.put("accountCount", userAccounts.size());
            response.put("status", "SYNC_INITIATED");

            logger.info("Successfully initiated async sync for {} accounts of user ID: {}", 
                       userAccounts.size(), userId);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error initiating sync for user ID: {}", userId, e);
            response.put("success", false);
            response.put("message", "Error initiating account synchronization: " + e.getMessage());
            response.put("errorCode", "SYNC_INITIATION_FAILED");
            response.put("userId", userId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

  
    // @GetMapping("/bank-icons")
    // public ResponseEntity<List<BankIconDto>> getAllBankIcons() {
    //     return ResponseEntity.ok(bankIconService.getAllBankIcons());
    // }
    // @GetMapping("/transactions/{accountId}")
    // public ResponseEntity<List<TransactionDto>> getTransactionsByAccountId(@PathVariable String accountId) {
    //     try {
    //         List<TransactionDto> transactions = plaidService.getTransactionsByAccountId(accountId);
    //         return ResponseEntity.ok(transactions);
    //     } catch (Exception e) {
    //         return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
    //     }
    // }
	
    // @PostMapping("/refresh/{userId}")
    // public ResponseEntity<Map<String, Object>> refreshAllUserAccounts(@PathVariable Integer userId) {
    //     try {
    //         Map<String, Object> response = plaidService.refreshAllUserAccounts(userId);
    //         if ((Boolean) response.get("success")) {
    //             return ResponseEntity.ok(response);
    //         } else {
    //             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    //         }
    //     } catch (Exception e) {
    //         Map<String, Object> errorResponse = new HashMap<>();
    //         errorResponse.put("success", false);
    //         errorResponse.put("message", "Error processing request: " + e.getMessage());
    //         return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    //     }
    // }
  
    // @PostMapping("/{plaidAccountId}/sync") 
    // public ResponseEntity<Map<String, Object>> syncAccount(@PathVariable String plaidAccountId) {
    //     Map<String, Object> response = plaidService.syncAccountTransactions(plaidAccountId);
        
    //     if ((boolean) response.getOrDefault("success", false)) {
    //         return ResponseEntity.ok(response);
    //     } else {
    //         return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    //     }
    // }

    // 	@GetMapping("/balance-history/{chartType}/{userId}")
    // 	public ResponseEntity<List<AccountBalanceHistoryDto>> getBalanceHistory(
    //         @PathVariable String chartType,
    //         @PathVariable int userId) {
        
    //     try {
    //         List<AccountBalanceHistoryDto> balanceHistory = 
    //             plaidService.getBalanceHistory(userId, chartType);
            
    //         return ResponseEntity.ok(balanceHistory);
    //     } catch (IllegalArgumentException e) {
    //         return ResponseEntity.badRequest().build();
    //     } catch (Exception e) {
    //         return ResponseEntity.internalServerError().build();
    //     }
    // // }

    // @GetMapping("/balance-history/{chartType}/{userId}") 
    // public ResponseEntity<List<AccountBalanceHistoryDto>> getBalanceHistory(
    //         @PathVariable String chartType,
    //         @PathVariable int userId,
    //         @RequestParam(defaultValue = "yearly") String timePeriod) {
        
    //     try {
    //         List<AccountBalanceHistoryDto> balanceHistory =
    //             plaidService.getBalanceHistory(userId, chartType, timePeriod);
                
    //         return ResponseEntity.ok(balanceHistory);
    //     } catch (IllegalArgumentException e) {
    //         return ResponseEntity.badRequest().build();
    //     } catch (Exception e) {
    //         return ResponseEntity.internalServerError().build();
    //     }
    // }
 
    /* 
    @PostMapping("/exchange_public_token")
    public ResponseEntity<?> exchangePublicToken(@RequestBody Map<String, String> requestBody) {
        try {
            String publicToken = requestBody.get("publicToken");  // Ensure correct key is used in frontend and backend
            if (publicToken == null || publicToken.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("{\"error\": \"Public token is required\"}");
            }
   
            String accessToken = plaidService.exchangePublicToken(publicToken);

            List<AccountBase> listOfAccounts = plaidService.getAccountBalance(accessToken);
            //

            

            // accessToken 


            if (accessToken != null) {
                return ResponseEntity.ok().body("{\"access_token\": \"" + accessToken + "\"}");
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("{\"error\": \"Failed to exchange public token\"}");
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\": \"Error exchanging public token: " + e.getMessage() + "\"}");
        }
    }*/
}