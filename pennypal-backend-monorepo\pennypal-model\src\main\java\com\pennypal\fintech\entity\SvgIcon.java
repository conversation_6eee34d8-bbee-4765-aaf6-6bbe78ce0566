package com.pennypal.fintech.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "svgicons")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SvgIcon {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(nullable = false)
    private String description;

    @Column(name = "icon_name", nullable = false)
    private String iconName;
  @Column(name = "ticker_symbol")
    private String tickerSymbol;

    @Lob
    @Column(name = "svg_content", columnDefinition = "BLOB")
    private byte[] svgContent;
}
