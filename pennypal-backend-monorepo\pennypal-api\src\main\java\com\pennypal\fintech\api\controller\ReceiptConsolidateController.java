package com.pennypal.fintech.api.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pennypal.fintech.entity.ReceiptConsolidate;
import com.pennypal.fintech.repository.ReceiptConsolidateRepository;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/api/v1/receipt")
@Tag(name = "Receipt Consolidation", description = "APIs for consolidated receipt data and spending summaries")
public class ReceiptConsolidateController {

    @Autowired
    private ReceiptConsolidateRepository receiptConsolidateRepository;

    @Operation(summary = "Get consolidated receipt summary",
               description = "Retrieves a consolidated summary of all receipt items with total spending amounts")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved consolidated summary",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of receipt items with total spending")
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "text/plain")
        )
    })
    @GetMapping("/summary")
    public ResponseEntity<?> getConsolidatedSummary() {
        try {
            List<ReceiptConsolidate> items = receiptConsolidateRepository.findAll();

            List<Map<String, Object>> response = items.stream()
                .map(item -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("itemName", item.getReceiptItem() != null ? item.getReceiptItem() : "Unknown Item");
                    map.put("totalSpent", item.getPrice() != null ? item.getPrice().doubleValue() : 0.0);
                    return map;
                })
                .collect(Collectors.toList());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            e.printStackTrace(); // Logs full stack trace in backend logs
            return ResponseEntity.status(500).body("Internal Server Error: " + e.getMessage());
        }
    }
}