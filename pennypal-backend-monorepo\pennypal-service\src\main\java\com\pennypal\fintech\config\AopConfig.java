package com.pennypal.fintech.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * Configuration class to enable AspectJ auto proxy for AOP functionality
 */
@Configuration
@EnableAspectJAutoProxy
public class AopConfig {
    // This class enables AOP functionality in the application
    // @EnableAspectJAutoProxy creates proxies for beans that have aspects applied to them
}