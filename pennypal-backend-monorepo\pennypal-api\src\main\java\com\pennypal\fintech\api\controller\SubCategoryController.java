package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.SubCategoryDto;
import com.pennypal.fintech.entity.SubCategory;
import com.pennypal.fintech.service.SubCategoryService;
import com.pennypal.fintech.dto.SubCategoryIconDto;
import com.pennypal.fintech.repository.SubCategoryRepository;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.multipart.MultipartFile;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.Base64;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1/subCategory")
@Tag(name = "SubCategory Management", description = "APIs for managing transaction subcategories and their icons")
public class SubCategoryController {

    @Autowired
    private SubCategoryService subCategoryService;

    @Autowired
    private SubCategoryRepository subCategoryRepository;

    @PostMapping
    @Operation(summary = "Create a new subcategory",
               description = "Creates a new transaction subcategory")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "201",
            description = "Subcategory created successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = SubCategory.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid subcategory data",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<SubCategory> createSubCategory(
            @Parameter(description = "Subcategory data to create", required = true)
            @RequestBody SubCategoryDto subCategoryDto) {
        SubCategory subCategory = subCategoryService.saveSubCategory(subCategoryDto);
        return new ResponseEntity<>(subCategory, HttpStatus.CREATED);
    }

    @GetMapping("/all")
    @Operation(summary = "Get all subcategories",
               description = "Retrieves all available transaction subcategories")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved all subcategories",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of subcategory DTOs")
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<List<SubCategoryDto>> getAllSubCategories() {
        List<SubCategoryDto> subCategories = subCategoryService.getAllSubCategoriesDto();
        return ResponseEntity.ok(subCategories);
    }

    // Alternatively, if you want the default GET ("/api/subcategories") to return all, you can do:
    // @GetMapping
    // public List<SubCategory> getAllSubCategories() {
    //     return subCategoryService.getAllSubCategories();
    // }

    // Get a SubCategory by ID
    @Operation(summary = "Get subcategory by ID",
               description = "Retrieves a specific transaction subcategory by its unique identifier")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved the subcategory",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = SubCategory.class)
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Subcategory not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/{id}")
    public ResponseEntity<SubCategory> getSubCategoryById(
        @Parameter(description = "Unique identifier of the subcategory", required = true)
        @PathVariable int id) {
        Optional<SubCategory> subCategory = subCategoryService.getSubCategoryById(id);
        return subCategory.map(ResponseEntity::ok)
                .orElseGet(() -> ResponseEntity.status(HttpStatus.NOT_FOUND).build());
    }

    // Delete a SubCategory by ID
    @Operation(summary = "Delete subcategory by ID",
               description = "Deletes a transaction subcategory by its unique identifier")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "204",
            description = "Subcategory deleted successfully",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Subcategory not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteSubCategory(
        @Parameter(description = "Unique identifier of the subcategory to delete", required = true)
        @PathVariable int id) {
        subCategoryService.deleteSubCategory(id);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    // Optional: Get SubCategories by Category ID
    @Operation(summary = "Get subcategories by category ID",
               description = "Retrieves all transaction subcategories for a specific category")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved subcategories",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of subcategories")
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/byCategory")
    public ResponseEntity<List<SubCategory>> getSubCategoriesByCategory(
        @Parameter(description = "ID of the category to get subcategories for", required = true)
        @RequestParam int categoryId) {
        List<SubCategory> subCategories = subCategoryService.getSubCategoriesByCategoryId(categoryId);
        return ResponseEntity.ok(subCategories);
    }

    // Get distinct SubCategories by User ID
    @Operation(summary = "Get distinct subcategories by user ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved distinct subcategories"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/byUser/distinct/{userId}")
    public ResponseEntity<List<Map<String, Object>>> getDistinctSubCategoriesByUser(
        @Parameter(description = "ID of the user to get distinct subcategories for", required = true)
        @PathVariable Integer userId) {
        try {
            List<Map<String, Object>> subCategories = subCategoryService.getDistinctSubCategoriesByUserId(userId);
            return ResponseEntity.ok(subCategories);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @PutMapping("/{id}/icon")
    @Operation(summary = "Update subcategory icon",
               description = "Updates the icon for a specific subcategory")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully updated subcategory icon"),
        @ApiResponse(responseCode = "404", description = "Subcategory not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<String> uploadSubCategoryIcon(
        @Parameter(description = "Unique identifier of the subcategory", required = true)
        @PathVariable int id,
        @Parameter(description = "Icon file to update", required = true)
        @RequestParam("file") MultipartFile file) {
        try {
            subCategoryService.saveSubCategoryIconBlob(id, file);
            return ResponseEntity.ok("Icon uploaded successfully");
        } catch (Exception e) {
            e.printStackTrace(); // Show exact error in logs
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error uploading icon: " + e.getMessage());
        }
    }

    @Operation(summary = "Get subcategory icon by ID",
               description = "Retrieves the icon for a specific subcategory")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved subcategory icon"),
        @ApiResponse(responseCode = "404", description = "Subcategory not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/{id}/icon")
    public ResponseEntity<byte[]> getSubCategoryIcon(
        @Parameter(description = "Unique identifier of the subcategory", required = true)
        @PathVariable int id) {
        System.out.println("GET /subcategory/" + id + "/icon");

        Optional<SubCategory> subCategoryOpt = subCategoryService.getSubCategoryById(id);
        
        if (subCategoryOpt.isPresent()) {
            SubCategory subCategory = subCategoryOpt.get();
            System.out.println("Found subcategory with ID: " + id);

            byte[] iconBlob = subCategory.getIconBlob();
            if (iconBlob != null) {
                System.out.println("Icon blob size: " + iconBlob.length);

                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.IMAGE_PNG);
                headers.setContentLength(iconBlob.length); //  Important

                return new ResponseEntity<>(iconBlob, headers, HttpStatus.OK);
            } else {
                System.out.println("Subcategory has no icon blob");
            }
        } else {
            System.out.println("Subcategory not found with ID: " + id);
        }

        return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
    }

    @Operation(summary = "Get all subcategory icons",
               description = "Retrieves all available subcategory icons")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved all subcategory icons",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of subcategory icon DTOs")
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/icons")
    public ResponseEntity<List<SubCategoryIconDto>> getAllSubCategoryIcons() {
        System.out.println("Fetching all subcategory icons...");

        List<SubCategory> subCategories = subCategoryService.findAllWithIcons();

        List<SubCategoryIconDto> iconDtos = subCategories.stream()
            .filter(sc -> sc.getIconBlob() != null)
            .map(sc -> {
                String base64 = Base64.getEncoder().encodeToString(sc.getIconBlob());
                return new SubCategoryIconDto(
                    sc.getId(),
                    sc.getSubCategory(),
                    base64
                );
            })
            .collect(Collectors.toList());

        return ResponseEntity.ok(iconDtos);
    }
}