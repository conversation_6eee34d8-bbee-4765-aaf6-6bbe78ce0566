package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.entity.Reconcile;
import com.pennypal.fintech.repository.ReconcileRepository;
import com.pennypal.fintech.service.ReconcileService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1/reconcile")
@Tag(name = "Transaction Reconciliation", description = "APIs for managing transaction reconciliation and matching")
public class ReconciliationController {
    @Autowired
    private ReconcileRepository reconcileRepository;

    @Autowired 
    private ReconcileService reconcileService;

    @GetMapping("/all")
    @Operation(summary = "Get all reconciled transactions",
               description = "Retrieves all transactions that have been reconciled and are not removed")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved reconciled transactions",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of reconciled transactions")
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<List<Reconcile>> getAllReconciledTransactions() {
        List<Reconcile> reconciles = reconcileRepository.findAllByRemovedFalse();
        return ResponseEntity.ok(reconciles);
    }

    
    @GetMapping("/by-reconcile-id/{reconcileId}")
    @Operation(summary = "Get reconciled transactions by reconcile ID",
               description = "Retrieves all transactions that match a specific reconcile ID")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved reconciled transactions",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of reconciled transactions")
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "No transactions found for the given reconcile ID",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    public ResponseEntity<List<Reconcile>> getReconciledByReconcileId(
            @Parameter(description = "Reconcile ID to search for", required = true)
            @PathVariable String reconcileId) {
        System.out.println("Fetching reconciled transactions for: " + reconcileId);
        List<Reconcile> reconciled = reconcileRepository.findByReconcileId(reconcileId);
        System.out.println("Found " + reconciled.size() + " matches");
        return ResponseEntity.ok(reconciled);
    }

 @Operation(summary = "Confirm reconciliation",
               description = "Confirms the reconciliation of a list of transactions")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully confirmed reconciliation",
            content = @Content(
                mediaType = "text/plain"
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid transaction IDs provided",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @PostMapping("/confirm")
    public ResponseEntity<?> confirmReconciliation(
        @Parameter(description = "List of transaction IDs to confirm reconciliation", required = true)
        @RequestBody List<Long> txnIds) {
        List<Integer> intIds = txnIds.stream()
                                    .map(Long::intValue)
                                    .collect(Collectors.toList());

        reconcileService.confirmReconciliation(intIds);
        return ResponseEntity.ok("Reconciliation confirmed");
    }

@Operation(summary = "Get reconciled transactions",
               description = "Retrieves all transactions that have been reconciled")
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved reconciled transactions",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(description = "List of reconciled transactions")
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content
        )
    })
    @GetMapping("/reconciled")
    public List<Reconcile> getReconciledTransactions() {
        return reconcileService.getReconciledTransactions();
    }
}