// package com.pennypal.fintech.service;

// import com.pennypal.fintech.dto.GoalDto;
// import com.pennypal.fintech.entity.Accounts;
// import com.pennypal.fintech.entity.Goal;
// import com.pennypal.fintech.entity.GoalAccount;
// import com.pennypal.fintech.entity.Users;
// import com.pennypal.fintech.repository.AccountRepository;
// import com.pennypal.fintech.repository.GoalAccountRepository;
// import com.pennypal.fintech.repository.GoalRepository;
// import com.pennypal.fintech.repository.UserRepository;
// import com.pennypal.fintech.exception.ResourceNotFoundException;
// import com.pennypal.fintech.exception.InvalidRequestException;

// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Service;
// import org.springframework.transaction.annotation.Transactional;

// import java.time.LocalDate;
// import java.time.Period;
// import java.time.YearMonth;
// import java.util.ArrayList;
// import java.util.List;
// import java.util.stream.Collectors;

// @Service
// public class GoalService {

//     private final GoalRepository goalRepository;
//     private final GoalAccountRepository goalAccountRepository;
//     private final UserRepository userRepository;
//     private final AccountRepository accountRepository;

//     @Autowired
//     public GoalService(
//             GoalRepository goalRepository,
//             GoalAccountRepository goalAccountRepository,
//             UserRepository userRepository,
//             AccountRepository accountRepository) {
//         this.goalRepository = goalRepository;
//         this.goalAccountRepository = goalAccountRepository;
//         this.userRepository = userRepository;
//         this.accountRepository = accountRepository;
//     }

//     @Transactional
//     public GoalDto.GoalResponse createGoal(int userId, GoalDto.CreateGoalRequest request) {
//         // Validate user
//         Users user = userRepository.findById(userId)
//                 .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

//         // Validate request
//         validateGoalRequest(request);

//         // Create goal entity
//         Goal goal = new Goal();
//         goal.setGoalName(request.getGoalName());
//         goal.setGoalAmount(request.getGoalAmount());
//         goal.setStartDate(request.getStartDate());
//         goal.setTargetDate(request.getTargetDate());
//         goal.setGoalType(request.getGoalType());
//         goal.setDescription(request.getDescription());
//         goal.setCurrentAmount(0.0);
//         goal.setUser(user);
//         goal.setStatus(Goal.GoalStatus.IN_PROGRESS);

//         // Save goal
//         Goal savedGoal = goalRepository.save(goal);

//         // Create goal accounts
//         List<GoalAccount> goalAccounts = new ArrayList<>();
        
//         if (request.getAccountAllocations() != null && !request.getAccountAllocations().isEmpty()) {
//             // Validate total allocation percentage
//             double totalAllocationPercentage = request.getAccountAllocations().stream()
//                     .mapToDouble(GoalDto.GoalAccountAllocation::getAllocationPercentage)
//                     .sum();
            
//             if (Math.abs(totalAllocationPercentage - 100.0) > 0.01) {
//                 throw new InvalidRequestException("Total allocation percentage must be 100%");
//             }

//             for (GoalDto.GoalAccountAllocation allocation : request.getAccountAllocations()) {
//                 // Validate account
//                 Accounts account = accountRepository.findById(allocation.getAccountId())
//                         .orElseThrow(() -> new ResourceNotFoundException("Account not found with id: " + allocation.getAccountId()));
                
//                 // Check if account belongs to user
//                 if (account.getUser().getId() != userId) {
//                     throw new InvalidRequestException("Account does not belong to user");
//                 }

//                 GoalAccount goalAccount = new GoalAccount();
//                 goalAccount.setGoal(savedGoal);
//                 goalAccount.setAccount(account);
//                 goalAccount.setAllocationPercentage(allocation.getAllocationPercentage());
//                 goalAccount.setAllocatedAmount((allocation.getAllocationPercentage() / 100) * request.getGoalAmount());
//                 goalAccount.setCurrentContribution(0.0);
                
//                 goalAccounts.add(goalAccountRepository.save(goalAccount));
//             }
//         }

//         return mapToGoalResponse(savedGoal, goalAccounts);
//     }

//     @Transactional(readOnly = true)
//     public GoalDto.GoalResponse getGoal(int userId, int goalId) {
//         // Validate user
//         Users user = userRepository.findById(userId)
//                 .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

//         // Get goal
//         Goal goal = goalRepository.findById(goalId)
//                 .orElseThrow(() -> new ResourceNotFoundException("Goal not found with id: " + goalId));

//         // Check if goal belongs to user
//         if (goal.getUser().getId() != userId) {
//             throw new ResourceNotFoundException("Goal not found with id: " + goalId);
//         }

//         // Get goal accounts
//         List<GoalAccount> goalAccounts = goalAccountRepository.findByGoal(goal);

//         return mapToGoalResponse(goal, goalAccounts);
//     }

//     @Transactional(readOnly = true)
//     public List<GoalDto.GoalResponse> getAllGoals(int userId) {
//         // Validate user
//         Users user = userRepository.findById(userId)
//                 .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

//         // Get all goals for user
//         List<Goal> goals = goalRepository.findByUser(user);

//         return goals.stream()
//                 .map(goal -> {
//                     List<GoalAccount> goalAccounts = goalAccountRepository.findByGoal(goal);
//                     return mapToGoalResponse(goal, goalAccounts);
//                 })
//                 .collect(Collectors.toList());
//     }

//     @Transactional
//     public GoalDto.GoalResponse updateGoal(int userId, int goalId, GoalDto.UpdateGoalRequest request) {
//         // Validate user
//         Users user = userRepository.findById(userId)
//                 .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
    
//         // Get goal
//         Goal goal = goalRepository.findById(goalId)
//                 .orElseThrow(() -> new ResourceNotFoundException("Goal not found with id: " + goalId));
    
//         // Check if goal belongs to user
//         if (goal.getUser().getId() != userId) {
//             throw new ResourceNotFoundException("Goal not found with id: " + goalId);
//         }
    
//         // Update goal fields if provided
//         if (request.getGoalName() != null) {
//             goal.setGoalName(request.getGoalName());
//         }
        
//         // Handle goal amount update carefully - need to recalculate allocations
//         boolean goalAmountChanged = request.getGoalAmount() != null && 
//                                    Math.abs(request.getGoalAmount() - goal.getGoalAmount()) > 0.01;
        
//         if (goalAmountChanged) {
//             goal.setGoalAmount(request.getGoalAmount());
//         }
        
//         if (request.getTargetDate() != null) {
//             goal.setTargetDate(request.getTargetDate());
//         }
        
//         if (request.getGoalType() != null) {
//             goal.setGoalType(request.getGoalType());
//         }
        
//         if (request.getDescription() != null) {
//             goal.setDescription(request.getDescription());
//         }
    
//         // Update goal status based on current progress and dates
//         goal.updateStatus();
        
//         // Save updated goal
//         Goal updatedGoal = goalRepository.save(goal);
    
//         // Get existing goal accounts before modification
//         List<GoalAccount> existingGoalAccounts = goalAccountRepository.findByGoal(goal);
        
//         // Handle account allocations if provided
//         if (request.getAccountAllocations() != null && !request.getAccountAllocations().isEmpty()) {
//             // Validate total allocation percentage
//             double totalAllocationPercentage = request.getAccountAllocations().stream()
//                     .mapToDouble(GoalDto.GoalAccountAllocation::getAllocationPercentage)
//                     .sum();
            
//             if (Math.abs(totalAllocationPercentage - 100.0) > 0.01) {
//                 throw new InvalidRequestException("Total allocation percentage must be 100%");
//             }
    
//             // Remove existing allocations
//             for (GoalAccount ga : existingGoalAccounts) {
//                 goalAccountRepository.delete(ga);
//             }
            
//             // Create new allocations
//             List<GoalAccount> newGoalAccounts = new ArrayList<>();
//             for (GoalDto.GoalAccountAllocation allocation : request.getAccountAllocations()) {
//                 // Validate account
//                 Accounts account = accountRepository.findById(allocation.getAccountId())
//                         .orElseThrow(() -> new ResourceNotFoundException("Account not found with id: " + allocation.getAccountId()));
                
//                 // Check if account belongs to user
//                 if (account.getUser().getId() != userId) {
//                     throw new InvalidRequestException("Account does not belong to user");
//                 }
    
//                 GoalAccount goalAccount = new GoalAccount();
//                 goalAccount.setGoal(updatedGoal);
//                 goalAccount.setAccount(account);
//                 goalAccount.setAllocationPercentage(allocation.getAllocationPercentage());
//                 goalAccount.setAllocatedAmount((allocation.getAllocationPercentage() / 100) * updatedGoal.getGoalAmount());
//                 goalAccount.setCurrentContribution(0.0); // Reset contribution when reallocating
                
//                 newGoalAccounts.add(goalAccountRepository.save(goalAccount));
//             }
            
//             // Return with new allocations
//             return mapToGoalResponse(updatedGoal, newGoalAccounts);
//         } else if (goalAmountChanged) {
//             // If only the goal amount changed, update allocated amounts proportionally
//             for (GoalAccount ga : existingGoalAccounts) {
//                 ga.setAllocatedAmount((ga.getAllocationPercentage() / 100) * updatedGoal.getGoalAmount());
//                 goalAccountRepository.save(ga);
//             }
//         }
    
//         // Return with updated/existing allocations
//         return mapToGoalResponse(updatedGoal, goalAccountRepository.findByGoal(updatedGoal));
//     }
// @Transactional
// public GoalDto.GoalResponse contributeToGoal(int userId, GoalDto.ContributeToGoalRequest request) {
//     // Validate user
//     Users user = userRepository.findById(userId)
//             .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

//     // Validate request fields
//     if (request.getGoalId() == null) {
//         throw new InvalidRequestException("Goal ID is required");
//     }
    
//     if (request.getAccountId() == null) {
//         throw new InvalidRequestException("Account ID is required");
//     }
    
//     if (request.getAmount() == null || request.getAmount() <= 0) {
//         throw new InvalidRequestException("Contribution amount must be positive");
//     }

//     // Get goal
//     Goal goal = goalRepository.findById(request.getGoalId())
//             .orElseThrow(() -> new ResourceNotFoundException("Goal not found with id: " + request.getGoalId()));

//     // Check if goal belongs to user
//     if (goal.getUser().getId() != userId) {
//         throw new ResourceNotFoundException("Goal not found with id: " + request.getGoalId());
//     }
    
//     // Check if goal is still active
//     if (goal.getStatus() == Goal.GoalStatus.COMPLETED) {
//         throw new InvalidRequestException("Cannot contribute to a completed goal");
//     }
    
//     if (goal.getStatus() == Goal.GoalStatus.FAILED) {
//         throw new InvalidRequestException("Cannot contribute to a failed goal");
//     }

//     // Get account
//     Accounts account = accountRepository.findById(request.getAccountId())
//             .orElseThrow(() -> new ResourceNotFoundException("Account not found with id: " + request.getAccountId()));

//     // Check if account belongs to user
//     if (account.getUser().getId() != userId) {
//         throw new InvalidRequestException("Account does not belong to user");
//     }

//     // Check if contribution would exceed the goal amount
//     double newGoalAmount = goal.getCurrentAmount() + request.getAmount();
//     if (newGoalAmount > goal.getGoalAmount()) {
//         throw new InvalidRequestException(
//             String.format("Contribution of %.2f would exceed goal amount of %.2f. Current amount: %.2f, remaining: %.2f", 
//                          request.getAmount(), goal.getGoalAmount(), goal.getCurrentAmount(), 
//                          goal.getGoalAmount() - goal.getCurrentAmount()));
//     }

//     // Update goal current amount
//     goal.setCurrentAmount(newGoalAmount);
    
//     // Check if goal is completed
//     if (goal.getCurrentAmount() >= goal.getGoalAmount()) {
//         goal.setStatus(Goal.GoalStatus.COMPLETED);
//     }
    
//     Goal updatedGoal = goalRepository.save(goal);

//     // Get goal accounts for response (if any exist)
//     List<GoalAccount> goalAccounts = goalAccountRepository.findByGoal(updatedGoal);

//     return mapToGoalResponse(updatedGoal, goalAccounts);
// }
// @Transactional(readOnly = true)
//     public GoalDto.GoalSummaryResponse getGoalsSummary(int userId) {
//         // Validate user
//         Users user = userRepository.findById(userId)
//                 .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

//         // Get goal counts
//         int totalGoals = goalRepository.findByUser(user).size();
//         int completedGoals = goalRepository.countByUserAndStatus(user, Goal.GoalStatus.COMPLETED);
//         int failedGoals = goalRepository.countByUserAndStatus(user, Goal.GoalStatus.FAILED);
//         int inProgressGoals = goalRepository.countByUserAndStatus(user, Goal.GoalStatus.IN_PROGRESS);

//         // Get upcoming goals (targets within next 3 months)
//         LocalDate today = LocalDate.now();
//         LocalDate threeMonthsLater = today.plusMonths(3);
        
//         List<Goal> upcomingGoals = goalRepository.findByUserAndStatus(user, Goal.GoalStatus.IN_PROGRESS).stream()
//                 .filter(goal -> !goal.getTargetDate().isBefore(today) && !goal.getTargetDate().isAfter(threeMonthsLater))
//                 .sorted((g1, g2) -> g1.getTargetDate().compareTo(g2.getTargetDate()))
//                 .limit(5)
//                 .collect(Collectors.toList());

//         List<GoalDto.GoalResponse> upcomingGoalResponses = upcomingGoals.stream()
//                 .map(goal -> mapToGoalResponse(goal, goalAccountRepository.findByGoal(goal)))
//                 .collect(Collectors.toList());

//         return GoalDto.GoalSummaryResponse.builder()
//                 .totalGoals(totalGoals)
//                 .completedGoals(completedGoals)
//                 .inProgressGoals(inProgressGoals)
//                 .failedGoals(failedGoals)
//                 .upcomingGoals(upcomingGoalResponses)
//                 .build();
//     }

//     @Transactional(readOnly = true)
//     public List<GoalDto.GoalResponse> getGoalsForMonth(int userId, int year, int month) {
//         // Validate user
//         Users user = userRepository.findById(userId)
//                 .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

//         // Validate year and month
//         if (year < 2000 || year > 2100 || month < 1 || month > 12) {
//             throw new InvalidRequestException("Invalid year or month");
//         }

//         // Get start and end of month
//         YearMonth yearMonth = YearMonth.of(year, month);
//         LocalDate startOfMonth = yearMonth.atDay(1);
//         LocalDate endOfMonth = yearMonth.atEndOfMonth();

//         // Get goals with target date in the month
//         List<Goal> goalsInMonth = goalRepository.findGoalsWithTargetDateInMonth(user, startOfMonth, endOfMonth);

//         return goalsInMonth.stream()
//                 .map(goal -> {
//                     GoalDto.GoalResponse response = mapToGoalResponse(goal, goalAccountRepository.findByGoal(goal));
                    
//                     // Add flag for goals completed this month
//                     if (goal.getStatus() == Goal.GoalStatus.COMPLETED && 
//                         goal.getUpdatedAt() != null &&
//                         goal.getUpdatedAt().getMonth().getValue() == month && 
//                         goal.getUpdatedAt().getYear() == year) {
//                         response.setIsCompletedThisMonth(true);
//                     } else {
//                         response.setIsCompletedThisMonth(false);
//                     }
                    
//                     return response;
//                 })
//                 .collect(Collectors.toList());
//     }

//     @Transactional
//     public GoalDto.GoalResponse createNextGoal(int userId, int previousGoalId, GoalDto.CreateGoalRequest request) {
//         // Validate user
//         Users user = userRepository.findById(userId)
//                 .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

//         // Get previous goal
//         Goal previousGoal = goalRepository.findById(previousGoalId)
//                 .orElseThrow(() -> new ResourceNotFoundException("Previous goal not found with id: " + previousGoalId));

//         // Check if previous goal belongs to user
//         if (previousGoal.getUser().getId() != userId) {
//             throw new ResourceNotFoundException("Previous goal not found with id: " + previousGoalId);
//         }

//         // Validate request
//         validateGoalRequest(request);

//         // Create new goal based on previous goal
//         Goal goal = new Goal();
//         goal.setGoalName(request.getGoalName());
//         goal.setGoalAmount(request.getGoalAmount());
//         goal.setStartDate(request.getStartDate());
//         goal.setTargetDate(request.getTargetDate());
//         goal.setGoalType(request.getGoalType());
//         goal.setDescription(request.getDescription());
//         goal.setCurrentAmount(0.0);
//         goal.setUser(user);
//         goal.setStatus(Goal.GoalStatus.IN_PROGRESS);

//         // Save goal
//         Goal savedGoal = goalRepository.save(goal);

//         // Create goal accounts based on request or previous goal's allocations
//         List<GoalAccount> goalAccounts = new ArrayList<>();
        
//         if (request.getAccountAllocations() != null && !request.getAccountAllocations().isEmpty()) {
//             // Use provided allocations
//             double totalAllocationPercentage = request.getAccountAllocations().stream()
//                     .mapToDouble(GoalDto.GoalAccountAllocation::getAllocationPercentage)
//                     .sum();
            
//             if (Math.abs(totalAllocationPercentage - 100.0) > 0.01) {
//                 throw new InvalidRequestException("Total allocation percentage must be 100%");
//             }

//             for (GoalDto.GoalAccountAllocation allocation : request.getAccountAllocations()) {
//                 Accounts account = accountRepository.findById(allocation.getAccountId())
//                         .orElseThrow(() -> new ResourceNotFoundException("Account not found with id: " + allocation.getAccountId()));
                
//                 if (account.getUser().getId() != userId) {
//                     throw new InvalidRequestException("Account does not belong to user");
//                 }

//                 GoalAccount goalAccount = new GoalAccount();
//                 goalAccount.setGoal(savedGoal);
//                 goalAccount.setAccount(account);
//                 goalAccount.setAllocationPercentage(allocation.getAllocationPercentage());
//                 goalAccount.setAllocatedAmount((allocation.getAllocationPercentage() / 100) * request.getGoalAmount());
//                 goalAccount.setCurrentContribution(0.0);
                
//                 goalAccounts.add(goalAccountRepository.save(goalAccount));
//             }
//         } else {
//             // Use previous goal's allocations
//             List<GoalAccount> previousGoalAccounts = goalAccountRepository.findByGoal(previousGoal);
            
//             for (GoalAccount previousGa : previousGoalAccounts) {
//                 Accounts account = previousGa.getAccount();
                
//                 // Ensure account still exists and belongs to user
//                 if (account != null && account.getUser().getId() == userId) {
//                     GoalAccount goalAccount = new GoalAccount();
//                     goalAccount.setGoal(savedGoal);
//                     goalAccount.setAccount(account);
//                     goalAccount.setAllocationPercentage(previousGa.getAllocationPercentage());
//                     goalAccount.setAllocatedAmount((previousGa.getAllocationPercentage() / 100) * request.getGoalAmount());
//                     goalAccount.setCurrentContribution(0.0);
                    
//                     goalAccounts.add(goalAccountRepository.save(goalAccount));
//                 }
//             }
//         }

//         return mapToGoalResponse(savedGoal, goalAccounts);
//     }

//     // Helper methods
//     private void validateGoalRequest(GoalDto.CreateGoalRequest request) {
//         // Validate required fields
//         if (request.getGoalName() == null || request.getGoalName().trim().isEmpty()) {
//             throw new InvalidRequestException("Goal name is required");
//         }
        
//         if (request.getGoalAmount() == null || request.getGoalAmount() <= 0) {
//             throw new InvalidRequestException("Goal amount must be positive");
//         }
        
//         if (request.getStartDate() == null) {
//             throw new InvalidRequestException("Start date is required");
//         }
        
//         if (request.getTargetDate() == null) {
//             throw new InvalidRequestException("Target date is required");
//         }
        
//         if (request.getTargetDate().isBefore(request.getStartDate())) {
//             throw new InvalidRequestException("Target date must be after start date");
//         }
        
//         if (request.getGoalType() == null) {
//             throw new InvalidRequestException("Goal type is required");
//         }
//     }

//     private GoalDto.GoalResponse mapToGoalResponse(Goal goal, List<GoalAccount> goalAccounts) {
//         // Calculate progress percentage
//         double progressPercentage = 0.0;
//         if (goal.getGoalAmount() > 0) {
//             progressPercentage = (goal.getCurrentAmount() / goal.getGoalAmount()) * 100;
//         }
        
//         // Format time remaining
//         String timeRemaining = calculateTimeRemaining(goal.getTargetDate());
        
//         // Map account responses
//         List<GoalDto.GoalAccountResponse> accountResponses = goalAccounts.stream()
//                 .map(ga -> GoalDto.GoalAccountResponse.builder()
//                         .accountId(ga.getAccount().getId())
//                         .accountName(ga.getAccount().getAccountName())
//                         .allocationPercentage(ga.getAllocationPercentage())
//                         .allocatedAmount(ga.getAllocatedAmount())
//                         .currentContribution(ga.getCurrentContribution())
//                         .progressPercentage(ga.getProgressPercentage())
//                         .build())
//                 .collect(Collectors.toList());
        
//         // Build response
//         return GoalDto.GoalResponse.builder()
//                 .id(goal.getId())
//                 .goalName(goal.getGoalName())
//                 .goalAmount(goal.getGoalAmount())
//                 .currentAmount(goal.getCurrentAmount())
//                 .progressPercentage(progressPercentage)
//                 .startDate(goal.getStartDate())
//                 .targetDate(goal.getTargetDate())
//                 .status(goal.getStatus())
//                 .goalType(goal.getGoalType())
//                 .description(goal.getDescription())
//                 .accounts(accountResponses)
//                 .timeRemaining(timeRemaining)
//                 .isCompletedThisMonth(isCompletedThisMonth(goal))
//                 .build();
//     }

//     private String calculateTimeRemaining(LocalDate targetDate) {
//         LocalDate today = LocalDate.now();
        
//         if (today.isAfter(targetDate)) {
//             return "Overdue";
//         }
        
//         Period period = Period.between(today, targetDate);
//         int years = period.getYears();
//         int months = period.getMonths();
//         int days = period.getDays();
        
//         StringBuilder sb = new StringBuilder();
        
//         if (years > 0) {
//             sb.append(years).append(years == 1 ? " year" : " years");
//             if (months > 0 || days > 0) {
//                 sb.append(", ");
//             }
//         }
        
//         if (months > 0) {
//             sb.append(months).append(months == 1 ? " month" : " months");
//             if (days > 0) {
//                 sb.append(", ");
//             }
//         }
        
//         if (days > 0 || (years == 0 && months == 0)) {
//             sb.append(days).append(days == 1 ? " day" : " days");
//         }
        
//         return sb.toString();
//     }

//     private boolean isCompletedThisMonth(Goal goal) {
//         if (goal.getStatus() != Goal.GoalStatus.COMPLETED || goal.getUpdatedAt() == null) {
//             return false;
//         }
        
//         LocalDate now = LocalDate.now();
//         return goal.getUpdatedAt().getMonth().getValue() == now.getMonth().getValue() && 
//                goal.getUpdatedAt().getYear() == now.getYear();
//     }
// }
package com.pennypal.fintech.service;

import com.pennypal.fintech.dto.GoalDto;
import com.pennypal.fintech.entity.Accounts;
import com.pennypal.fintech.entity.Goal;
import com.pennypal.fintech.entity.GoalAccount;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.repository.AccountRepository;
import com.pennypal.fintech.repository.GoalAccountRepository;
import com.pennypal.fintech.repository.GoalRepository;
import com.pennypal.fintech.repository.UserRepository;
import com.pennypal.fintech.exception.ResourceNotFoundException;
import com.pennypal.fintech.exception.InvalidRequestException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class GoalService {

    private final GoalRepository goalRepository;
    private final GoalAccountRepository goalAccountRepository;
    private final UserRepository userRepository;
    private final AccountRepository accountRepository;
    private final CacheManager cacheManager;

    @Autowired
    public GoalService(
            GoalRepository goalRepository,
            GoalAccountRepository goalAccountRepository,
            UserRepository userRepository,
            AccountRepository accountRepository,
            CacheManager cacheManager) {
        this.goalRepository = goalRepository;
        this.goalAccountRepository = goalAccountRepository;
        this.userRepository = userRepository;
        this.accountRepository = accountRepository;
        this.cacheManager = cacheManager;
    }

    @Transactional
    public GoalDto.GoalResponse createGoal(int userId, GoalDto.CreateGoalRequest request) {
        // Validate user
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        // Validate request
        validateGoalRequest(request);

        // Create goal entity
        Goal goal = new Goal();
        goal.setGoalName(request.getGoalName());
        goal.setGoalAmount(request.getGoalAmount());
        goal.setStartDate(request.getStartDate());
        goal.setTargetDate(request.getTargetDate());
        goal.setGoalType(request.getGoalType());
        goal.setDescription(request.getDescription());
        goal.setCurrentAmount(0.0);
        goal.setUser(user);
        goal.setStatus(Goal.GoalStatus.IN_PROGRESS);
        goal.setCreatedAt(LocalDateTime.now());
        goal.setUpdatedAt(LocalDateTime.now());

        // Save goal
        Goal savedGoal = goalRepository.save(goal);

        // Create goal accounts with initial contributions
        List<GoalAccount> goalAccounts = createGoalAccountsWithInitialContributions(
                savedGoal, request.getInitialContributions(), userId);

        // Update goal current amount based on initial contributions
        double totalInitialContribution = goalAccounts.stream()
                .mapToDouble(GoalAccount::getCurrentContribution)
                .sum();
        
        if (totalInitialContribution > 0) {
            savedGoal.setCurrentAmount(totalInitialContribution);
            
            // Check if goal is already completed with initial contribution
            if (savedGoal.getCurrentAmount() >= savedGoal.getGoalAmount()) {
                savedGoal.setStatus(Goal.GoalStatus.COMPLETED);
            }
            
            goalRepository.save(savedGoal);
        }

        // Clear goal cache
        clearGoalRelatedCache(userId);

        return mapToGoalResponse(savedGoal, goalAccounts);
    }

    @Transactional
    public GoalDto.GoalResponse updateGoal(int userId, int goalId, GoalDto.UpdateGoalRequest request) {
        // Validate user
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        // Validate goal
        Goal goal = goalRepository.findById(goalId)
                .orElseThrow(() -> new ResourceNotFoundException("Goal not found with id: " + goalId));

        // Ensure goal belongs to current user
        if (!goal.getUser().getId().equals(userId)) {
            throw new InvalidRequestException("Goal does not belong to current user");
        }

        // Update goal fields
        goal.setGoalName(request.getGoalName());
        goal.setGoalAmount(request.getGoalAmount());
        goal.setStartDate(LocalDate.parse(request.getStartDate()));
        goal.setTargetDate(request.getTargetDate());
        goal.setGoalType(request.getGoalType());
        goal.setDescription(request.getDescription());
        goal.setUpdatedAt(LocalDateTime.now());

        // Handle contributions if present
        if (request.getInitialContributions() != null && !request.getInitialContributions().isEmpty()) {
            List<GoalAccount> goalAccounts = goalAccountRepository.findByGoalId(goalId);

            for (GoalDto.InitialContributionRequest contrib : request.getInitialContributions()) {
                for (GoalAccount account : goalAccounts) {
                    if (account.getAccount().getId() == contrib.getAccountId()) {
                        account.setCurrentContribution(contrib.getAmount());
                        account.setGoalAccountBalance(contrib.getAmount());
                        account.setAllocatedAmount(contrib.getAmount());  // make sure this is also updated
                        account.setInitialAllocatedBalance(contrib.getAmount());
                        break;
                    }
                }
            }

            goalAccountRepository.saveAll(goalAccounts);

            // Update goal's total contribution
            double totalContribution = goalAccounts.stream()
                    .mapToDouble(GoalAccount::getCurrentContribution)
                    .sum();
            goal.setCurrentAmount(totalContribution);

            // Mark goal completed if it meets the target
            if (totalContribution >= goal.getGoalAmount()) {
                goal.setStatus(Goal.GoalStatus.COMPLETED);
            } else {
                goal.setStatus(Goal.GoalStatus.IN_PROGRESS);
            }
        }

        // Save updated goal
        Goal updatedGoal = goalRepository.save(goal);
        List<GoalAccount> updatedAccounts = goalAccountRepository.findByGoalId(goalId);

        // Clear goal cache
        clearGoalRelatedCache(userId);

        return mapToGoalResponse(updatedGoal, updatedAccounts);
    }

    private void updateGoalStatus(Goal goal) {
        if (goal.getCurrentAmount() >= goal.getGoalAmount()) {
            goal.setStatus(Goal.GoalStatus.COMPLETED);
        } else if (goal.getTargetDate().isBefore(LocalDate.now())) {
            goal.setStatus(Goal.GoalStatus.FAILED);
        } else {
            goal.setStatus(Goal.GoalStatus.IN_PROGRESS);
        }
    }    

    @Transactional
    public GoalDto.GoalResponse contributeToGoal(int userId, GoalDto.ContributeToGoalRequest request) {
        // Validate user
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        // Validate request fields
        validateContributeRequest(request);

        // Get goal
        Goal goal = goalRepository.findById(request.getGoalId())
                .orElseThrow(() -> new ResourceNotFoundException("Goal not found with id: " + request.getGoalId()));

        // Check if goal belongs to user
        if (goal.getUser().getId() != userId) {
            throw new ResourceNotFoundException("Goal not found with id: " + request.getGoalId());
        }
        
        // Check if goal is still active
        validateGoalStatus(goal);

        // Get account
        Accounts account = accountRepository.findById(request.getAccountId())
                .orElseThrow(() -> new ResourceNotFoundException("Account not found with id: " + request.getAccountId()));

        // Check if account belongs to user
        if (account.getUser().getId() != userId) {
            throw new InvalidRequestException("Account does not belong to user");
        }

        // Check account balance
        if (account.getBalance() < request.getAmount()) {
            throw new InvalidRequestException(
                String.format("Insufficient account balance. Available: %.2f, Requested: %.2f", 
                             account.getBalance(), request.getAmount()));
        }

        // Check if contribution would exceed the goal amount
        double newGoalAmount = goal.getCurrentAmount() + request.getAmount();
        if (newGoalAmount > goal.getGoalAmount()) {
            throw new InvalidRequestException(
                String.format("Contribution of %.2f would exceed goal amount of %.2f. Current amount: %.2f, remaining: %.2f", 
                             request.getAmount(), goal.getGoalAmount(), goal.getCurrentAmount(), 
                             goal.getGoalAmount() - goal.getCurrentAmount()));
        }

        // Process the contribution
        processContribution(goal, account, request.getAmount());

        // Get updated goal accounts for response
        List<GoalAccount> goalAccounts = goalAccountRepository.findByGoal(goal);

        // Clear goal cache
        clearGoalRelatedCache(userId);

        return mapToGoalResponse(goal, goalAccounts);
    }

    @Cacheable(value = "goalCache",
               key = "#root.methodName + '_' + #userId + '_' + #goalId",
               unless = "#result == null")
    @Transactional
    public GoalDto.GoalAccountBalanceResponse getGoalAccountBalance(int userId, int goalId) {
        // Validate user
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        // Get goal
        Goal goal = goalRepository.findById(goalId)
                .orElseThrow(() -> new ResourceNotFoundException("Goal not found with id: " + goalId));

        // Check if goal belongs to user
        if (goal.getUser().getId() != userId) {
            throw new ResourceNotFoundException("Goal not found with id: " + goalId);
        }

        // Get goal accounts
        List<GoalAccount> goalAccounts = goalAccountRepository.findByGoal(goal);

        // Calculate total goal account balance
        double totalGoalAccountBalance = goalAccounts.stream()
                .mapToDouble(GoalAccount::getGoalAccountBalance)
                .sum();

        // Map account balances
        List<GoalDto.GoalAccountBalanceDetail> accountBalances = goalAccounts.stream()
                .map(ga -> GoalDto.GoalAccountBalanceDetail.builder()
                        .accountId(ga.getAccount().getId())
                        .accountName(ga.getAccount().getAccountName())
                        .goalAccountBalance(ga.getGoalAccountBalance())
                        .originalAccountBalance(ga.getAccount().getBalance())
                        .allocationPercentage(ga.getAllocationPercentage())
                        .allocatedAmount(ga.getAllocatedAmount())
                        .currentContribution(ga.getCurrentContribution())
                        .build())
                .collect(Collectors.toList());

        return GoalDto.GoalAccountBalanceResponse.builder()
                .goalId(goalId)
                .goalName(goal.getGoalName())
                .totalGoalAccountBalance(totalGoalAccountBalance)
                .accountBalances(accountBalances)
                .build();
    }

    @Cacheable(value = "goalCache",
               key = "#root.methodName + '_' + #userId + '_' + #accountId",
               unless = "#result == null")
    @Transactional
    public GoalDto.AccountGoalSummaryResponse getAccountGoalSummary(int userId, int accountId) {
        // Validate user
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        // Get account
        Accounts account = accountRepository.findById(accountId)
                .orElseThrow(() -> new ResourceNotFoundException("Account not found with id: " + accountId));

        // Check if account belongs to user
        if (account.getUser().getId() != userId) {
            throw new InvalidRequestException("Account does not belong to user");
        }

        // Get all goal accounts for this account
        List<GoalAccount> goalAccounts = goalAccountRepository.findByAccount(account);

        // Calculate total allocated to goals
        double totalAllocatedToGoals = goalAccounts.stream()
                .mapToDouble(GoalAccount::getGoalAccountBalance)
                .sum();

        // Calculate available balance
        double availableBalance = account.getBalance() - totalAllocatedToGoals;

        // Map goal summaries
        List<GoalDto.GoalSummaryForAccount> goalSummaries = goalAccounts.stream()
                .map(ga -> GoalDto.GoalSummaryForAccount.builder()
                        .goalId(ga.getGoal().getId())
                        .goalName(ga.getGoal().getGoalName())
                        .goalStatus(ga.getGoal().getStatus())
                        .allocationPercentage(ga.getAllocationPercentage())
                        .allocatedAmount(ga.getAllocatedAmount())
                        .goalAccountBalance(ga.getGoalAccountBalance())
                        .currentContribution(ga.getCurrentContribution())
                        .build())
                .collect(Collectors.toList());

        return GoalDto.AccountGoalSummaryResponse.builder()
                .accountId(accountId)
                .accountName(account.getAccountName())
                .totalAccountBalance(account.getBalance())
                .totalAllocatedToGoals(totalAllocatedToGoals)
                .availableBalance(availableBalance)
                .goalSummaries(goalSummaries)
                .build();
    }


    private List<GoalAccount> createGoalAccountsWithInitialContributions(
        Goal goal, List<GoalDto.InitialContribution> initialContributions, int userId) {
        
        List<GoalAccount> goalAccounts = new ArrayList<>();

        if (initialContributions != null && !initialContributions.isEmpty()) {
            // Validate accounts and contributions first
            for (GoalDto.InitialContribution contribution : initialContributions) {
                Accounts account = accountRepository.findById(contribution.getAccountId())
                        .orElseThrow(() -> new ResourceNotFoundException("Account not found with id: " + contribution.getAccountId()));

                // Ensure account belongs to current user
                if (account.getUser().getId() != userId) {
                    throw new InvalidRequestException("Account does not belong to current user: " + contribution.getAccountId());
                }

                Double contributionAmount = contribution.getAmount() != null ? contribution.getAmount() : 0.0;

                if (contributionAmount < 0) {
                    throw new InvalidRequestException("Initial contribution cannot be negative for account: " + account.getAccountName());
                }

                if (contributionAmount > account.getBalance()) {
                    throw new InvalidRequestException(String.format(
                            "Insufficient balance in account '%s'. Available: %.2f, Required: %.2f",
                            account.getAccountName(), account.getBalance(), contributionAmount));
                }
            }

            // Process all valid contributions
            for (GoalDto.InitialContribution contribution : initialContributions) {
                Accounts account = accountRepository.findById(contribution.getAccountId()).get();

                double contributionAmount = contribution.getAmount() != null ? contribution.getAmount() : 0.0;

                GoalAccount goalAccount = new GoalAccount();
                goalAccount.setGoal(goal);
                goalAccount.setAccount(account);
                goalAccount.setAllocationPercentage(0.0); // No allocation percentage needed
                goalAccount.setAllocatedAmount(contributionAmount); // Set allocated amount to initial contribution
                goalAccount.setCurrentContribution(contributionAmount);
                goalAccount.setGoalAccountBalance(contributionAmount);
                goalAccount.setCreatedAt(LocalDateTime.now());
                goalAccount.setUpdatedAt(LocalDateTime.now());

                // Deduct from account balance if contribution > 0
                if (contributionAmount > 0) {
                    account.setBalance(account.getBalance() - contributionAmount);
                    accountRepository.save(account);
                }

                goalAccounts.add(goalAccountRepository.save(goalAccount));
            }
        }

        // Clear goal cache
        clearGoalRelatedCache(userId);

        return goalAccounts;
    }

    private void validateContributeRequest(GoalDto.ContributeToGoalRequest request) {
        if (request.getGoalId() == null) {
            throw new InvalidRequestException("Goal ID is required");
        }
        
        if (request.getAccountId() == null) {
            throw new InvalidRequestException("Account ID is required");
        }
        
        if (request.getAmount() == null || request.getAmount() <= 0) {
            throw new InvalidRequestException("Contribution amount must be positive");
        }
    }

    private void validateGoalStatus(Goal goal) {
        if (goal.getStatus() == Goal.GoalStatus.COMPLETED) {
            throw new InvalidRequestException("Cannot contribute to a completed goal");
        }
        
        if (goal.getStatus() == Goal.GoalStatus.FAILED) {
            throw new InvalidRequestException("Cannot contribute to a failed goal");
        }
    }

    private void processContribution(Goal goal, Accounts account, Double amount) {
        // Find the goal account for this account and goal
        GoalAccount goalAccount = goalAccountRepository.findByGoalAndAccount(goal, account);
        
        // Update account balance
        account.setBalance(account.getBalance() - amount);
        accountRepository.save(account);
        
        // Update goal current amount
        goal.setCurrentAmount(goal.getCurrentAmount() + amount);
        
        // Check if goal is completed
        if (goal.getCurrentAmount() >= goal.getGoalAmount()) {
            goal.setStatus(Goal.GoalStatus.COMPLETED);
        }
        
        goal.setUpdatedAt(LocalDateTime.now());
        goalRepository.save(goal);
        
        // Update goal account if it exists
        if (goalAccount != null) {
            goalAccount.setCurrentContribution(goalAccount.getCurrentContribution() + amount);
            goalAccount.setGoalAccountBalance(goalAccount.getGoalAccountBalance() + amount);
            goalAccount.setUpdatedAt(LocalDateTime.now());
            goalAccountRepository.save(goalAccount);
        }
    }

    private void returnFundsToOriginalAccounts(List<GoalAccount> goalAccounts) {
        for (GoalAccount ga : goalAccounts) {
            if (ga.getGoalAccountBalance() > 0) {
                // Return the goal account balance to the original account
                Accounts account = ga.getAccount();
                double balanceToReturn = ga.getGoalAccountBalance();
                account.setBalance(account.getBalance() + balanceToReturn);
                accountRepository.save(account);
                
                // Log the transaction for audit purposes (optional)
                System.out.println(String.format("Returned %.2f to account '%s' (ID: %d)", 
                                balanceToReturn, account.getAccountName(), account.getId()));
            }
        }
    }

    private void validateGoalRequest(GoalDto.CreateGoalRequest request) {
        // Validate required fields
        if (request.getGoalName() == null || request.getGoalName().trim().isEmpty()) {
            throw new InvalidRequestException("Goal name is required");
        }
        
        if (request.getGoalAmount() == null || request.getGoalAmount() <= 0) {
            throw new InvalidRequestException("Goal amount must be positive");
        }
        
        if (request.getStartDate() == null) {
            throw new InvalidRequestException("Start date is required");
        }
        
        if (request.getTargetDate() == null) {
            throw new InvalidRequestException("Target date is required");
        }
        
        if (request.getTargetDate().isBefore(request.getStartDate())) {
            throw new InvalidRequestException("Target date must be after start date");
        }
        
        if (request.getGoalType() == null) {
            throw new InvalidRequestException("Goal type is required");
        }
    }

    private GoalDto.GoalResponse mapToGoalResponse(Goal goal, List<GoalAccount> goalAccounts) {
        // Calculate progress percentage
        double progressPercentage = 0.0;
        if (goal.getGoalAmount() > 0) {
            progressPercentage = (goal.getCurrentAmount() / goal.getGoalAmount()) * 100;
        }
        
        // Format time remaining
        String timeRemaining = calculateTimeRemaining(goal.getTargetDate());
        
        // Map account responses
        List<GoalDto.GoalAccountResponse> accountResponses = goalAccounts.stream()
                .map(ga -> GoalDto.GoalAccountResponse.builder()
                        .accountId(ga.getAccount().getId())
                        .accountName(ga.getAccount().getAccountName())
                        .allocationPercentage(ga.getAllocationPercentage())
                        .allocatedAmount(ga.getAllocatedAmount())
                        .currentContribution(ga.getCurrentContribution())
                        .goalAccountBalance(ga.getGoalAccountBalance())
                        .progressPercentage(ga.getProgressPercentage())
                        .build())
                .collect(Collectors.toList());
        
        // Build response
        return GoalDto.GoalResponse.builder()
                .id(goal.getId())
                .goalName(goal.getGoalName())
                .goalAmount(goal.getGoalAmount())
                .currentAmount(goal.getCurrentAmount())
                .progressPercentage(progressPercentage)
                .startDate(goal.getStartDate())
                .targetDate(goal.getTargetDate())
                .status(goal.getStatus())
                .goalType(goal.getGoalType())
                .description(goal.getDescription())
                .accounts(accountResponses)
                .timeRemaining(timeRemaining)
                .isCompletedThisMonth(isCompletedThisMonth(goal))
                .build();
    }

    private String calculateTimeRemaining(LocalDate targetDate) {
        LocalDate today = LocalDate.now();
        
        if (today.isAfter(targetDate)) {
            return "Overdue";
        }
        
        Period period = Period.between(today, targetDate);
        int years = period.getYears();
        int months = period.getMonths();
        int days = period.getDays();
        
        StringBuilder sb = new StringBuilder();
        
        if (years > 0) {
            sb.append(years).append(years == 1 ? " year" : " years");
            if (months > 0 || days > 0) {
                sb.append(", ");
            }
        }
        
        if (months > 0) {
            sb.append(months).append(months == 1 ? " month" : " months");
            if (days > 0) {
                sb.append(", ");
            }
        }
        
        if (days > 0 || (years == 0 && months == 0)) {
            sb.append(days).append(days == 1 ? " day" : " days");
        }
        
        return sb.toString();
    }

    private boolean isCompletedThisMonth(Goal goal) {
        if (goal.getStatus() != Goal.GoalStatus.COMPLETED || goal.getUpdatedAt() == null) {
            return false;
        }
        
        LocalDate now = LocalDate.now();
        return goal.getUpdatedAt().getMonth().getValue() == now.getMonth().getValue() && 
               goal.getUpdatedAt().getYear() == now.getYear();
    }

    // Additional methods for existing functionality
    @Cacheable(value = "goalCache",
               key = "#root.methodName + '_' + #userId + '_' + #goalId",
               unless = "#result == null")
    @Transactional(readOnly = true)
    public GoalDto.GoalResponse getGoal(int userId, int goalId) {
        // Validate user
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        // Get goal
        Goal goal = goalRepository.findById(goalId)
                .orElseThrow(() -> new ResourceNotFoundException("Goal not found with id: " + goalId));

        // Check if goal belongs to user
        if (goal.getUser().getId() != userId) {
            throw new ResourceNotFoundException("Goal not found with id: " + goalId);
        }

        // Get goal accounts
        List<GoalAccount> goalAccounts = goalAccountRepository.findByGoal(goal);

        return mapToGoalResponse(goal, goalAccounts);
    }

    @Cacheable(value = "goalCache",
               key = "#root.methodName + '_' + #userId",
               unless = "#result == null")
    @Transactional(readOnly = true)
    public List<GoalDto.GoalResponse> getAllGoals(int userId) {
        // Validate user
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        // Get all goals for user
        List<Goal> goals = goalRepository.findByUser(user);

        return goals.stream()
                .map(goal -> {
                    List<GoalAccount> goalAccounts = goalAccountRepository.findByGoal(goal);
                    return mapToGoalResponse(goal, goalAccounts);
                })
                .collect(Collectors.toList());
    }

    @Cacheable(value = "goalCache",
               key = "#root.methodName + '_' + #userId",
               unless = "#result == null")
    @Transactional(readOnly = true)
    public GoalDto.GoalSummaryResponse getGoalsSummary(int userId) {
        // Validate user
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        // Get goal counts
        int totalGoals = goalRepository.findByUser(user).size();
        int completedGoals = goalRepository.countByUserAndStatus(user, Goal.GoalStatus.COMPLETED);
        int failedGoals = goalRepository.countByUserAndStatus(user, Goal.GoalStatus.FAILED);
        int inProgressGoals = goalRepository.countByUserAndStatus(user, Goal.GoalStatus.IN_PROGRESS);

        // Get upcoming goals (targets within next 3 months)
        LocalDate today = LocalDate.now();
        LocalDate threeMonthsLater = today.plusMonths(3);
        
        List<Goal> upcomingGoals = goalRepository.findByUserAndStatus(user, Goal.GoalStatus.IN_PROGRESS).stream()
                .filter(goal -> !goal.getTargetDate().isBefore(today) && !goal.getTargetDate().isAfter(threeMonthsLater))
                .sorted((g1, g2) -> g1.getTargetDate().compareTo(g2.getTargetDate()))
                .limit(5)
                .collect(Collectors.toList());

        List<GoalDto.GoalResponse> upcomingGoalResponses = upcomingGoals.stream()
                .map(goal -> mapToGoalResponse(goal, goalAccountRepository.findByGoal(goal)))
                .collect(Collectors.toList());

        return GoalDto.GoalSummaryResponse.builder()
                .totalGoals(totalGoals)
                .completedGoals(completedGoals)
                .inProgressGoals(inProgressGoals)
                .failedGoals(failedGoals)
                .upcomingGoals(upcomingGoalResponses)
                .build();
    }
    
    @Cacheable(value = "goalCache",
               key = "#root.methodName + '_' + #userId + '_' + #year + '_' + #month",
               unless = "#result == null")
    @Transactional(readOnly = true)
    public List<GoalDto.GoalResponse> getGoalsForMonth(int userId, int year, int month) {
        // Validate user
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        // Get goals for specific month
        YearMonth yearMonth = YearMonth.of(year, month);
        LocalDate startOfMonth = yearMonth.atDay(1);
        LocalDate endOfMonth = yearMonth.atEndOfMonth();
        
        List<Goal> goals = goalRepository.findByUser(user).stream()
                .filter(goal -> {
                    LocalDate targetDate = goal.getTargetDate();
                    return !targetDate.isBefore(startOfMonth) && !targetDate.isAfter(endOfMonth);
                })
                .collect(Collectors.toList());

        return goals.stream()
                .map(goal -> {
                    List<GoalAccount> goalAccounts = goalAccountRepository.findByGoal(goal);
                    return mapToGoalResponse(goal, goalAccounts);
                })
                .collect(Collectors.toList());
    }

    public void clearGoalRelatedCache(Integer userId) {
        clearCacheByPatterns("goalCache",
            "getGoalAccountBalance_" + userId + "_",
            "getAccountGoalSummary_" + userId + "_",
            "getGoal_" + userId + "_",
            "getAllGoals_" + userId,
            "getGoalsSummary_" + userId,
            "getGoalsForMonth_" + userId + "_"
        );
    }

    private void clearCacheByPatterns(String cacheName, String... patterns) {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
            if (cache != null && cache instanceof CaffeineCache caffeineCache) {
                com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache =
                    (com.github.benmanes.caffeine.cache.Cache<Object, Object>) caffeineCache.getNativeCache();
    
                // Remove all cache entries that match any of the provided patterns
                nativeCache.asMap().keySet().removeIf(key -> {
                    if (key instanceof String keyStr) {
                        for (String pattern : patterns) {
                            if (keyStr.startsWith(pattern)) {
                                return true;
                            }
                        }
                    }
                    return false;
                });
    
                log.info("Cleared cache entries matching patterns: {} from cache: {}",
                    java.util.Arrays.toString(patterns), cacheName);
            } else {
                log.warn("Cache '{}' not found or not a CaffeineCache instance", cacheName);
            }
        } catch (Exception e) {
            log.error("Error clearing cache '{}' with patterns: {}",
                cacheName, java.util.Arrays.toString(patterns), e);
        }
    }
}