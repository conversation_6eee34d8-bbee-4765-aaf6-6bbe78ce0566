package com.pennypal.fintech.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.pennypal.fintech.entity.Accounts;
import com.pennypal.fintech.repository.AccountRepository;
import com.pennypal.fintech.service.AccountBalanceService;
import com.pennypal.fintech.service.TransactionService;

import java.util.Optional;

/**
 * Aspect for automatically clearing caches after transaction synchronization operations.
 * This ensures cache consistency when transaction data is updated.
 */
@Aspect
@Component
@Slf4j
public class CacheClearingAspect {

    @Autowired
    private AccountBalanceService accountBalanceService;

    @Autowired
    private TransactionService transactionService;

    @Autowired
    private AccountRepository accountRepository;

    /**
     * Intercepts calls to syncTransactionsForAccount method in PlaidService
     * and automatically clears relevant caches after successful execution.
     * 
     * @param joinPoint The join point representing the method execution
     * @param result The return value from the method (number of transactions processed)
     */
    @AfterReturning(
        pointcut = "execution(* com.pennypal.fintech.service.PlaidService.syncTransactionsForAccount(int))",
        returning = "result"
    )
    public void clearCachesAfterPlaidTransactionSync(JoinPoint joinPoint, Object result) {
        try {
            // Extract accountId from method arguments
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] instanceof Integer) {
                Integer accountId = (Integer) args[0];
                
                // Get the user ID from the account
                Optional<Accounts> accountOpt = accountRepository.findById(accountId);
                if (accountOpt.isPresent()) {
                    Integer userId = accountOpt.get().getUser().getId();
                    int transactionsProcessed = (result instanceof Integer) ? (Integer) result : 0;
                    
                    log.info("AOP: Clearing caches after syncTransactionsForAccount - AccountId: {}, UserId: {}, TransactionsProcessed: {}", 
                             accountId, userId, transactionsProcessed);
                    
                    // Clear account balance cache
                    accountBalanceService.clearAccountBalanceCache(userId);
                    log.debug("AOP: Cleared account balance cache for user: {}", userId);
                    
                    // Clear transaction related cache
                    transactionService.clearTransactionRelatedCache(userId);
                    log.debug("AOP: Cleared transaction cache for user: {}", userId);
                    
                    log.info("AOP: Successfully cleared all caches for user: {} after processing {} transactions", 
                             userId, transactionsProcessed);
                } else {
                    log.warn("AOP: Account not found with ID: {} - unable to clear caches", accountId);
                }
            } else {
                log.warn("AOP: Invalid arguments for syncTransactionsForAccount method - expected Integer accountId");
            }
        } catch (Exception e) {
            log.error("AOP: Error clearing caches after syncTransactionsForAccount: {}", e.getMessage(), e);
            // Don't rethrow the exception to avoid affecting the main business logic
        }
    }

    /**
     * Intercepts calls to syncTransactionsForAccount method in MxService
     * and automatically clears relevant caches after successful execution.
     * 
     * @param joinPoint The join point representing the method execution
     * @param result The return value from the method (number of transactions processed)
     */
    @AfterReturning(
        pointcut = "execution(* com.pennypal.fintech.service.MxService.syncTransactionsForAccount(Integer))",
        returning = "result"
    )
    public void clearCachesAfterMxTransactionSync(JoinPoint joinPoint, Object result) {
        try {
            // Extract accountId from method arguments
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] instanceof Integer) {
                Integer accountId = (Integer) args[0];
                
                // Get the user ID from the account
                Optional<Accounts> accountOpt = accountRepository.findById(accountId);
                if (accountOpt.isPresent()) {
                    Integer userId = accountOpt.get().getUser().getId();
                    int transactionsProcessed = (result instanceof Integer) ? (Integer) result : 0;
                    
                    log.info("AOP: Clearing caches after MX syncTransactionsForAccount - AccountId: {}, UserId: {}, TransactionsProcessed: {}", 
                             accountId, userId, transactionsProcessed);
                    
                    // Clear account balance cache
                    accountBalanceService.clearAccountBalanceCache(userId);
                    log.debug("AOP: Cleared account balance cache for user: {}", userId);
                    
                    // Clear transaction related cache
                    transactionService.clearTransactionRelatedCache(userId);
                    log.debug("AOP: Cleared transaction cache for user: {}", userId);
                    
                    log.info("AOP: Successfully cleared all caches for user: {} after processing {} transactions", 
                             userId, transactionsProcessed);
                } else {
                    log.warn("AOP: Account not found with ID: {} - unable to clear caches", accountId);
                }
            } else {
                log.warn("AOP: Invalid arguments for MX syncTransactionsForAccount method - expected Integer accountId");
            }
        } catch (Exception e) {
            log.error("AOP: Error clearing caches after MX syncTransactionsForAccount: {}", e.getMessage(), e);
            // Don't rethrow the exception to avoid affecting the main business logic
        }
    }

    /**
     * Intercepts calls to syncTransactionsForAccount method in FinicityServicev1
     * and automatically clears relevant caches after successful execution.
     * 
     * @param joinPoint The join point representing the method execution
     * @param result The return value from the method (number of transactions processed)
     */
    @AfterReturning(
        pointcut = "execution(* com.pennypal.fintech.service.FinicityServicev1.syncTransactionsForAccount(Integer))",
        returning = "result"
    )
    public void clearCachesAfterFinicityTransactionSync(JoinPoint joinPoint, Object result) {
        try {
            // Extract accountId from method arguments
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] instanceof Integer) {
                Integer accountId = (Integer) args[0];
                
                // Get the user ID from the account
                Optional<Accounts> accountOpt = accountRepository.findById(accountId);
                if (accountOpt.isPresent()) {
                    Integer userId = accountOpt.get().getUser().getId();
                    int transactionsProcessed = (result instanceof Integer) ? (Integer) result : 0;
                    
                    log.info("AOP: Clearing caches after Finicity syncTransactionsForAccount - AccountId: {}, UserId: {}, TransactionsProcessed: {}", 
                             accountId, userId, transactionsProcessed);
                    
                    // Clear account balance cache
                    accountBalanceService.clearAccountBalanceCache(userId);
                    log.debug("AOP: Cleared account balance cache for user: {}", userId);
                    
                    // Clear transaction related cache
                    transactionService.clearTransactionRelatedCache(userId);
                    log.debug("AOP: Cleared transaction cache for user: {}", userId);
                    
                    log.info("AOP: Successfully cleared all caches for user: {} after processing {} transactions", 
                             userId, transactionsProcessed);
                } else {
                    log.warn("AOP: Account not found with ID: {} - unable to clear caches", accountId);
                }
            } else {
                log.warn("AOP: Invalid arguments for Finicity syncTransactionsForAccount method - expected Integer accountId");
            }
        } catch (Exception e) {
            log.error("AOP: Error clearing caches after Finicity syncTransactionsForAccount: {}", e.getMessage(), e);
            // Don't rethrow the exception to avoid affecting the main business logic
        }
    }
}