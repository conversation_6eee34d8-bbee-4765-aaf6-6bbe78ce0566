package com.pennypal.fintech.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pennypal.fintech.entity.Budget;
import com.pennypal.fintech.entity.Reconcile;
import com.pennypal.fintech.entity.SubCategory;
import com.pennypal.fintech.entity.Transactions;
import com.pennypal.fintech.repository.BudgetRepository;
import com.pennypal.fintech.repository.ReconcileRepository;
import com.pennypal.fintech.repository.SubCategoryRepository;
import com.pennypal.fintech.repository.TransactionRepository;
import com.pennypal.fintech.repository.UserRepository;

import jakarta.transaction.Transactional;

@Service
public class ReconcileService {
    private static final Logger log = LoggerFactory.getLogger(ReconcileService.class);

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private ReconcileRepository reconcileRepository;

    @Autowired 
    private BudgetRepository budgetRepository;

    @Autowired
    private SubCategoryRepository subCategoryRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private TransactionService transactionService;

    @Autowired
    private BudgetService budgetService;

    @Transactional
    public void reconcileTransaction(Transactions transaction) {
        if (transaction.getDescription() == null || transaction.getDescription().trim().isEmpty()) {
            log.warn("Skipping reconciliation: description is null or empty for transactionId: {}", transaction.getId());
            return;
        }

        String description = transaction.getDescription().trim();
        Integer accountId = transaction.getAccount().getId();

        log.info("Querying for matches with description: '{}', accountId: {}", description, accountId);

        List<Transactions> potentialMatches = transactionRepository
                .findByDescriptionAndAccountIdAndRemovedFalse(description, accountId);

        log.info("Found {} potential matches", potentialMatches.size());

        for (Transactions existingTx : potentialMatches) {
            if (existingTx.getId() == transaction.getId()) {
                log.debug("Skipping same transaction ID: {}", existingTx.getId());
                continue;
            }

            if ("yes".equalsIgnoreCase(existingTx.getReconcileFlag())) {
                log.debug("Skipping already reconciled transaction ID: {}", existingTx.getId());
                continue;
            }

            double sum = transaction.getTransactionAmount() + existingTx.getTransactionAmount();
            boolean isOppositeAmount = Math.abs(sum) < 0.01;

            log.info("Comparing amounts: {} + {} = {}, isOpposite: {}", 
                     transaction.getTransactionAmount(), 
                     existingTx.getTransactionAmount(), 
                     sum, isOppositeAmount);

            if (isOppositeAmount) {
                String reconcileId = UUID.randomUUID().toString();

                transaction.setReconcileFlag("D");
                transaction.setReconcileId(reconcileId);
                transactionRepository.save(transaction);

                existingTx.setReconcileFlag("D");
                existingTx.setReconcileId(reconcileId);
                transactionRepository.save(existingTx);

                Reconcile r1 = new Reconcile();
                r1.setTransactionDate(transaction.getTransactionDate());
                r1.setDescription(description);
                r1.setCategory(transaction.getCategory());
                r1.setAmount(transaction.getTransactionAmount());
                r1.setReconcileId(reconcileId);
                r1.setReconcileFlag("D");
                r1.setAccountName(transaction.getAccount().getAccountName());
                r1.setTransactionId(String.valueOf(transaction.getId())); // Set transactionId
                reconcileRepository.save(r1);

                Reconcile r2 = new Reconcile();
                r2.setTransactionDate(existingTx.getTransactionDate());
                r2.setDescription(description);
                r2.setCategory(existingTx.getCategory());
                r2.setAmount(existingTx.getTransactionAmount());
                r2.setReconcileId(reconcileId);
                r2.setReconcileFlag("D");
                r2.setAccountName(existingTx.getAccount().getAccountName());
                r2.setTransactionId(String.valueOf(existingTx.getId())); // Set transactionId
                reconcileRepository.save(r2);

                log.info("Reconciled transaction pair: {} <--> {}, reconcileId={}", 
                         transaction.getId(), existingTx.getId(), reconcileId);
                break;
            }
        }
    }

    @Transactional(rollbackOn = Exception.class)
    public void confirmReconciliation(List<Integer> txnIds) {
        if (txnIds == null || txnIds.isEmpty()) {
            log.warn("No transaction IDs provided for reconciliation");
            throw new IllegalArgumentException("Transaction ID list is empty or null");
        }

        log.info("Processing reconciliation for transaction IDs: {}", txnIds);
        StringBuilder errorMessages = new StringBuilder();
        boolean anyProcessed = false;

        for (Integer id : txnIds) {
            try {
                log.info("Processing transaction ID: {}", id);

                // Find transaction
                Optional<Transactions> txnOpt = transactionRepository.findById(id);
                if (!txnOpt.isPresent()) {
                    log.warn("Transaction not found: {}", id);
                    errorMessages.append("Transaction ID ").append(id).append(" not found; ");
                    continue;
                }

                Transactions txn = txnOpt.get();
                log.debug("Transaction {}: reconcileFlag={}, reconcileId={}, amount={}, description={}, subCategoryId={}, accountId={}",
                        id, txn.getReconcileFlag(), txn.getReconcileId(), 
                        txn.getTransactionAmount(), txn.getDescription(), 
                        txn.getSubCategoryId(), txn.getAccount() != null ? txn.getAccount().getId() : null);

                // Check reconcileFlag
                if (!"D".equals(txn.getReconcileFlag())) {
                    log.warn("Skipping transaction ID {}: reconcileFlag is '{}', expected 'D'", 
                             id, txn.getReconcileFlag() != null ? txn.getReconcileFlag() : "null");
                    errorMessages.append("Transaction ID ").append(id)
                                 .append(" has invalid reconcileFlag '")
                                 .append(txn.getReconcileFlag() != null ? txn.getReconcileFlag() : "null")
                                 .append("'; ");
                    continue;
                }

                // Update transaction
                txn.setReconcileFlag("yes");
                Transactions savedTxn = transactionRepository.save(txn);
                log.info("Updated transaction ID {}: reconcileFlag set to '{}'", id, savedTxn.getReconcileFlag());

                // Clear transaction related cache
                transactionService.clearTransactionRelatedCache(txn.getUser().getId());

                // Update Reconcile table
                Optional<Reconcile> recOpt = reconcileRepository.findByTransactionIdAndReconcileIdAndAmountAndDescription(
                    String.valueOf(id), txn.getReconcileId(), txn.getTransactionAmount(), txn.getDescription());
                if (recOpt.isPresent()) {
                    Reconcile rec = recOpt.get();
                    rec.setReconcileFlag("yes");
                    rec.setExcludeFromBudget(true); // Exclude from budget
                    Reconcile savedRec = reconcileRepository.save(rec);
                    log.info("Updated Reconcile record for transaction ID {}: reconcileId={}, reconcileFlag={}, excludeFromBudget={}", 
                             id, savedRec.getReconcileId(), savedRec.getReconcileFlag(), savedRec.getExcludeFromBudget());
                } else {
                    log.warn("No Reconcile record found for transaction ID {}: reconcileId={}, amount={}, description={}",
                            id, txn.getReconcileId(), txn.getTransactionAmount(), txn.getDescription());
                    errorMessages.append("No Reconcile record for transaction ID ").append(id).append("; ");
                }

                // Update budget
                reverseBudgetImpact(txn);
                log.info("Completed reconciliation for transaction ID {}", id);
                anyProcessed = true;
            } catch (Exception e) {
                log.error("Failed to reconcile transaction ID {}: {}", id, e.getMessage(), e);
                errorMessages.append("Failed to process transaction ID ").append(id)
                             .append(": ").append(e.getMessage()).append("; ");
            }
        }

        if (!anyProcessed && errorMessages.length() > 0) {
            log.error("No transactions were processed: {}", errorMessages);
            throw new RuntimeException("No valid transactions with reconcileFlag 'D' were processed: " + errorMessages);
        } else if (errorMessages.length() > 0) {
            log.warn("Some transactions failed to process: {}", errorMessages);
            // Allow partial success, but log warnings
        }
    }

    private void reverseBudgetImpact(Transactions txn) {
        try {
            BigDecimal amt = BigDecimal.valueOf(txn.getTransactionAmount());
            if (amt.compareTo(BigDecimal.ZERO) < 0) {
                amt = amt.negate();
            }
            log.debug("Transaction ID {}: Reversing budget impact for amount={}", 
                     txn.getId(), amt);

            // Find SubCategory
            Integer subCategoryId = txn.getSubCategoryId();
            if (subCategoryId == null) {
                log.warn("SubCategoryId is null for transaction ID {}", txn.getId());
                return;
            }

            Optional<SubCategory> scOpt = subCategoryRepository.findById(subCategoryId);
            if (!scOpt.isPresent()) {
                log.warn("SubCategory not found for transaction ID {}: subCategoryId={}", 
                         txn.getId(), subCategoryId);
                return;
            }
            SubCategory sc = scOpt.get();

            // Find Budget
            Integer userId = txn.getAccount() != null && txn.getAccount().getUser() != null 
                           ? txn.getAccount().getUser().getId() : null;
            Integer categoryId = sc.getCategory() != null ? sc.getCategory().getId() : null;
            if (userId == null || categoryId == null) {
                log.warn("Invalid userId={} or categoryId={} for transaction ID {}", 
                         userId, categoryId, txn.getId());
                return;
            }

            Optional<Budget> ob = budgetRepository.findLatestBudgetByCategoryIdAndSubCategoryId(
                userId, categoryId, subCategoryId, txn.getTransactionDate().toLocalDate());

            if (ob.isPresent()) {
                Budget b = ob.get();
                log.debug("Found budget for transaction ID {}: budgetId={}, actual={}, allocated={}",
                         txn.getId(), b.getId(), b.getActual(), b.getAllocated());

                // Update budget
                BigDecimal newActual = b.getActual().subtract(amt);
                b.setActual(newActual);
                b.setRemaining(b.getAllocated().subtract(newActual));
                Budget savedBudget = budgetRepository.save(b);
                log.info("Updated budget for transaction ID {}: budgetId={}, actual={}, remaining={}",
                        txn.getId(), savedBudget.getId(), savedBudget.getActual(), savedBudget.getRemaining());

                // Clear budget related cache
                budgetService.clearBudgetRelatedCache(userId, txn.getTransactionDate().getYear(), txn.getTransactionDate().getMonthValue());

            } else {
                log.warn("No budget found for transaction ID {}: userId={}, categoryId={}, subCategoryId={}, date={}",
                        txn.getId(), userId, categoryId, subCategoryId, 
                        txn.getTransactionDate().toLocalDate());
            }
        } catch (Exception e) {
            log.error("Failed to reverse budget impact for transaction ID {}: {}", 
                     txn.getId(), e.getMessage(), e);
            throw new RuntimeException("Failed to reverse budget for transaction ID " + 
                     txn.getId() + ": " + e.getMessage(), e);
        }
    }

    public List<Reconcile> getReconciledTransactions() {
        log.info("Fetching reconciled transactions with reconcileFlag='yes'");
        return reconcileRepository.findByReconcileFlag("yes");
    }
}