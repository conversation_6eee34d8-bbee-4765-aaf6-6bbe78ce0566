package com.pennypal.fintech.api.config;

import java.io.File;
import java.nio.file.Paths;
import java.nio.file.Path;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {
    
        @Value("${file.upload-dir}")
        private String uploadDir;

        @Override
        public void addCorsMappings(CorsRegistry registry) {
                registry.addMapping("/**")
                        .allowedOrigins("http://localhost:5173")  // Allow requests from your frontend URL
                        .allowedMethods("GET", "POST", "PUT", "DELETE")  // Allow the HTTP methods that you need
                        .allowedHeaders("*")  // Allow any headers
                        .allowCredentials(true)  // Allow credentials if necessary
                        .exposedHeaders("Cache-Control", "Content-Language", "Content-Type", "Expires", "Last-Modified", "Pragma"); // Expose headers for SSE
        }

        @Override
        public void addResourceHandlers(ResourceHandlerRegistry registry) {
                Path uploadPath = Paths.get(uploadDir).toAbsolutePath();
                String uploadUri = "file:///" + uploadPath.toString().replace("\\", "/") + "/";

                registry.addResourceHandler("/uploads/**")
                        .addResourceLocations(uploadUri);
                
                registry.addResourceHandler("/uploads/scanned-copy/**")
                        .addResourceLocations(uploadUri + "scanned-copy/");
        }
}