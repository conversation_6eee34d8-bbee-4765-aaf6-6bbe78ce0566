package com.pennypal.fintech.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Column;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ForeignKey;
import java.time.LocalDateTime;

@Entity
@Table(name = "transactions")
@Data
public class Transactions {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @Column(name = "transaction_date")
    private LocalDateTime transactionDate;

    // private String transactionId;

    @Column(name = "insert_Datetime")
    private LocalDateTime insertDateTime;

    @Column(name = "update_Datetime")
    private LocalDateTime updateDateTime;

    @Column(name = "description")
    private String description;

    @Column(name = "transaction_amount")
    private Double transactionAmount;

    @Column(name = "category")
    private String category;

    @Column(name = "transaction_type")
    private String transactionType;

    @Column(name = "transaction_id")
    private String transactionId;
    @Column(name = "merchant_name")
    private String merchantName;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "account_id", foreignKey = @ForeignKey(name = "transaction_ibfk_1"))
    private Accounts account;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", foreignKey = @ForeignKey(name = "transaction_ibfk_2"))
    private Users user;
    
    @Column(name = "reconcile_flag")
    private String reconcileFlag;

    @Column(name = "reconcile_id")
    private String reconcileId;

    @Column(name = "removed")
    private Boolean removed = false; // default to false

    @Column(name = "is_remove", columnDefinition = "BOOLEAN DEFAULT false")
    private boolean remove = false;

    @Column(name = "tax")
    private Double tax;

    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;

     @Column(name = "tag")
    private String tag;


    @Column(name = "sub_category_id")
    private Integer subCategoryId;

    
    @Column(name = "category_id") // New column
    private Integer categoryId;
    
    @Column(name = "hide_from_budget", columnDefinition = "BOOLEAN DEFAULT false")
    private boolean hideFromBudget = false;

    @Column(name = "custom_sub_category_id")
    private Integer customSubCategoryId;

      @Column(name = "exclude_from_budget", columnDefinition = "BOOLEAN DEFAULT false")
    private boolean excludeFromBudget = false;

    public Integer getCustomSubCategoryId() {
        return customSubCategoryId;
    }

    public void setCustomSubCategoryId(Integer customSubCategoryId) {
        this.customSubCategoryId = customSubCategoryId;
    }

    public String getReconcileFlag() {
        return reconcileFlag;
    }

    public void setReconcileFlag(String reconcileFlag) {
        this.reconcileFlag = reconcileFlag;
    }

     public String getReconcileId() {
        return reconcileId;
    }
    
    public void setReconcileId(String reconcileId) {
        this.reconcileId = reconcileId;
    }

    public Boolean getRemoved() {
        return removed;
    }

    public void setRemoved(Boolean removed) {
        this.removed = removed;
    }

    public boolean isRemove() {
        return remove;
    }

    public void setisRemove(boolean remove) {
        this.remove = remove;
    }
    public Double getTax() {
    return tax;
}

public void setTax(Double tax) {
    this.tax = tax;
}

public String getNotes() {
    return notes;
}

public void setNotes(String notes) {
    this.notes = notes;
}

public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Integer getSubCategoryId() {
        return subCategoryId;
    }

    public void setSubCategoryId(Integer subCategoryId) {
        this.subCategoryId = subCategoryId;
    }

      public Integer getCategoryId() { // New getter
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) { // New setter
        this.categoryId = categoryId;
    }

     public boolean isHideFromBudget() {
    return hideFromBudget;
}

public void setHideFromBudget(boolean hideFromBudget) {
    this.hideFromBudget = hideFromBudget;
}

 public boolean isExcludeFromBudget() {
        return excludeFromBudget;
    }

    public void setExcludeFromBudget(boolean excludeFromBudget) {
        this.excludeFromBudget = excludeFromBudget;
    }
    
    @Override
    public String toString() {
        return "Transaction{" +
                "id=" + id +
                ", transactionDate=" + transactionDate +
                ", insertDateTime=" + insertDateTime +
                ", updateDateTime=" + updateDateTime +
                ", description='" + description + '\'' +
                ", transactionAmount=" + transactionAmount +
                ",merchantName=" + merchantName + '\'' +
                // ", amount=" + amount +
                ", category='" + category + '\'' +
                ", accountId=" + (account != null ? account.getId() : null) +
                ", userId=" + (user != null ? user.getId() : null) +
               ", excludeFromBudget=" + excludeFromBudget +

                '}';
    }

    // public void setRemoved(boolean b) {
    // // TODO Auto-generated method stub
    // throw new UnsupportedOperationException("Unimplemented method 'setRemoved'");
    // }
}